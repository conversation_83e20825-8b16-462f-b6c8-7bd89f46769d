'use client';

import React, { useState } from 'react';
import { 
  Heart, 
  Calendar, 
  FileText, 
  Bell, 
  User, 
  Settings,
  PlusCircle,
  Clock,
  CheckCircle,
  AlertCircle,
  Star,
  MessageCircle,
  Camera,
  Award
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge,
  Navigation,
  Footer,
  PetCard
} from '@/components';
import { useAuth } from '@/contexts/AuthContext';
import { Pet } from '@/components/pets/PetCard';

interface Application {
  id: string;
  petId: string;
  petName: string;
  petImage: string;
  status: 'pending' | 'approved' | 'rejected' | 'interview' | 'home-visit';
  submittedDate: string;
  lastUpdate: string;
  nextStep?: string;
}

interface Appointment {
  id: string;
  type: 'meet-greet' | 'home-visit' | 'interview' | 'pickup';
  petName: string;
  date: string;
  time: string;
  location: string;
  status: 'scheduled' | 'completed' | 'cancelled';
}

const sampleApplications: Application[] = [
  {
    id: '1',
    petId: '1',
    petName: 'Buddy',
    petImage: '/images/pets/buddy.jpg',
    status: 'approved',
    submittedDate: '2024-01-15',
    lastUpdate: '2024-01-20',
    nextStep: 'Schedule pickup appointment'
  },
  {
    id: '2',
    petId: '2',
    petName: 'Luna',
    petImage: '/images/pets/luna.jpg',
    status: 'interview',
    submittedDate: '2024-01-18',
    lastUpdate: '2024-01-22',
    nextStep: 'Phone interview scheduled for Jan 25'
  },
  {
    id: '3',
    petId: '3',
    petName: 'Charlie',
    petImage: '/images/pets/charlie.jpg',
    status: 'pending',
    submittedDate: '2024-01-20',
    lastUpdate: '2024-01-20',
    nextStep: 'Application under review'
  }
];

const sampleAppointments: Appointment[] = [
  {
    id: '1',
    type: 'pickup',
    petName: 'Buddy',
    date: '2024-01-25',
    time: '2:00 PM',
    location: 'Animal Heaven Main Shelter',
    status: 'scheduled'
  },
  {
    id: '2',
    type: 'interview',
    petName: 'Luna',
    date: '2024-01-25',
    time: '10:00 AM',
    location: 'Phone Interview',
    status: 'scheduled'
  }
];

const savedPets: Pet[] = [
  {
    id: '4',
    name: 'Max',
    type: 'dog',
    breed: 'Labrador Mix',
    age: { value: 2, unit: 'years' },
    gender: 'male',
    size: 'large',
    color: 'Brown',
    description: 'Friendly and energetic dog looking for an active family.',
    personality: ['friendly', 'energetic', 'playful'],
    images: [{ url: '/images/pets/max.jpg', alt: 'Max', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Happy Paws', city: 'Mumbai', state: 'Maharashtra' },
    adoptionFee: 4500,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: false },
    views: 156,
    likes: 78,
    featured: false
  }
];

const DashboardPage: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
        <Navigation isAuthenticated={false} savedPetsCount={0} />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <h1 className="text-2xl font-bold text-neutral-900 mb-4">Please log in to access your dashboard</h1>
          <Button variant="primary" onClick={() => window.location.href = '/login'}>
            Go to Login
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'interview': return 'text-blue-600 bg-blue-100';
      case 'home-visit': return 'text-purple-600 bg-purple-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'rejected': return <AlertCircle className="w-4 h-4" />;
      case 'interview': return <MessageCircle className="w-4 h-4" />;
      case 'home-visit': return <Calendar className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
      <Navigation isAuthenticated={true} savedPetsCount={savedPets.length} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="font-primary text-3xl font-bold text-neutral-900">
                Welcome back, {user.firstName}!
              </h1>
              <p className="text-neutral-600 mt-1">
                Track your adoption journey and manage your account
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" leftIcon={<Bell className="w-4 h-4" />}>
                Notifications
              </Button>
              <Button variant="outline" leftIcon={<Settings className="w-4 h-4" />}>
                Settings
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card variant="warm">
            <CardContent className="p-6 text-center">
              <FileText className="w-8 h-8 text-orange-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-neutral-900 mb-1">
                {sampleApplications.length}
              </div>
              <div className="text-sm text-neutral-600">Active Applications</div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent className="p-6 text-center">
              <Heart className="w-8 h-8 text-red-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-neutral-900 mb-1">
                {savedPets.length}
              </div>
              <div className="text-sm text-neutral-600">Saved Pets</div>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent className="p-6 text-center">
              <Calendar className="w-8 h-8 text-blue-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-neutral-900 mb-1">
                {sampleAppointments.length}
              </div>
              <div className="text-sm text-neutral-600">Upcoming Appointments</div>
            </CardContent>
          </Card>

          <Card variant="outlined">
            <CardContent className="p-6 text-center">
              <Award className="w-8 h-8 text-green-600 mx-auto mb-3" />
              <div className="text-2xl font-bold text-neutral-900 mb-1">0</div>
              <div className="text-sm text-neutral-600">Successful Adoptions</div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Applications & Appointments */}
          <div className="lg:col-span-2 space-y-8">
            {/* Recent Applications */}
            <Card variant="elevated">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Adoption Applications
                  </CardTitle>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sampleApplications.map((application) => (
                    <div key={application.id} className="flex items-center gap-4 p-4 border border-neutral-200 rounded-lg">
                      <div className="w-16 h-16 bg-gradient-to-br from-orange-200 to-green-200 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">🐕</span>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg">{application.petName}</h3>
                          <Badge className={getStatusColor(application.status)} size="sm">
                            <span className="flex items-center gap-1">
                              {getStatusIcon(application.status)}
                              {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                            </span>
                          </Badge>
                        </div>
                        <p className="text-neutral-600 text-sm mb-2">
                          Applied on {new Date(application.submittedDate).toLocaleDateString()}
                        </p>
                        {application.nextStep && (
                          <p className="text-blue-600 text-sm font-medium">
                            Next: {application.nextStep}
                          </p>
                        )}
                      </div>
                      
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Upcoming Appointments */}
            <Card variant="warm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Upcoming Appointments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sampleAppointments.map((appointment) => (
                    <div key={appointment.id} className="flex items-center gap-4 p-4 bg-white rounded-lg border border-orange-200">
                      <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <Calendar className="w-6 h-6 text-orange-600" />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1">
                          {appointment.type.split('-').map(word => 
                            word.charAt(0).toUpperCase() + word.slice(1)
                          ).join(' ')} - {appointment.petName}
                        </h3>
                        <p className="text-neutral-600 text-sm">
                          {new Date(appointment.date).toLocaleDateString()} at {appointment.time}
                        </p>
                        <p className="text-neutral-500 text-xs">
                          {appointment.location}
                        </p>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          Reschedule
                        </Button>
                        <Button variant="primary" size="sm">
                          Join
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Profile & Saved Pets */}
          <div className="space-y-8">
            {/* Profile Summary */}
            <Card variant="outlined">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Profile Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-orange-200 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl font-semibold text-neutral-700">
                      {user.firstName[0]}{user.lastName[0]}
                    </span>
                  </div>
                  <h3 className="font-semibold text-lg">{user.firstName} {user.lastName}</h3>
                  <p className="text-neutral-600 text-sm">{user.email}</p>
                  <Badge variant="primary" size="sm" className="mt-2">
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </Badge>
                </div>
                
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Member since:</span>
                    <span className="font-medium">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Profile completion:</span>
                    <span className="font-medium text-green-600">85%</span>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full mt-4">
                  Edit Profile
                </Button>
              </CardContent>
            </Card>

            {/* Saved Pets */}
            <Card variant="elevated">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="w-5 h-5" />
                    Saved Pets
                  </CardTitle>
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {savedPets.length > 0 ? (
                  <div className="space-y-4">
                    {savedPets.slice(0, 2).map((pet) => (
                      <div key={pet.id} className="border border-neutral-200 rounded-lg p-3">
                        <div className="flex items-center gap-3">
                          <div className="w-12 h-12 bg-gradient-to-br from-orange-200 to-green-200 rounded-lg flex items-center justify-center">
                            <span className="text-lg">🐕</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold">{pet.name}</h4>
                            <p className="text-neutral-600 text-sm">{pet.breed}</p>
                          </div>
                          <Button variant="ghost" size="sm">
                            <Heart className="w-4 h-4 text-red-500 fill-current" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Heart className="w-12 h-12 text-neutral-400 mx-auto mb-3" />
                    <p className="text-neutral-600 mb-4">No saved pets yet</p>
                    <Button variant="outline" size="sm">
                      Browse Pets
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card variant="warm">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="primary" className="w-full" leftIcon={<PlusCircle className="w-4 h-4" />}>
                    Start New Application
                  </Button>
                  <Button variant="outline" className="w-full" leftIcon={<Calendar className="w-4 h-4" />}>
                    Schedule Visit
                  </Button>
                  <Button variant="outline" className="w-full" leftIcon={<Camera className="w-4 h-4" />}>
                    Share Pet Photo
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default DashboardPage;
