'use client';

import React, { useState } from 'react';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  Send, 
  MessageCircle,
  Calendar,
  Users,
  Heart,
  AlertCircle,
  CheckCircle,
  Navigation as NavigationIcon
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Input,
  Navigation,
  Footer
} from '@/components';

interface ContactForm {
  name: string;
  email: string;
  phone: string;
  subject: string;
  category: string;
  message: string;
  urgent: boolean;
}

interface ContactInfo {
  type: 'main' | 'emergency' | 'volunteer' | 'adoption';
  title: string;
  description: string;
  phone: string;
  email: string;
  hours: string;
  icon: React.ReactNode;
  color: string;
}

const contactInfo: ContactInfo[] = [
  {
    type: 'main',
    title: 'General Information',
    description: 'Questions about our services, programs, or general inquiries',
    phone: '+91 98765 43210',
    email: '<EMAIL>',
    hours: 'Mon-Fri: 9AM-6PM, Sat-Sun: 10AM-4PM',
    icon: <MessageCircle className="w-6 h-6" />,
    color: 'bg-blue-100 text-blue-600'
  },
  {
    type: 'emergency',
    title: 'Emergency Rescue',
    description: 'Urgent animal rescue situations requiring immediate attention',
    phone: '+91 98765 43211',
    email: '<EMAIL>',
    hours: '24/7 Emergency Hotline',
    icon: <AlertCircle className="w-6 h-6" />,
    color: 'bg-red-100 text-red-600'
  },
  {
    type: 'adoption',
    title: 'Adoption Services',
    description: 'Questions about adoption process, pet availability, and applications',
    phone: '+91 98765 43212',
    email: '<EMAIL>',
    hours: 'Mon-Sun: 10AM-6PM',
    icon: <Heart className="w-6 h-6" />,
    color: 'bg-orange-100 text-orange-600'
  },
  {
    type: 'volunteer',
    title: 'Volunteer Program',
    description: 'Information about volunteering opportunities and applications',
    phone: '+91 98765 43213',
    email: '<EMAIL>',
    hours: 'Mon-Fri: 9AM-5PM',
    icon: <Users className="w-6 h-6" />,
    color: 'bg-green-100 text-green-600'
  }
];

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState<ContactForm>({
    name: '',
    email: '',
    phone: '',
    subject: '',
    category: '',
    message: '',
    urgent: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleInputChange = (field: keyof ContactForm, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setSubmitStatus('success');
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        category: '',
        message: '',
        urgent: false
      });
    } catch (error) {
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            Contact Animal Heaven
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            We're here to help! Whether you have questions about adoption, need emergency rescue assistance, 
            or want to get involved as a volunteer, we'd love to hear from you.
          </p>
          
          {/* Quick Contact Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
              <div className="text-sm text-neutral-600">Emergency Support</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">&lt;2hrs</div>
              <div className="text-sm text-neutral-600">Response Time</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">5</div>
              <div className="text-sm text-neutral-600">Locations</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">100%</div>
              <div className="text-sm text-neutral-600">Response Rate</div>
            </div>
          </div>
        </div>

        {/* Contact Methods */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
              How Can We Help?
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Choose the best way to reach us based on your needs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {contactInfo.map((info) => (
              <Card key={info.type} variant="elevated" className="group hover:scale-105 transition-all duration-300">
                <CardContent className="p-6">
                  <div className={`w-12 h-12 rounded-lg ${info.color} flex items-center justify-center mb-4`}>
                    {info.icon}
                  </div>
                  
                  <h3 className="font-semibold text-lg mb-2">{info.title}</h3>
                  <p className="text-neutral-600 text-sm mb-4 leading-relaxed">
                    {info.description}
                  </p>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-neutral-500" />
                      <span className="text-neutral-700">{info.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-neutral-500" />
                      <span className="text-neutral-700">{info.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-neutral-500" />
                      <span className="text-neutral-700">{info.hours}</span>
                    </div>
                  </div>

                  <div className="mt-4 flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Phone className="w-4 h-4 mr-1" />
                      Call
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Mail className="w-4 h-4 mr-1" />
                      Email
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Contact Form and Location */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Contact Form */}
          <Card variant="warm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="w-5 h-5" />
                Send Us a Message
              </CardTitle>
            </CardHeader>
            <CardContent>
              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-100 border border-green-300 rounded-lg flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-green-800">Message sent successfully! We'll get back to you soon.</span>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-100 border border-red-300 rounded-lg flex items-center gap-2">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <span className="text-red-800">Failed to send message. Please try again or call us directly.</span>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Full Name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    required
                  />
                  <Input
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Phone Number"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                  />
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Category
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      required
                    >
                      <option value="">Select a category</option>
                      <option value="adoption">Adoption Inquiry</option>
                      <option value="volunteer">Volunteer Interest</option>
                      <option value="donation">Donation Question</option>
                      <option value="emergency">Emergency Rescue</option>
                      <option value="general">General Question</option>
                      <option value="feedback">Feedback</option>
                    </select>
                  </div>
                </div>

                <Input
                  label="Subject"
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                  required
                />

                <div>
                  <label className="block text-sm font-medium text-neutral-700 mb-2">
                    Message
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    rows={5}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Please provide details about your inquiry..."
                    required
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="urgent"
                    checked={formData.urgent}
                    onChange={(e) => handleInputChange('urgent', e.target.checked)}
                    className="rounded border-neutral-300 text-orange-600 focus:ring-orange-500"
                  />
                  <label htmlFor="urgent" className="ml-2 text-sm text-neutral-700">
                    This is an urgent matter requiring immediate attention
                  </label>
                </div>

                <Button 
                  type="submit" 
                  variant="primary" 
                  size="lg" 
                  className="w-full"
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Location and Hours */}
          <div className="space-y-6">
            {/* Main Location */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Visit Our Main Shelter
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Animal Heaven Main Shelter</h4>
                    <p className="text-neutral-600">
                      123 Pet Street<br />
                      Animal City, AC 12345<br />
                      India
                    </p>
                  </div>

                  <div>
                    <h4 className="font-semibold mb-2">Operating Hours</h4>
                    <div className="space-y-1 text-sm text-neutral-600">
                      <div className="flex justify-between">
                        <span>Monday - Friday</span>
                        <span>9:00 AM - 6:00 PM</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Saturday - Sunday</span>
                        <span>10:00 AM - 4:00 PM</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Emergency Hotline</span>
                        <span>24/7</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1">
                      <NavigationIcon className="w-4 h-4 mr-2" />
                      Get Directions
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule Visit
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Map Placeholder */}
            <Card variant="outlined">
              <CardContent className="p-0">
                <div className="h-64 bg-gradient-to-br from-blue-100 to-green-100 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-12 h-12 text-neutral-400 mx-auto mb-2" />
                    <p className="text-neutral-600 font-medium">Interactive Map</p>
                    <p className="text-neutral-500 text-sm">Click to view full map</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Locations */}
            <Card variant="outlined">
              <CardHeader>
                <CardTitle>Other Locations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div>
                      <h5 className="font-medium">North Branch</h5>
                      <p className="text-sm text-neutral-600">456 North Ave, City North</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <NavigationIcon className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <h5 className="font-medium">South Branch</h5>
                      <p className="text-sm text-neutral-600">789 South St, City South</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <NavigationIcon className="w-4 h-4" />
                    </Button>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>
                      <h5 className="font-medium">Mobile Clinic</h5>
                      <p className="text-sm text-neutral-600">Various locations daily</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Calendar className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* FAQ Preview */}
        <section className="mb-16">
          <Card variant="warm" className="max-w-4xl mx-auto">
            <CardContent className="p-8 text-center">
              <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-neutral-600 mb-6">
                Find quick answers to common questions about adoption, volunteering, and our services.
              </p>
              <Button variant="primary" size="lg">
                View All FAQs
              </Button>
            </CardContent>
          </Card>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default ContactPage;
