'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { 
  Heart, 
  Share2, 
  Download, 
  Filter, 
  Grid, 
  List, 
  Calendar, 
  User, 
  Camera,
  Instagram,
  Facebook,
  Twitter,
  Upload,
  X,
  ChevronLeft,
  ChevronRight,
  Star,
  Eye
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  Badge, 
  Input,
  Navigation,
  Footer
} from '@/components';

interface GalleryPhoto {
  id: string;
  url: string;
  title: string;
  description: string;
  petName?: string;
  petType: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  category: 'adoption' | 'success' | 'rescue' | 'daily-life' | 'events' | 'volunteers';
  submittedBy: {
    name: string;
    type: 'adopter' | 'volunteer' | 'staff' | 'community';
  };
  submittedDate: string;
  likes: number;
  views: number;
  featured: boolean;
  tags: string[];
}

interface Category {
  id: string;
  name: string;
  description: string;
  count: number;
  color: string;
}

const categories: Category[] = [
  {
    id: 'adoption',
    name: 'Adoption Stories',
    description: 'Happy moments from adoption days',
    count: 45,
    color: 'bg-orange-100 text-orange-800'
  },
  {
    id: 'success',
    name: 'Success Stories',
    description: 'Before and after transformations',
    count: 32,
    color: 'bg-green-100 text-green-800'
  },
  {
    id: 'rescue',
    name: 'Rescue Operations',
    description: 'Behind the scenes of rescue work',
    count: 28,
    color: 'bg-blue-100 text-blue-800'
  },
  {
    id: 'daily-life',
    name: 'Daily Life',
    description: 'Pets enjoying their new homes',
    count: 67,
    color: 'bg-purple-100 text-purple-800'
  },
  {
    id: 'events',
    name: 'Events',
    description: 'Adoption fairs and community events',
    count: 23,
    color: 'bg-yellow-100 text-yellow-800'
  },
  {
    id: 'volunteers',
    name: 'Volunteers',
    description: 'Our amazing volunteers in action',
    count: 19,
    color: 'bg-pink-100 text-pink-800'
  }
];

const samplePhotos: GalleryPhoto[] = [
  {
    id: '1',
    url: '/images/gallery/adoption-1.jpg',
    title: 'Max\'s First Day Home',
    description: 'Max exploring his new backyard for the first time. Pure joy!',
    petName: 'Max',
    petType: 'dog',
    category: 'adoption',
    submittedBy: { name: 'Sarah Johnson', type: 'adopter' },
    submittedDate: '2024-01-15',
    likes: 234,
    views: 1456,
    featured: true,
    tags: ['golden-retriever', 'first-day', 'backyard', 'happy']
  },
  {
    id: '2',
    url: '/images/gallery/success-1.jpg',
    title: 'Luna\'s Transformation',
    description: 'From scared shelter cat to confident family member in 6 months.',
    petName: 'Luna',
    petType: 'cat',
    category: 'success',
    submittedBy: { name: 'Mike Chen', type: 'adopter' },
    submittedDate: '2024-01-20',
    likes: 189,
    views: 987,
    featured: true,
    tags: ['transformation', 'rescue', 'persian', 'before-after']
  },
  {
    id: '3',
    url: '/images/gallery/rescue-1.jpg',
    title: 'Emergency Rescue Operation',
    description: 'Our team rescuing abandoned puppies from a construction site.',
    petType: 'dog',
    category: 'rescue',
    submittedBy: { name: 'Animal Heaven Team', type: 'staff' },
    submittedDate: '2024-01-25',
    likes: 456,
    views: 2134,
    featured: false,
    tags: ['rescue', 'puppies', 'emergency', 'teamwork']
  },
  {
    id: '4',
    url: '/images/gallery/daily-1.jpg',
    title: 'Buddy\'s Morning Routine',
    description: 'Buddy helping with the morning coffee routine. Best assistant ever!',
    petName: 'Buddy',
    petType: 'dog',
    category: 'daily-life',
    submittedBy: { name: 'Lisa Patel', type: 'adopter' },
    submittedDate: '2024-02-01',
    likes: 123,
    views: 678,
    featured: false,
    tags: ['morning', 'coffee', 'helper', 'routine']
  },
  {
    id: '5',
    url: '/images/gallery/event-1.jpg',
    title: 'Community Adoption Fair',
    description: 'Amazing turnout at our monthly adoption fair in the park.',
    petType: 'other',
    category: 'events',
    submittedBy: { name: 'Volunteer Team', type: 'volunteer' },
    submittedDate: '2024-02-05',
    likes: 89,
    views: 445,
    featured: false,
    tags: ['adoption-fair', 'community', 'park', 'volunteers']
  },
  {
    id: '6',
    url: '/images/gallery/volunteer-1.jpg',
    title: 'Volunteer Training Day',
    description: 'New volunteers learning proper pet handling techniques.',
    petType: 'other',
    category: 'volunteers',
    submittedBy: { name: 'Training Team', type: 'staff' },
    submittedDate: '2024-02-10',
    likes: 67,
    views: 234,
    featured: false,
    tags: ['training', 'volunteers', 'education', 'skills']
  }
];

const GalleryPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredPhotos, setFilteredPhotos] = useState(samplePhotos);
  const [selectedPhoto, setSelectedPhoto] = useState<GalleryPhoto | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [sortBy, setSortBy] = useState('newest');

  React.useEffect(() => {
    let filtered = samplePhotos.filter(photo => {
      const matchesCategory = selectedCategory === 'all' || photo.category === selectedCategory;
      const matchesSearch = searchQuery === '' || 
        photo.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        photo.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        photo.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      return matchesCategory && matchesSearch;
    });

    // Sort photos
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.submittedDate).getTime() - new Date(a.submittedDate).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.submittedDate).getTime() - new Date(b.submittedDate).getTime());
        break;
      case 'popular':
        filtered.sort((a, b) => b.likes - a.likes);
        break;
      case 'views':
        filtered.sort((a, b) => b.views - a.views);
        break;
    }

    setFilteredPhotos(filtered);
  }, [selectedCategory, searchQuery, sortBy]);

  const handlePhotoClick = (photo: GalleryPhoto) => {
    setSelectedPhoto(photo);
  };

  const handleLike = (photoId: string) => {
    setFilteredPhotos(prev => 
      prev.map(photo => 
        photo.id === photoId 
          ? { ...photo, likes: photo.likes + 1 }
          : photo
      )
    );
  };

  const handleShare = (photo: GalleryPhoto) => {
    if (navigator.share) {
      navigator.share({
        title: photo.title,
        text: photo.description,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  const nextPhoto = () => {
    if (!selectedPhoto) return;
    const currentIndex = filteredPhotos.findIndex(p => p.id === selectedPhoto.id);
    const nextIndex = (currentIndex + 1) % filteredPhotos.length;
    setSelectedPhoto(filteredPhotos[nextIndex]);
  };

  const previousPhoto = () => {
    if (!selectedPhoto) return;
    const currentIndex = filteredPhotos.findIndex(p => p.id === selectedPhoto.id);
    const prevIndex = (currentIndex - 1 + filteredPhotos.length) % filteredPhotos.length;
    setSelectedPhoto(filteredPhotos[prevIndex]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            Happy Tails Gallery
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Celebrate the joy of pet adoption through our community gallery. Share your pet's story, 
            browse heartwarming moments, and connect with fellow animal lovers.
          </p>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">500+</div>
              <div className="text-sm text-neutral-600">Photos Shared</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-600 mb-2">200+</div>
              <div className="text-sm text-neutral-600">Success Stories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">50K+</div>
              <div className="text-sm text-neutral-600">Total Views</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">1K+</div>
              <div className="text-sm text-neutral-600">Community Members</div>
            </div>
          </div>
        </div>

        {/* Upload Button */}
        <div className="text-center mb-12">
          <Button
            variant="primary"
            size="lg"
            onClick={() => setShowUploadModal(true)}
            leftIcon={<Upload className="w-5 h-5" />}
          >
            Share Your Pet's Photo
          </Button>
          <p className="text-sm text-neutral-500 mt-2">
            Help inspire others by sharing your pet's journey
          </p>
        </div>

        {/* Categories */}
        <section className="mb-12">
          <div className="text-center mb-8">
            <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
              Browse by Category
            </h2>
            <p className="text-lg text-neutral-600">
              Explore different types of moments and stories
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            {categories.map((category) => (
              <Card
                key={category.id}
                variant={selectedCategory === category.id ? 'warm' : 'outlined'}
                className="cursor-pointer transition-all duration-300 hover:scale-105"
                onClick={() => setSelectedCategory(category.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-semibold text-lg">{category.name}</h3>
                      <p className="text-neutral-600 text-sm">{category.description}</p>
                    </div>
                    <Badge variant="outline" className={category.color}>
                      {category.count}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button
              variant={selectedCategory === 'all' ? 'primary' : 'outline'}
              onClick={() => setSelectedCategory('all')}
            >
              View All Photos
            </Button>
          </div>
        </section>

        {/* Search and Filters */}
        <div className="mb-8">
          <Card variant="warm">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search photos by title, description, or tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    leftIcon={<Camera className="w-4 h-4" />}
                    variant="default"
                  />
                </div>
                <div className="flex gap-4">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="popular">Most Liked</option>
                    <option value="views">Most Viewed</option>
                  </select>
                  <div className="flex border border-neutral-300 rounded-lg overflow-hidden">
                    <Button
                      variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="rounded-none border-0"
                    >
                      <Grid className="w-4 h-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'masonry' ? 'primary' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('masonry')}
                      className="rounded-none border-0"
                    >
                      <List className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Photo Gallery */}
        <section className="mb-12">
          <div className="flex justify-between items-center mb-6">
            <h2 className="font-primary text-2xl font-bold text-neutral-900">
              {filteredPhotos.length} photos found
              {selectedCategory !== 'all' && ` in ${categories.find(c => c.id === selectedCategory)?.name}`}
            </h2>
          </div>

          {filteredPhotos.length > 0 ? (
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6'
            }>
              {filteredPhotos.map((photo) => (
                <Card
                  key={photo.id}
                  variant="elevated"
                  padding="none"
                  className="group overflow-hidden cursor-pointer hover:scale-105 transition-all duration-300"
                  onClick={() => handlePhotoClick(photo)}
                >
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={photo.url}
                      alt={photo.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />

                    {/* Fallback gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-200 to-pink-200 flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-4xl mb-2">
                          {photo.petType === 'dog' ? '🐕' :
                           photo.petType === 'cat' ? '🐱' :
                           photo.petType === 'bird' ? '🐦' :
                           photo.petType === 'rabbit' ? '🐰' : '🐾'}
                        </div>
                        <p className="text-neutral-600 font-medium text-sm">{photo.title}</p>
                      </div>
                    </div>

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300" />

                    {/* Featured Badge */}
                    {photo.featured && (
                      <div className="absolute top-3 left-3">
                        <Badge variant="primary" size="sm">
                          ⭐ Featured
                        </Badge>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="absolute bottom-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLike(photo.id);
                        }}
                        className="bg-white/90 backdrop-blur-sm text-red-600 hover:bg-white"
                      >
                        <Heart className="w-4 h-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleShare(photo);
                        }}
                        className="bg-white/90 backdrop-blur-sm hover:bg-white"
                      >
                        <Share2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-1 line-clamp-1">{photo.title}</h3>
                    <p className="text-neutral-600 text-sm mb-3 line-clamp-2">{photo.description}</p>

                    <div className="flex items-center justify-between text-xs text-neutral-500">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          <Heart className="w-3 h-3" />
                          <span>{photo.likes}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          <span>{photo.views}</span>
                        </div>
                      </div>
                      <span>by {photo.submittedBy.name}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card variant="outlined" className="text-center py-12">
              <CardContent>
                <Camera className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="font-semibold text-neutral-700 mb-2">No photos found</h3>
                <p className="text-neutral-500 mb-4">
                  Try adjusting your search terms or category filter.
                </p>
                <Button variant="outline" onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                }}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default GalleryPage;
