import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatDate(date: Date | string): string {
  const d = new Date(date);
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

export function formatAge(value: number, unit: 'months' | 'years'): string {
  if (unit === 'months') {
    if (value < 12) {
      return `${value} ${value === 1 ? 'month' : 'months'} old`;
    } else {
      const years = Math.floor(value / 12);
      const months = value % 12;
      if (months === 0) {
        return `${years} ${years === 1 ? 'year' : 'years'} old`;
      }
      return `${years}y ${months}m old`;
    }
  }
  return `${value} ${value === 1 ? 'year' : 'years'} old`;
}

export function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-');
}
