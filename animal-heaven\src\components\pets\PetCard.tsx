'use client';

import React from 'react';
import Image from 'next/image';
import { Heart, MapPin, Calendar, Eye } from 'lucide-react';
import { <PERSON>, CardContent, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { cn, formatAge } from '@/lib/utils';

export interface Pet {
  id: string;
  name: string;
  type: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  breed: string;
  age: {
    value: number;
    unit: 'months' | 'years';
  };
  gender: 'male' | 'female';
  size: 'small' | 'medium' | 'large' | 'extra-large';
  color: string;
  description: string;
  personality: string[];
  images: Array<{
    url: string;
    alt: string;
    isPrimary: boolean;
  }>;
  status: 'available' | 'pending' | 'adopted' | 'fostered' | 'medical-hold';
  location: {
    shelter: string;
    city: string;
    state: string;
  };
  adoptionFee: number;
  specialNeeds: boolean;
  goodWith: {
    children: boolean;
    dogs: boolean;
    cats: boolean;
  };
  views: number;
  likes: number;
  featured: boolean;
}

interface PetCardProps {
  pet: Pet;
  onLike?: (petId: string) => void;
  onSave?: (petId: string) => void;
  onViewDetails?: (petId: string) => void;
  isLiked?: boolean;
  isSaved?: boolean;
  className?: string;
}

const PetCard: React.FC<PetCardProps> = ({
  pet,
  onLike,
  onSave,
  onViewDetails,
  isLiked = false,
  isSaved = false,
  className
}) => {
  const primaryImage = pet.images.find(img => img.isPrimary) || pet.images[0];
  
  const statusColors = {
    available: 'success',
    pending: 'warning',
    adopted: 'primary',
    fostered: 'secondary',
    'medical-hold': 'danger'
  } as const;

  const sizeLabels = {
    small: 'Small',
    medium: 'Medium',
    large: 'Large',
    'extra-large': 'Extra Large'
  };

  return (
    <Card 
      variant="warm" 
      padding="none" 
      rounded="xl"
      className={cn(
        'group overflow-hidden hover:scale-105 transition-all duration-300 cursor-pointer',
        pet.featured && 'ring-2 ring-orange-300 ring-offset-2',
        className
      )}
      onClick={() => onViewDetails?.(pet.id)}
    >
      {/* Image Section */}
      <div className="relative aspect-[4/3] overflow-hidden">
        {primaryImage ? (
          <Image
            src={primaryImage.url}
            alt={primaryImage.alt}
            fill
            className="object-cover group-hover:scale-110 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-orange-100 to-amber-100 flex items-center justify-center">
            <div className="text-6xl">🐾</div>
          </div>
        )}
        
        {/* Status Badge */}
        <div className="absolute top-3 left-3">
          <Badge variant={statusColors[pet.status]} size="sm">
            {pet.status.charAt(0).toUpperCase() + pet.status.slice(1).replace('-', ' ')}
          </Badge>
        </div>

        {/* Featured Badge */}
        {pet.featured && (
          <div className="absolute top-3 right-3">
            <Badge variant="primary" size="sm">
              ⭐ Featured
            </Badge>
          </div>
        )}

        {/* Action Buttons */}
        <div className="absolute bottom-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="sm"
            variant={isLiked ? 'primary' : 'outline'}
            onClick={(e) => {
              e.stopPropagation();
              onLike?.(pet.id);
            }}
            className="bg-white/90 backdrop-blur-sm"
          >
            <Heart className={cn('w-4 h-4', isLiked && 'fill-current')} />
          </Button>
          <Button
            size="sm"
            variant={isSaved ? 'secondary' : 'outline'}
            onClick={(e) => {
              e.stopPropagation();
              onSave?.(pet.id);
            }}
            className="bg-white/90 backdrop-blur-sm"
          >
            📋
          </Button>
        </div>

        {/* Special Needs Indicator */}
        {pet.specialNeeds && (
          <div className="absolute bottom-3 left-3">
            <Badge variant="warning" size="sm">
              Special Needs
            </Badge>
          </div>
        )}
      </div>

      {/* Content Section */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Name and Basic Info */}
          <div>
            <h3 className="font-primary text-lg font-semibold text-neutral-900 group-hover:text-orange-600 transition-colors">
              {pet.name}
            </h3>
            <p className="text-sm text-neutral-600">
              {pet.breed} • {formatAge(pet.age.value, pet.age.unit)} • {sizeLabels[pet.size]}
            </p>
          </div>

          {/* Description */}
          <p className="text-sm text-neutral-700 line-clamp-2">
            {pet.description}
          </p>

          {/* Personality Traits */}
          <div className="flex flex-wrap gap-1">
            {pet.personality.slice(0, 3).map((trait) => (
              <Badge key={trait} variant="outline" size="sm">
                {trait}
              </Badge>
            ))}
            {pet.personality.length > 3 && (
              <Badge variant="outline" size="sm">
                +{pet.personality.length - 3} more
              </Badge>
            )}
          </div>

          {/* Good With */}
          <div className="flex gap-2 text-xs text-neutral-600">
            {pet.goodWith.children && <span>👶 Kids</span>}
            {pet.goodWith.dogs && <span>🐕 Dogs</span>}
            {pet.goodWith.cats && <span>🐱 Cats</span>}
          </div>
        </div>
      </CardContent>

      {/* Footer */}
      <CardFooter className="px-4 pb-4 pt-0 flex justify-between items-center">
        <div className="flex items-center gap-4 text-xs text-neutral-500">
          <div className="flex items-center gap-1">
            <MapPin className="w-3 h-3" />
            <span>{pet.location.city}</span>
          </div>
          <div className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            <span>{pet.views}</span>
          </div>
        </div>
        
        <div className="text-right">
          <p className="text-sm font-semibold text-orange-600">
            {pet.adoptionFee === 0 ? 'Free' : `₹${pet.adoptionFee.toLocaleString()}`}
          </p>
          <p className="text-xs text-neutral-500">Adoption Fee</p>
        </div>
      </CardFooter>
    </Card>
  );
};

export { PetCard };
export type { Pet, PetCardProps };
