import { NextRequest, NextResponse } from 'next/server';
import { db, validateEmail, hashPassword } from '@/lib/database';

// POST /api/auth/register - User registration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['email', 'password', 'firstName', 'lastName'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Validate email format
    if (!validateEmail(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (body.password.length < 6) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 6 characters long' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db.getUserByEmail(body.email.toLowerCase());
    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 409 }
      );
    }

    // Hash password (in real app)
    // const passwordHash = await hashPassword(body.password);

    // Create user
    const userData = {
      email: body.email.toLowerCase(),
      firstName: body.firstName.trim(),
      lastName: body.lastName.trim(),
      phone: body.phone?.trim(),
      address: body.address?.trim(),
      role: 'user' as const,
      preferences: {
        petTypes: [],
        notifications: true,
        newsletter: false
      },
      verificationStatus: 'pending' as const,
      lastLogin: new Date().toISOString()
    };

    const user = await db.createUser(userData);

    // In real app, generate JWT token
    const token = `mock_token_${user.id}_${Date.now()}`;

    // Remove sensitive data from user object
    const { ...safeUser } = user;

    return NextResponse.json({
      success: true,
      data: {
        user: safeUser,
        token,
        expiresIn: '24h'
      },
      message: 'Registration successful'
    }, { status: 201 });
  } catch (error) {
    console.error('Error during registration:', error);
    return NextResponse.json(
      { success: false, error: 'Registration failed' },
      { status: 500 }
    );
  }
}

// OPTIONS /api/auth/register - Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
