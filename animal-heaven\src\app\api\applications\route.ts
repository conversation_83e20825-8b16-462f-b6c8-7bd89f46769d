import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';

// GET /api/applications - Get applications with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // In real app, validate authentication and authorization
    // const user = await validateAuthToken(request);
    // if (!user) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    const filters = {
      userId: searchParams.get('userId') || undefined,
      petId: searchParams.get('petId') || undefined,
      status: searchParams.get('status') || undefined,
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: parseInt(searchParams.get('offset') || '0')
    };

    // If not admin/staff, only show user's own applications
    // if (user.role !== 'admin' && user.role !== 'staff') {
    //   filters.userId = user.id;
    // }

    const result = await db.getApplications(filters);
    
    return NextResponse.json({
      success: true,
      data: result.applications,
      pagination: {
        total: result.total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: result.total > filters.offset + filters.limit
      }
    });
  } catch (error) {
    console.error('Error fetching applications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch applications' },
      { status: 500 }
    );
  }
}

// POST /api/applications - Create a new adoption application
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In real app, validate user authentication
    // const user = await validateAuthToken(request);
    // if (!user) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    // Validate required fields
    const requiredFields = ['userId', 'petId', 'applicationData'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate that the pet exists and is available
    const pet = await db.getPetById(body.petId);
    if (!pet) {
      return NextResponse.json(
        { success: false, error: 'Pet not found' },
        { status: 404 }
      );
    }

    if (pet.status !== 'available') {
      return NextResponse.json(
        { success: false, error: 'Pet is not available for adoption' },
        { status: 400 }
      );
    }

    // Check if user already has a pending application for this pet
    const existingApplications = await db.getApplications({
      userId: body.userId,
      petId: body.petId,
      status: 'pending'
    });

    if (existingApplications.total > 0) {
      return NextResponse.json(
        { success: false, error: 'You already have a pending application for this pet' },
        { status: 400 }
      );
    }

    // Validate application data structure
    const applicationData = body.applicationData;
    const requiredAppFields = ['livingArrangement', 'experience', 'workSchedule'];
    for (const field of requiredAppFields) {
      if (!applicationData[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required application field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Create application
    const application = await db.createApplication({
      userId: body.userId,
      petId: body.petId,
      status: 'pending',
      priority: 'medium',
      applicationData: {
        livingArrangement: applicationData.livingArrangement,
        experience: applicationData.experience,
        otherPets: applicationData.otherPets || false,
        children: applicationData.children || false,
        workSchedule: applicationData.workSchedule,
        references: applicationData.references || []
      },
      notes: [],
      nextStep: 'Application under review'
    });
    
    return NextResponse.json({
      success: true,
      data: application,
      message: 'Application submitted successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating application:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit application' },
      { status: 500 }
    );
  }
}

// PUT /api/applications - Bulk update applications (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In real app, validate admin authentication
    // const user = await validateAuthToken(request);
    // if (!user || (user.role !== 'admin' && user.role !== 'staff')) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    if (!Array.isArray(body.updates)) {
      return NextResponse.json(
        { success: false, error: 'Updates must be an array' },
        { status: 400 }
      );
    }

    const results = [];
    for (const update of body.updates) {
      if (!update.id) {
        results.push({ id: null, success: false, error: 'Missing application ID' });
        continue;
      }

      try {
        const updatedApplication = await db.updateApplication(update.id, update.data);
        if (updatedApplication) {
          results.push({ id: update.id, success: true, data: updatedApplication });
        } else {
          results.push({ id: update.id, success: false, error: 'Application not found' });
        }
      } catch (error) {
        results.push({ id: update.id, success: false, error: 'Update failed' });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed'
    });
  } catch (error) {
    console.error('Error bulk updating applications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update applications' },
      { status: 500 }
    );
  }
}
