'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface FilterOption {
  id: string;
  label: string;
  icon: string;
  count: number;
  description: string;
  color: 'primary' | 'secondary' | 'success' | 'warning';
}

interface QuickFiltersProps {
  onFilterSelect?: (filterId: string) => void;
}

const filterOptions: FilterOption[] = [
  {
    id: 'dogs',
    label: 'Dogs',
    icon: '🐕',
    count: 156,
    description: 'Loyal companions ready for adventure',
    color: 'primary'
  },
  {
    id: 'cats',
    label: 'Cats',
    icon: '🐱',
    count: 89,
    description: 'Independent friends who love to cuddle',
    color: 'secondary'
  },
  {
    id: 'puppies',
    label: 'Puppies',
    icon: '🐶',
    count: 34,
    description: 'Young pups full of energy and love',
    color: 'warning'
  },
  {
    id: 'kittens',
    label: 'Kittens',
    icon: '🐾',
    count: 28,
    description: 'Tiny bundles of joy and mischief',
    color: 'success'
  },
  {
    id: 'senior',
    label: 'Senior Pets',
    icon: '💝',
    count: 42,
    description: 'Wise souls looking for peaceful homes',
    color: 'primary'
  },
  {
    id: 'special-needs',
    label: 'Special Needs',
    icon: '❤️',
    count: 23,
    description: 'Extra special pets needing extra love',
    color: 'secondary'
  }
];

const QuickFilters: React.FC<QuickFiltersProps> = ({ onFilterSelect }) => {
  return (
    <section className="py-16 bg-gradient-to-br from-neutral-50 to-orange-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
            Find Your Perfect Match
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Browse by category to find the type of companion that fits your lifestyle. 
            Each pet is unique and waiting for their special someone.
          </p>
        </div>

        {/* Quick Filter Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filterOptions.map((option) => (
            <Card 
              key={option.id} 
              variant="warm" 
              className="group hover:scale-105 transition-all duration-300 cursor-pointer"
              onClick={() => onFilterSelect?.(option.id)}
            >
              <CardContent className="p-6 text-center">
                {/* Icon */}
                <div className="text-6xl mb-4 group-hover:animate-bounce-gentle">
                  {option.icon}
                </div>

                {/* Title and Count */}
                <div className="mb-3">
                  <h3 className="font-primary text-xl font-semibold text-neutral-900 mb-1">
                    {option.label}
                  </h3>
                  <Badge variant={option.color} size="sm">
                    {option.count} available
                  </Badge>
                </div>

                {/* Description */}
                <p className="text-neutral-600 text-sm mb-4 leading-relaxed">
                  {option.description}
                </p>

                {/* Action Button */}
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full group-hover:bg-orange-500 group-hover:text-white group-hover:border-orange-500 transition-all duration-300"
                >
                  Browse {option.label}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Emergency Adoptions */}
          <Card variant="outlined" className="border-red-200 bg-red-50">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-3">🚨</div>
              <h3 className="font-primary text-lg font-semibold text-red-800 mb-2">
                Urgent Adoptions
              </h3>
              <p className="text-red-600 text-sm mb-4">
                Pets in immediate need of homes
              </p>
              <Button variant="danger" size="sm" className="w-full">
                View Urgent Cases
              </Button>
            </CardContent>
          </Card>

          {/* Featured Pets */}
          <Card variant="outlined" className="border-yellow-200 bg-yellow-50">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-3">⭐</div>
              <h3 className="font-primary text-lg font-semibold text-yellow-800 mb-2">
                Featured Pets
              </h3>
              <p className="text-yellow-600 text-sm mb-4">
                Staff picks and special personalities
              </p>
              <Button variant="warning" size="sm" className="w-full">
                Meet Our Stars
              </Button>
            </CardContent>
          </Card>

          {/* Recently Added */}
          <Card variant="outlined" className="border-green-200 bg-green-50">
            <CardContent className="p-6 text-center">
              <div className="text-4xl mb-3">✨</div>
              <h3 className="font-primary text-lg font-semibold text-green-800 mb-2">
                New Arrivals
              </h3>
              <p className="text-green-600 text-sm mb-4">
                Recently rescued and ready for love
              </p>
              <Button variant="secondary" size="sm" className="w-full">
                See New Pets
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Search Encouragement */}
        <div className="text-center mt-12">
          <Card variant="elevated" className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="font-primary text-2xl font-semibold text-neutral-900 mb-4">
                Can't Find What You're Looking For?
              </h3>
              <p className="text-neutral-600 mb-6">
                Use our advanced search to filter by specific breeds, ages, sizes, and personality traits. 
                We'll help you find the perfect match for your family.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="primary" size="lg">
                  Advanced Search
                </Button>
                <Button variant="outline" size="lg">
                  Get Adoption Help
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Statistics Bar */}
        <div className="mt-16 bg-white rounded-2xl shadow-lg p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-2xl font-bold text-orange-600 mb-1">245</div>
              <div className="text-sm text-neutral-600">Pets Available</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600 mb-1">2,500+</div>
              <div className="text-sm text-neutral-600">Successful Adoptions</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600 mb-1">50+</div>
              <div className="text-sm text-neutral-600">Partner Shelters</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600 mb-1">24/7</div>
              <div className="text-sm text-neutral-600">Support Available</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export { QuickFilters };
export type { FilterOption, QuickFiltersProps };
