'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
}

const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ 
    className, 
    variant = 'default', 
    size = 'md',
    icon,
    children, 
    ...props 
  }, ref) => {
    const baseStyles = `
      inline-flex items-center gap-1 rounded-full font-medium transition-colors
    `;

    const variants = {
      default: 'bg-neutral-100 text-neutral-800 hover:bg-neutral-200',
      primary: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
      secondary: 'bg-green-100 text-green-800 hover:bg-green-200',
      success: 'bg-emerald-100 text-emerald-800 hover:bg-emerald-200',
      warning: 'bg-amber-100 text-amber-800 hover:bg-amber-200',
      danger: 'bg-red-100 text-red-800 hover:bg-red-200',
      outline: 'border border-neutral-300 text-neutral-700 hover:bg-neutral-50'
    };

    const sizes = {
      sm: 'px-2 py-0.5 text-xs',
      md: 'px-2.5 py-1 text-sm',
      lg: 'px-3 py-1.5 text-base'
    };

    return (
      <div
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        {...props}
      >
        {icon && <span className="flex-shrink-0">{icon}</span>}
        {children}
      </div>
    );
  }
);

Badge.displayName = 'Badge';

export { Badge };
