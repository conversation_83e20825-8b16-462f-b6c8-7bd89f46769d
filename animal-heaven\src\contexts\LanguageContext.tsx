'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'hi' | 'es' | 'fr';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string) => string;
  isRTL: boolean;
}

// Simple translations object - in real app, this would be loaded from files
const translations: Record<Language, Record<string, string>> = {
  en: {
    'nav.home': 'Home',
    'nav.adopt': 'Adopt',
    'nav.volunteer': 'Volunteer',
    'nav.donate': 'Donate',
    'nav.resources': 'Resources',
    'nav.gallery': 'Gallery',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.login': 'Login',
    'nav.signup': 'Sign Up',
    'nav.logout': 'Logout',
    'nav.dashboard': 'Dashboard',
    'hero.title': 'Find Your Perfect Companion',
    'hero.subtitle': 'Connect with loving pets in need of forever homes',
    'hero.cta': 'Start Your Search',
    'common.loading': 'Loading...',
    'common.error': 'Something went wrong',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.submit': 'Submit',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'footer.tagline': 'Every pet deserves a loving home'
  },
  hi: {
    'nav.home': 'होम',
    'nav.adopt': 'गोद लें',
    'nav.volunteer': 'स्वयंसेवक',
    'nav.donate': 'दान करें',
    'nav.resources': 'संसाधन',
    'nav.gallery': 'गैलरी',
    'nav.about': 'हमारे बारे में',
    'nav.contact': 'संपर्क',
    'nav.login': 'लॉगिन',
    'nav.signup': 'साइन अप',
    'nav.logout': 'लॉगआउट',
    'nav.dashboard': 'डैशबोर्ड',
    'hero.title': 'अपना परफेक्ट साथी खोजें',
    'hero.subtitle': 'हमेशा के लिए घर की तलाश में प्यारे पालतू जानवरों से जुड़ें',
    'hero.cta': 'अपनी खोज शुरू करें',
    'common.loading': 'लोड हो रहा है...',
    'common.error': 'कुछ गलत हुआ',
    'common.save': 'सेव करें',
    'common.cancel': 'रद्द करें',
    'common.submit': 'जमा करें',
    'common.search': 'खोजें',
    'common.filter': 'फिल्टर',
    'footer.tagline': 'हर पालतू जानवर एक प्यार भरे घर का हकदार है'
  },
  es: {
    'nav.home': 'Inicio',
    'nav.adopt': 'Adoptar',
    'nav.volunteer': 'Voluntario',
    'nav.donate': 'Donar',
    'nav.resources': 'Recursos',
    'nav.gallery': 'Galería',
    'nav.about': 'Acerca de',
    'nav.contact': 'Contacto',
    'nav.login': 'Iniciar sesión',
    'nav.signup': 'Registrarse',
    'nav.logout': 'Cerrar sesión',
    'nav.dashboard': 'Panel',
    'hero.title': 'Encuentra tu Compañero Perfecto',
    'hero.subtitle': 'Conecta con mascotas amorosas que necesitan hogares para siempre',
    'hero.cta': 'Comienza tu Búsqueda',
    'common.loading': 'Cargando...',
    'common.error': 'Algo salió mal',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'common.submit': 'Enviar',
    'common.search': 'Buscar',
    'common.filter': 'Filtrar',
    'footer.tagline': 'Cada mascota merece un hogar lleno de amor'
  },
  fr: {
    'nav.home': 'Accueil',
    'nav.adopt': 'Adopter',
    'nav.volunteer': 'Bénévole',
    'nav.donate': 'Faire un don',
    'nav.resources': 'Ressources',
    'nav.gallery': 'Galerie',
    'nav.about': 'À propos',
    'nav.contact': 'Contact',
    'nav.login': 'Connexion',
    'nav.signup': 'S\'inscrire',
    'nav.logout': 'Déconnexion',
    'nav.dashboard': 'Tableau de bord',
    'hero.title': 'Trouvez votre Compagnon Parfait',
    'hero.subtitle': 'Connectez-vous avec des animaux aimants qui cherchent des foyers pour toujours',
    'hero.cta': 'Commencez votre Recherche',
    'common.loading': 'Chargement...',
    'common.error': 'Quelque chose s\'est mal passé',
    'common.save': 'Sauvegarder',
    'common.cancel': 'Annuler',
    'common.submit': 'Soumettre',
    'common.search': 'Rechercher',
    'common.filter': 'Filtrer',
    'footer.tagline': 'Chaque animal mérite un foyer aimant'
  }
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');

  // Translation function
  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  // Check if language is RTL
  const isRTL = language === 'ar' || language === 'he'; // Add RTL languages as needed

  // Set language and update document
  const setLanguage = (newLanguage: Language) => {
    setLanguageState(newLanguage);
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', newLanguage);
      document.documentElement.lang = newLanguage;
      document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    }
  };

  // Initialize language on mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    const browserLanguage = navigator.language.split('-')[0] as Language;
    
    // Use saved language, then browser language, then default to English
    const initialLanguage = savedLanguage || 
      (Object.keys(translations).includes(browserLanguage) ? browserLanguage : 'en');
    
    setLanguage(initialLanguage);
  }, []);

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
