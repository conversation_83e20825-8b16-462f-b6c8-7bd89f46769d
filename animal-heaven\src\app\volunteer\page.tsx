'use client';

import React, { useState } from 'react';
import { 
  Heart, 
  Users, 
  Calendar, 
  Clock, 
  MapPin, 
  Phone, 
  Mail, 
  CheckCircle,
  Star,
  Award,
  Camera,
  Stethoscope,
  Home,
  Car
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge, 
  Input,
  Navigation,
  Footer
} from '@/components';

interface VolunteerRole {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  timeCommitment: string;
  requirements: string[];
  benefits: string[];
  color: 'primary' | 'secondary' | 'success' | 'warning';
}

const volunteerRoles: VolunteerRole[] = [
  {
    id: 'animal-care',
    title: 'Animal Care Assistant',
    icon: <Heart className="w-6 h-6" />,
    description: 'Help with daily care activities including feeding, grooming, and providing companionship to our animals.',
    timeCommitment: '4-6 hours per week',
    requirements: ['Love for animals', 'Physical ability to handle pets', 'Reliable schedule'],
    benefits: ['Hands-on animal experience', 'Training provided', 'Flexible scheduling'],
    color: 'primary'
  },
  {
    id: 'adoption-counselor',
    title: 'Adoption Counselor',
    icon: <Users className="w-6 h-6" />,
    description: 'Guide potential adopters through the adoption process and help match families with perfect pets.',
    timeCommitment: '6-8 hours per week',
    requirements: ['Excellent communication skills', 'Weekend availability', 'Customer service experience'],
    benefits: ['People interaction', 'Adoption success stories', 'Communication training'],
    color: 'secondary'
  },
  {
    id: 'event-coordinator',
    title: 'Event Coordinator',
    icon: <Calendar className="w-6 h-6" />,
    description: 'Help organize adoption events, fundraisers, and community outreach programs.',
    timeCommitment: '8-10 hours per week',
    requirements: ['Event planning experience', 'Organizational skills', 'Creative thinking'],
    benefits: ['Event management experience', 'Community networking', 'Leadership opportunities'],
    color: 'success'
  },
  {
    id: 'photographer',
    title: 'Pet Photographer',
    icon: <Camera className="w-6 h-6" />,
    description: 'Capture beautiful photos of our animals to help them find homes faster.',
    timeCommitment: '2-4 hours per week',
    requirements: ['Photography skills', 'Own camera equipment', 'Patience with animals'],
    benefits: ['Portfolio building', 'Creative expression', 'Flexible timing'],
    color: 'warning'
  },
  {
    id: 'medical-assistant',
    title: 'Medical Assistant',
    icon: <Stethoscope className="w-6 h-6" />,
    description: 'Assist veterinary staff with basic medical care and health monitoring.',
    timeCommitment: '6-8 hours per week',
    requirements: ['Medical/veterinary background', 'Attention to detail', 'Calm demeanor'],
    benefits: ['Medical experience', 'Veterinary training', 'Health education'],
    color: 'primary'
  },
  {
    id: 'foster-coordinator',
    title: 'Foster Coordinator',
    icon: <Home className="w-6 h-6" />,
    description: 'Manage foster programs and support foster families with training and resources.',
    timeCommitment: '10-12 hours per week',
    requirements: ['Foster experience', 'Strong communication', 'Problem-solving skills'],
    benefits: ['Program management', 'Family support', 'Leadership role'],
    color: 'secondary'
  },
  {
    id: 'transport-volunteer',
    title: 'Transport Volunteer',
    icon: <Car className="w-6 h-6" />,
    description: 'Help transport animals to vet appointments, adoption events, and foster homes.',
    timeCommitment: '4-6 hours per week',
    requirements: ['Valid driver\'s license', 'Reliable vehicle', 'Flexible schedule'],
    benefits: ['Flexible timing', 'Direct impact', 'Travel opportunities'],
    color: 'success'
  }
];

const VolunteerPage: React.FC = () => {
  const [selectedRole, setSelectedRole] = useState<string | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    availability: '',
    experience: '',
    interests: [] as string[],
    motivation: ''
  });

  const handleRoleSelect = (roleId: string) => {
    setSelectedRole(roleId);
    setShowApplicationForm(true);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleInterestToggle = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Volunteer application submitted:', formData);
    alert('Thank you for your application! We will contact you within 48 hours.');
    setShowApplicationForm(false);
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: '',
      availability: '',
      experience: '',
      interests: [],
      motivation: ''
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            Join Our Volunteer Family
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Make a difference in the lives of animals in need. Whether you have 2 hours or 20 hours a week, 
            there's a perfect volunteer opportunity waiting for you at Animal Heaven.
          </p>
          
          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">150+</div>
              <div className="text-sm text-neutral-600">Active Volunteers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">2,500+</div>
              <div className="text-sm text-neutral-600">Animals Helped</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">50+</div>
              <div className="text-sm text-neutral-600">Events Organized</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">10,000+</div>
              <div className="text-sm text-neutral-600">Volunteer Hours</div>
            </div>
          </div>
        </div>

        {/* Volunteer Roles */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
              Volunteer Opportunities
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Choose from various roles that match your skills, interests, and availability. 
              Every contribution makes a meaningful impact.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {volunteerRoles.map((role) => (
              <Card 
                key={role.id} 
                variant="elevated" 
                className="group hover:scale-105 transition-all duration-300 cursor-pointer"
                onClick={() => handleRoleSelect(role.id)}
              >
                <CardHeader>
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`p-3 rounded-lg bg-${role.color === 'primary' ? 'orange' : role.color === 'secondary' ? 'green' : role.color === 'success' ? 'emerald' : 'amber'}-100`}>
                      {role.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{role.title}</CardTitle>
                      <Badge variant={role.color} size="sm">
                        {role.timeCommitment}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-neutral-600 mb-4 leading-relaxed">
                    {role.description}
                  </p>
                  
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-sm text-neutral-800 mb-2">Requirements:</h4>
                      <ul className="text-xs text-neutral-600 space-y-1">
                        {role.requirements.slice(0, 2).map((req, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-semibold text-sm text-neutral-800 mb-2">Benefits:</h4>
                      <ul className="text-xs text-neutral-600 space-y-1">
                        {role.benefits.slice(0, 2).map((benefit, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <Star className="w-3 h-3 text-yellow-500 flex-shrink-0" />
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-4 group-hover:bg-orange-500 group-hover:text-white group-hover:border-orange-500 transition-all duration-300"
                  >
                    Apply for This Role
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Why Volunteer */}
        <section className="mb-16">
          <Card variant="warm" className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                  Why Volunteer with Animal Heaven?
                </h2>
                <p className="text-lg text-neutral-600">
                  Join a community of passionate animal lovers making a real difference
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Heart className="w-5 h-5 text-orange-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 mb-2">Make a Real Impact</h3>
                      <p className="text-neutral-600 text-sm">
                        Every hour you volunteer directly improves the lives of animals in need and helps them find loving homes.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Users className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 mb-2">Join a Community</h3>
                      <p className="text-neutral-600 text-sm">
                        Connect with like-minded people who share your passion for animal welfare and make lasting friendships.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Award className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 mb-2">Gain Experience</h3>
                      <p className="text-neutral-600 text-sm">
                        Develop new skills, gain valuable experience, and enhance your resume while doing meaningful work.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Clock className="w-5 h-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 mb-2">Flexible Scheduling</h3>
                      <p className="text-neutral-600 text-sm">
                        Choose volunteer opportunities that fit your schedule, whether you have a few hours or a full day.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Star className="w-5 h-5 text-yellow-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 mb-2">Training & Support</h3>
                      <p className="text-neutral-600 text-sm">
                        Receive comprehensive training and ongoing support to ensure you feel confident and prepared.
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <Heart className="w-5 h-5 text-red-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-neutral-900 mb-2">Personal Fulfillment</h3>
                      <p className="text-neutral-600 text-sm">
                        Experience the joy and satisfaction that comes from helping animals and seeing them thrive.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Application Form Modal */}
        {showApplicationForm && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <Card variant="elevated" className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle>Volunteer Application</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowApplicationForm(false)}
                  >
                    ✕
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="First Name"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                    <Input
                      label="Last Name"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      required
                    />
                    <Input
                      label="Phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>

                  <Input
                    label="Address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    required
                  />

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Availability
                    </label>
                    <select
                      value={formData.availability}
                      onChange={(e) => handleInputChange('availability', e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      required
                    >
                      <option value="">Select your availability</option>
                      <option value="weekdays">Weekdays</option>
                      <option value="weekends">Weekends</option>
                      <option value="both">Both weekdays and weekends</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-3">
                      Areas of Interest (select all that apply)
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      {volunteerRoles.map((role) => (
                        <label key={role.id} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.interests.includes(role.id)}
                            onChange={() => handleInterestToggle(role.id)}
                            className="rounded border-neutral-300 text-orange-600 focus:ring-orange-500"
                          />
                          <span className="ml-2 text-sm text-neutral-700">{role.title}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Previous Experience with Animals
                    </label>
                    <textarea
                      value={formData.experience}
                      onChange={(e) => handleInputChange('experience', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Tell us about your experience with animals..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Why do you want to volunteer with us?
                    </label>
                    <textarea
                      value={formData.motivation}
                      onChange={(e) => handleInputChange('motivation', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Share your motivation for volunteering..."
                      required
                    />
                  </div>

                  <div className="flex gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowApplicationForm(false)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button type="submit" variant="primary" className="flex-1">
                      Submit Application
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Contact Information */}
        <section className="mb-16">
          <Card variant="outlined" className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                  Ready to Get Started?
                </h2>
                <p className="text-lg text-neutral-600">
                  Have questions about volunteering? We're here to help!
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="p-4 bg-orange-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <Phone className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold text-neutral-900 mb-2">Call Us</h3>
                  <p className="text-neutral-600 mb-2">+91 98765 43210</p>
                  <p className="text-sm text-neutral-500">Mon-Fri 9AM-6PM</p>
                </div>

                <div className="text-center">
                  <div className="p-4 bg-green-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <Mail className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-neutral-900 mb-2">Email Us</h3>
                  <p className="text-neutral-600 mb-2"><EMAIL></p>
                  <p className="text-sm text-neutral-500">Response within 24 hours</p>
                </div>

                <div className="text-center">
                  <div className="p-4 bg-blue-100 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <MapPin className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-neutral-900 mb-2">Visit Us</h3>
                  <p className="text-neutral-600 mb-2">123 Pet Street, Animal City</p>
                  <p className="text-sm text-neutral-500">Open daily 10AM-5PM</p>
                </div>
              </div>

              <div className="text-center mt-8">
                <Button variant="primary" size="lg" onClick={() => setShowApplicationForm(true)}>
                  Start Your Volunteer Journey
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default VolunteerPage;
