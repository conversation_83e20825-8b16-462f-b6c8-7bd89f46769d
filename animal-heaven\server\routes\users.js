const express = require('express');
const User = require('../models/User');
const { auth } = require('../middleware/auth');

const router = express.Router();

// Get user's saved pets
router.get('/saved-pets', auth, async (req, res) => {
  try {
    const user = await User.findById(req.userId).populate('savedPets');
    res.json({ savedPets: user.savedPets });
  } catch (error) {
    console.error('Get saved pets error:', error);
    res.status(500).json({ message: 'Server error while fetching saved pets' });
  }
});

// Update user preferences
router.put('/preferences', auth, async (req, res) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.userId,
      { preferences: req.body },
      { new: true, runValidators: true }
    ).select('-password');

    res.json({ message: 'Preferences updated successfully', user });
  } catch (error) {
    console.error('Update preferences error:', error);
    res.status(500).json({ message: 'Server error while updating preferences' });
  }
});

module.exports = router;
