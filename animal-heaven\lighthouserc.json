{"ci": {"collect": {"numberOfRuns": 3, "settings": {"chromeFlags": "--no-sandbox --disable-dev-shm-usage", "preset": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}}}, "assert": {"assertions": {"categories:performance": ["warn", {"minScore": 0.8}], "categories:accessibility": ["error", {"minScore": 0.9}], "categories:best-practices": ["warn", {"minScore": 0.8}], "categories:seo": ["warn", {"minScore": 0.8}], "first-contentful-paint": ["warn", {"maxNumericValue": 2000}], "largest-contentful-paint": ["warn", {"maxNumericValue": 2500}], "cumulative-layout-shift": ["warn", {"maxNumericValue": 0.1}], "total-blocking-time": ["warn", {"maxNumericValue": 300}], "speed-index": ["warn", {"maxNumericValue": 3000}], "interactive": ["warn", {"maxNumericValue": 3000}]}}, "upload": {"target": "temporary-public-storage"}}}