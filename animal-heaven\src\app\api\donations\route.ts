import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';

// GET /api/donations - Get donations with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // In real app, validate authentication and authorization
    // const user = await validateAuthToken(request);
    // if (!user) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    const filters = {
      userId: searchParams.get('userId') || undefined,
      type: searchParams.get('type') || undefined,
      status: searchParams.get('status') || undefined,
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: parseInt(searchParams.get('offset') || '0')
    };

    // If not admin/staff, only show user's own donations
    // if (user.role !== 'admin' && user.role !== 'staff') {
    //   filters.userId = user.id;
    // }

    const result = await db.getDonations(filters);
    
    return NextResponse.json({
      success: true,
      data: result.donations,
      pagination: {
        total: result.total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: result.total > filters.offset + filters.limit
      }
    });
  } catch (error) {
    console.error('Error fetching donations:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch donations' },
      { status: 500 }
    );
  }
}

// POST /api/donations - Create a new donation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['amount', 'type', 'purpose', 'donorInfo'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Validate amount
    if (typeof body.amount !== 'number' || body.amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Amount must be a positive number' },
        { status: 400 }
      );
    }

    // Validate donation type
    const validTypes = ['one-time', 'monthly', 'sponsorship'];
    if (!validTypes.includes(body.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid donation type' },
        { status: 400 }
      );
    }

    // Validate purpose
    const validPurposes = ['general', 'medical', 'food', 'shelter', 'specific-pet'];
    if (!validPurposes.includes(body.purpose)) {
      return NextResponse.json(
        { success: false, error: 'Invalid donation purpose' },
        { status: 400 }
      );
    }

    // If sponsoring specific pet, validate pet exists
    if (body.purpose === 'specific-pet') {
      if (!body.petId) {
        return NextResponse.json(
          { success: false, error: 'Pet ID required for specific pet donations' },
          { status: 400 }
        );
      }

      const pet = await db.getPetById(body.petId);
      if (!pet) {
        return NextResponse.json(
          { success: false, error: 'Pet not found' },
          { status: 404 }
        );
      }
    }

    // Validate donor info
    const donorInfo = body.donorInfo;
    if (!donorInfo.name || !donorInfo.email) {
      return NextResponse.json(
        { success: false, error: 'Donor name and email are required' },
        { status: 400 }
      );
    }

    // Create donation
    const donationData = {
      userId: body.userId || undefined,
      amount: body.amount,
      type: body.type,
      purpose: body.purpose,
      petId: body.petId || undefined,
      status: 'pending' as const,
      paymentMethod: body.paymentMethod || 'online',
      transactionId: body.transactionId,
      donorInfo: {
        name: donorInfo.name,
        email: donorInfo.email,
        anonymous: donorInfo.anonymous || false
      }
    };

    const donation = await db.createDonation(donationData);

    // In real app, process payment here
    // const paymentResult = await processPayment(donation);
    // if (paymentResult.success) {
    //   await db.updateDonation(donation.id, { 
    //     status: 'completed',
    //     transactionId: paymentResult.transactionId 
    //   });
    // }

    // For demo, mark as completed
    const completedDonation = await db.updateDonation(donation.id, { 
      status: 'completed',
      transactionId: `txn_${Date.now()}`
    });
    
    return NextResponse.json({
      success: true,
      data: completedDonation || donation,
      message: 'Donation processed successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error processing donation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process donation' },
      { status: 500 }
    );
  }
}

// PUT /api/donations - Update donation status (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In real app, validate admin authentication
    // const user = await validateAuthToken(request);
    // if (!user || (user.role !== 'admin' && user.role !== 'staff')) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    if (!body.id || !body.status) {
      return NextResponse.json(
        { success: false, error: 'Donation ID and status are required' },
        { status: 400 }
      );
    }

    const validStatuses = ['pending', 'completed', 'failed', 'refunded'];
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid status' },
        { status: 400 }
      );
    }

    const updatedDonation = await db.updateDonation(body.id, {
      status: body.status,
      transactionId: body.transactionId
    });

    if (!updatedDonation) {
      return NextResponse.json(
        { success: false, error: 'Donation not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updatedDonation,
      message: 'Donation status updated successfully'
    });
  } catch (error) {
    console.error('Error updating donation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update donation' },
      { status: 500 }
    );
  }
}
