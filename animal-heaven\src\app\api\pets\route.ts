import { NextRequest, NextResponse } from 'next/server';
import { db, Pet } from '@/lib/database';

// GET /api/pets - Get all pets with optional filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters = {
      type: searchParams.get('type') || undefined,
      size: searchParams.get('size') || undefined,
      age: searchParams.get('age') || undefined,
      location: searchParams.get('location') || undefined,
      status: searchParams.get('status') || 'available',
      limit: parseInt(searchParams.get('limit') || '20'),
      offset: parseInt(searchParams.get('offset') || '0')
    };

    const result = await db.getPets(filters);
    
    return NextResponse.json({
      success: true,
      data: result.pets,
      pagination: {
        total: result.total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: result.total > filters.offset + filters.limit
      }
    });
  } catch (error) {
    console.error('Error fetching pets:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pets' },
      { status: 500 }
    );
  }
}

// POST /api/pets - Create a new pet (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In real app, validate user authentication and admin role
    // const user = await validateAuthToken(request);
    // if (!user || (user.role !== 'admin' && user.role !== 'staff')) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    // Validate required fields
    const requiredFields = ['name', 'type', 'breed', 'age', 'gender', 'size', 'description'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        );
      }
    }

    // Create pet with default values
    const petData = {
      name: body.name,
      type: body.type,
      breed: body.breed,
      age: body.age,
      gender: body.gender,
      size: body.size,
      color: body.color || 'Unknown',
      description: body.description,
      personality: body.personality || [],
      images: body.images || [],
      status: body.status || 'available',
      location: body.location || {
        shelter: 'Animal Heaven Main',
        city: 'Mumbai',
        state: 'Maharashtra'
      },
      adoptionFee: body.adoptionFee || 0,
      specialNeeds: body.specialNeeds || false,
      specialNeedsDescription: body.specialNeedsDescription,
      goodWith: body.goodWith || {
        children: true,
        dogs: true,
        cats: true
      },
      medicalHistory: body.medicalHistory || {
        vaccinated: false,
        spayedNeutered: false,
        microchipped: false,
        healthIssues: []
      },
      intakeDate: body.intakeDate || new Date().toISOString(),
      views: 0,
      likes: 0,
      featured: body.featured || false
    };

    const pet = await db.createPet(petData);
    
    return NextResponse.json({
      success: true,
      data: pet,
      message: 'Pet created successfully'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating pet:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create pet' },
      { status: 500 }
    );
  }
}

// PUT /api/pets - Bulk update pets (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In real app, validate admin authentication
    
    if (!Array.isArray(body.updates)) {
      return NextResponse.json(
        { success: false, error: 'Updates must be an array' },
        { status: 400 }
      );
    }

    const results = [];
    for (const update of body.updates) {
      if (!update.id) {
        results.push({ id: null, success: false, error: 'Missing pet ID' });
        continue;
      }

      try {
        const updatedPet = await db.updatePet(update.id, update.data);
        if (updatedPet) {
          results.push({ id: update.id, success: true, data: updatedPet });
        } else {
          results.push({ id: update.id, success: false, error: 'Pet not found' });
        }
      } catch (error) {
        results.push({ id: update.id, success: false, error: 'Update failed' });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed'
    });
  } catch (error) {
    console.error('Error bulk updating pets:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update pets' },
      { status: 500 }
    );
  }
}

// DELETE /api/pets - Bulk delete pets (admin only)
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    
    // In real app, validate admin authentication
    
    if (!Array.isArray(body.ids)) {
      return NextResponse.json(
        { success: false, error: 'IDs must be an array' },
        { status: 400 }
      );
    }

    const results = [];
    for (const id of body.ids) {
      try {
        const deleted = await db.deletePet(id);
        results.push({ id, success: deleted });
      } catch (error) {
        results.push({ id, success: false, error: 'Delete failed' });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk delete completed'
    });
  } catch (error) {
    console.error('Error bulk deleting pets:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete pets' },
      { status: 500 }
    );
  }
}
