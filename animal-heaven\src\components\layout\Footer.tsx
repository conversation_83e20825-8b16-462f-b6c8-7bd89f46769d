'use client';

import React from 'react';
import Link from 'next/link';
import {
  Heart,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  PawPrint
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  const footerSections = [
    {
      title: 'Quick Links',
      links: [
        { href: '/adopt', label: 'Adopt a Pet' },
        { href: '/volunteer', label: 'Volunteer' },
        { href: '/donate', label: 'Donate' },
        { href: '/resources', label: 'Pet Care Resources' },
        { href: '/gallery', label: 'Happy Tails Gallery' },
      ]
    },
    {
      title: 'About Us',
      links: [
        { href: '/about', label: 'Our Story' },
        { href: '/about/team', label: 'Meet the Team' },
        { href: '/about/mission', label: 'Our Mission' },
        { href: '/about/partners', label: 'Partners' },
        { href: '/contact', label: 'Contact Us' },
      ]
    },
    {
      title: 'Support',
      links: [
        { href: '/help', label: 'Help Center' },
        { href: '/faq', label: 'FAQ' },
        { href: '/adoption-process', label: 'Adoption Process' },
        { href: '/emergency', label: 'Emergency Helpline' },
        { href: '/feedback', label: 'Feedback' },
      ]
    },
    {
      title: 'Legal',
      links: [
        { href: '/privacy', label: 'Privacy Policy' },
        { href: '/terms', label: 'Terms of Service' },
        { href: '/cookies', label: 'Cookie Policy' },
        { href: '/disclaimer', label: 'Disclaimer' },
      ]
    }
  ];

  const socialLinks = [
    { href: 'https://facebook.com', icon: Facebook, label: 'Facebook' },
    { href: 'https://instagram.com', icon: Instagram, label: 'Instagram' },
    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },
    { href: 'https://youtube.com', icon: Youtube, label: 'YouTube' },
  ];

  return (
    <footer className="bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-500 rounded-full flex items-center justify-center">
                <PawPrint className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="font-primary text-xl font-bold text-gradient">
                  Animal Heaven
                </h3>
                <p className="text-sm text-neutral-400">Find Your Perfect Companion</p>
              </div>
            </div>
            
            <p className="text-neutral-300 mb-6 leading-relaxed">
              Connecting loving families with pets in need. Every adoption saves a life and 
              creates a forever bond filled with unconditional love and joy.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm text-neutral-300">
                <Phone className="w-4 h-4 text-orange-400" />
                <span>+91 98765 43210</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-neutral-300">
                <Mail className="w-4 h-4 text-orange-400" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-neutral-300">
                <MapPin className="w-4 h-4 text-orange-400" />
                <span>123 Pet Street, Animal City, AC 12345</span>
              </div>
            </div>

            {/* Emergency Helpline */}
            <div className="mt-6 p-4 bg-red-900/30 border border-red-700/50 rounded-lg">
              <h4 className="font-semibold text-red-300 mb-2">24/7 Emergency Helpline</h4>
              <p className="text-red-200 text-lg font-bold">+91 98765 00000</p>
              <p className="text-red-300 text-sm">For animal emergencies and rescue</p>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title}>
              <h4 className="font-primary font-semibold text-white mb-4">
                {section.title}
              </h4>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-neutral-300 hover:text-orange-400 transition-colors text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Subscription */}
        <div className="mt-12 pt-8 border-t border-neutral-700">
          <div className="max-w-md mx-auto text-center lg:text-left lg:max-w-none lg:flex lg:items-center lg:justify-between">
            <div className="lg:flex-1">
              <h4 className="font-primary text-lg font-semibold text-white mb-2">
                Stay Updated with Animal Heaven
              </h4>
              <p className="text-neutral-300 text-sm mb-4 lg:mb-0">
                Get the latest news about adoptable pets, success stories, and events.
              </p>
            </div>
            
            <div className="lg:ml-8 lg:flex-shrink-0">
              <div className="flex flex-col sm:flex-row gap-2 max-w-md">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 bg-neutral-800 border-neutral-600 text-white placeholder:text-neutral-400"
                />
                <Button variant="primary" className="whitespace-nowrap">
                  Subscribe
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Social Links */}
        <div className="mt-8 pt-8 border-t border-neutral-700">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="flex space-x-4 mb-4 sm:mb-0">
              {socialLinks.map((social) => {
                const Icon = social.icon;
                return (
                  <a
                    key={social.label}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-neutral-800 hover:bg-orange-600 rounded-full flex items-center justify-center transition-colors group"
                    aria-label={social.label}
                  >
                    <Icon className="w-5 h-5 text-neutral-400 group-hover:text-white" />
                  </a>
                );
              })}
            </div>

            <div className="flex items-center space-x-4 text-sm text-neutral-400">
              <span>Made with</span>
              <Heart className="w-4 h-4 text-red-500 animate-pulse" />
              <span>for animals in need</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-neutral-900 border-t border-neutral-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-neutral-400">
            <p>
              © {currentYear} Animal Heaven. All rights reserved.
            </p>
            <p className="mt-2 sm:mt-0">
              Registered Non-Profit Organization | License: NPO-2024-001
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export { Footer };
