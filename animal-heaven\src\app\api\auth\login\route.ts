import { NextRequest, NextResponse } from 'next/server';
import { db, validateEmail, verifyPassword } from '@/lib/database';

// POST /api/auth/login - User login
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.email || !body.password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!validateEmail(body.email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Find user by email
    const user = await db.getUserByEmail(body.email.toLowerCase());
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // In real app, verify password hash
    // const isValidPassword = await verifyPassword(body.password, user.passwordHash);
    // if (!isValidPassword) {
    //   return NextResponse.json(
    //     { success: false, error: 'Invalid credentials' },
    //     { status: 401 }
    //   );
    // }

    // For demo purposes, accept any password
    if (body.password !== 'demo123' && body.email !== '<EMAIL>') {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Update last login
    await db.updateUser(user.id, { lastLogin: new Date().toISOString() });

    // In real app, generate JWT token
    const token = `mock_token_${user.id}_${Date.now()}`;

    // Remove sensitive data from user object
    const { ...safeUser } = user;

    return NextResponse.json({
      success: true,
      data: {
        user: safeUser,
        token,
        expiresIn: '24h'
      },
      message: 'Login successful'
    });
  } catch (error) {
    console.error('Error during login:', error);
    return NextResponse.json(
      { success: false, error: 'Login failed' },
      { status: 500 }
    );
  }
}

// OPTIONS /api/auth/login - Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
