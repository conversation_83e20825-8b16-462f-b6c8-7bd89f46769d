{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  isLoading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    isLoading = false,\n    leftIcon,\n    rightIcon,\n    children, \n    disabled,\n    ...props \n  }, ref) => {\n    const baseStyles = `\n      inline-flex items-center justify-center gap-2 rounded-lg font-medium \n      transition-all duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed\n      active:scale-95 hover:shadow-md\n    `;\n\n    const variants = {\n      primary: `\n        bg-gradient-to-r from-orange-500 to-orange-600 text-white \n        hover:from-orange-600 hover:to-orange-700 shadow-warm\n      `,\n      secondary: `\n        bg-gradient-to-r from-green-500 to-green-600 text-white \n        hover:from-green-600 hover:to-green-700\n      `,\n      outline: `\n        border-2 border-orange-500 text-orange-600 bg-transparent \n        hover:bg-orange-50 hover:border-orange-600\n      `,\n      ghost: `\n        text-neutral-700 bg-transparent hover:bg-neutral-100\n      `,\n      danger: `\n        bg-gradient-to-r from-red-500 to-red-600 text-white \n        hover:from-red-600 hover:to-red-700\n      `\n    };\n\n    const sizes = {\n      sm: 'px-3 py-1.5 text-sm',\n      md: 'px-4 py-2 text-base',\n      lg: 'px-6 py-3 text-lg',\n      xl: 'px-8 py-4 text-xl'\n    };\n\n    return (\n      <button\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading ? (\n          <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent\" />\n        ) : leftIcon}\n        {children}\n        {!isLoading && rightIcon}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAaA,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,QAUG;QAVF,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,aAAc;IAMpB,MAAM,WAAW;QACf,SAAU;QAIV,WAAY;QAIZ,SAAU;QAIV,OAAQ;QAGR,QAAS;IAIX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,0BACC,6LAAC;gBAAI,WAAU;;;;;2DACb;YACH;YACA,CAAC,aAAa;;;;;;;AAGrB;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'elevated' | 'outlined' | 'warm';\n  padding?: 'none' | 'sm' | 'md' | 'lg';\n  rounded?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ \n    className, \n    variant = 'default', \n    padding = 'md',\n    rounded = 'lg',\n    children, \n    ...props \n  }, ref) => {\n    const baseStyles = `\n      bg-white border transition-all duration-200\n    `;\n\n    const variants = {\n      default: 'border-neutral-200 shadow-sm hover:shadow-md',\n      elevated: 'border-neutral-200 shadow-lg hover:shadow-xl',\n      outlined: 'border-2 border-neutral-300 shadow-none hover:border-orange-300',\n      warm: 'border-orange-200 bg-gradient-to-br from-orange-50 to-amber-50 shadow-warm hover:shadow-lg'\n    };\n\n    const paddings = {\n      none: '',\n      sm: 'p-3',\n      md: 'p-4',\n      lg: 'p-6'\n    };\n\n    const roundings = {\n      sm: 'rounded-sm',\n      md: 'rounded-md',\n      lg: 'rounded-lg',\n      xl: 'rounded-xl',\n      '2xl': 'rounded-2xl'\n    };\n\n    return (\n      <div\n        className={cn(\n          baseStyles,\n          variants[variant],\n          paddings[padding],\n          roundings[rounded],\n          className\n        )}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5', className)}\n      {...props}\n    />\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLHeadingElement>>(\n  ({ className, ...props }, ref) => (\n    <h3\n      ref={ref}\n      className={cn('font-primary text-xl font-semibold leading-none tracking-tight text-neutral-900', className)}\n      {...props}\n    />\n  )\n);\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(\n  ({ className, ...props }, ref) => (\n    <p\n      ref={ref}\n      className={cn('text-sm text-neutral-600', className)}\n      {...props}\n    />\n  )\n);\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div ref={ref} className={cn('pt-0', className)} {...props} />\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(\n  ({ className, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center pt-4', className)}\n      {...props}\n    />\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAWA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,QAOG;QAPF,EACC,SAAS,EACT,UAAU,SAAS,EACnB,UAAU,IAAI,EACd,UAAU,IAAI,EACd,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,aAAc;IAIpB,MAAM,WAAW;QACf,SAAS;QACT,UAAU;QACV,UAAU;QACV,MAAM;IACR;IAEA,MAAM,WAAW;QACf,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,QAAQ,EACjB,SAAS,CAAC,QAAQ,EAClB;QAEF,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;;;AAIf,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mFAAmF;QAChG,GAAG,KAAK;;;;;;;;AAIf,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;;AAIf,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;;;AAG9D,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACtB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;;;;;;;AAIf,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  variant?: 'default' | 'warm';\n  inputSize?: 'sm' | 'md' | 'lg';\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n  error?: string;\n  label?: string;\n  helper?: string;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ \n    className, \n    type = 'text',\n    variant = 'default',\n    inputSize = 'md',\n    leftIcon,\n    rightIcon,\n    error,\n    label,\n    helper,\n    id,\n    ...props \n  }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n\n    const baseStyles = `\n      flex w-full rounded-lg border bg-white px-3 py-2 text-sm \n      transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium \n      placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 \n      focus-visible:ring-orange-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed \n      disabled:opacity-50\n    `;\n\n    const variants = {\n      default: `\n        border-neutral-300 hover:border-neutral-400 focus:border-orange-500\n      `,\n      warm: `\n        border-orange-200 bg-orange-50/50 hover:border-orange-300 focus:border-orange-500\n      `\n    };\n\n    const sizes = {\n      sm: 'h-8 px-2 text-xs',\n      md: 'h-10 px-3 text-sm',\n      lg: 'h-12 px-4 text-base'\n    };\n\n    const hasError = !!error;\n    const errorStyles = hasError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '';\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label \n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-neutral-700 mb-1\"\n          >\n            {label}\n          </label>\n        )}\n        \n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500\">\n              {leftIcon}\n            </div>\n          )}\n          \n          <input\n            type={type}\n            id={inputId}\n            className={cn(\n              baseStyles,\n              variants[variant],\n              sizes[inputSize],\n              leftIcon && 'pl-10',\n              rightIcon && 'pr-10',\n              errorStyles,\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          \n          {rightIcon && (\n            <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500\">\n              {rightIcon}\n            </div>\n          )}\n        </div>\n\n        {error && (\n          <p className=\"mt-1 text-xs text-red-600\">{error}</p>\n        )}\n        \n        {helper && !error && (\n          <p className=\"mt-1 text-xs text-neutral-500\">{helper}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAYG;QAZF,EACC,SAAS,EACT,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,QAAQ,EACR,SAAS,EACT,KAAK,EACL,KAAK,EACL,MAAM,EACN,EAAE,EACF,GAAG,OACJ;IACC,MAAM,UAAU,MAAM,AAAC,SAAgD,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IAEpE,MAAM,aAAc;IAQpB,MAAM,WAAW;QACf,SAAU;QAGV,MAAO;IAGT;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,WAAW,CAAC,CAAC;IACnB,MAAM,cAAc,WAAW,2DAA2D;IAE1F,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;0BAET;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;kCAIL,6LAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,UAAU,EAChB,YAAY,SACZ,aAAa,SACb,aACA;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAGV,2BACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;YAKN,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;YAG3C,UAAU,CAAC,uBACV,6LAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'outline';\n  size?: 'sm' | 'md' | 'lg';\n  icon?: React.ReactNode;\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  ({ \n    className, \n    variant = 'default', \n    size = 'md',\n    icon,\n    children, \n    ...props \n  }, ref) => {\n    const baseStyles = `\n      inline-flex items-center gap-1 rounded-full font-medium transition-colors\n    `;\n\n    const variants = {\n      default: 'bg-neutral-100 text-neutral-800 hover:bg-neutral-200',\n      primary: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      secondary: 'bg-green-100 text-green-800 hover:bg-green-200',\n      success: 'bg-emerald-100 text-emerald-800 hover:bg-emerald-200',\n      warning: 'bg-amber-100 text-amber-800 hover:bg-amber-200',\n      danger: 'bg-red-100 text-red-800 hover:bg-red-200',\n      outline: 'border border-neutral-300 text-neutral-700 hover:bg-neutral-50'\n    };\n\n    const sizes = {\n      sm: 'px-2 py-0.5 text-xs',\n      md: 'px-2.5 py-1 text-sm',\n      lg: 'px-3 py-1.5 text-base'\n    };\n\n    return (\n      <div\n        className={cn(\n          baseStyles,\n          variants[variant],\n          sizes[size],\n          className\n        )}\n        ref={ref}\n        {...props}\n      >\n        {icon && <span className=\"flex-shrink-0\">{icon}</span>}\n        {children}\n      </div>\n    );\n  }\n);\n\nBadge.displayName = 'Badge';\n\nexport { Badge };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,QAOG;QAPF,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,IAAI,EACJ,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,aAAc;IAIpB,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX;QAEF,KAAK;QACJ,GAAG,KAAK;;YA<PERSON>,sBAAQ,6LAAC;gBAAK,WAAU;0BAAiB;;;;;;YACzC;;;;;;;AAGP;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Search, Filter, X } from 'lucide-react';\nimport { Input } from '@/components/ui/Input';\nimport { Button } from '@/components/ui/Button';\nimport { Badge } from '@/components/ui/Badge';\nimport { cn } from '@/lib/utils';\n\nexport interface SearchFilters {\n  type?: string;\n  breed?: string;\n  size?: string;\n  age?: string;\n  location?: string;\n  gender?: string;\n}\n\ninterface SearchBarProps {\n  placeholder?: string;\n  onSearch?: (query: string, filters: SearchFilters) => void;\n  onFiltersChange?: (filters: SearchFilters) => void;\n  showFilters?: boolean;\n  className?: string;\n}\n\nconst SearchBar: React.FC<SearchBarProps> = ({\n  placeholder = \"Search for your perfect companion...\",\n  onSearch,\n  onFiltersChange,\n  showFilters = true,\n  className\n}) => {\n  const [query, setQuery] = useState('');\n  const [filters, setFilters] = useState<SearchFilters>({});\n  const [showFilterPanel, setShowFilterPanel] = useState(false);\n\n  const handleSearch = () => {\n    onSearch?.(query, filters);\n  };\n\n  const handleFilterChange = (key: keyof SearchFilters, value: string) => {\n    const newFilters = { ...filters, [key]: value };\n    setFilters(newFilters);\n    onFiltersChange?.(newFilters);\n  };\n\n  const clearFilter = (key: keyof SearchFilters) => {\n    const newFilters = { ...filters };\n    delete newFilters[key];\n    setFilters(newFilters);\n    onFiltersChange?.(newFilters);\n  };\n\n  const clearAllFilters = () => {\n    setFilters({});\n    onFiltersChange?.({});\n  };\n\n  const activeFiltersCount = Object.keys(filters).length;\n\n  return (\n    <div className={cn('w-full max-w-4xl mx-auto', className)}>\n      {/* Main Search Bar */}\n      <div className=\"relative\">\n        <div className=\"flex gap-2\">\n          <div className=\"flex-1\">\n            <Input\n              value={query}\n              onChange={(e) => setQuery(e.target.value)}\n              placeholder={placeholder}\n              leftIcon={<Search className=\"w-4 h-4\" />}\n              inputSize=\"lg\"\n              variant=\"warm\"\n              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n            />\n          </div>\n          \n          {showFilters && (\n            <Button\n              variant={showFilterPanel ? 'primary' : 'outline'}\n              size=\"lg\"\n              onClick={() => setShowFilterPanel(!showFilterPanel)}\n              leftIcon={<Filter className=\"w-4 h-4\" />}\n            >\n              Filters\n              {activeFiltersCount > 0 && (\n                <Badge variant=\"secondary\" size=\"sm\" className=\"ml-1\">\n                  {activeFiltersCount}\n                </Badge>\n              )}\n            </Button>\n          )}\n          \n          <Button\n            variant=\"primary\"\n            size=\"lg\"\n            onClick={handleSearch}\n          >\n            Search\n          </Button>\n        </div>\n\n        {/* Active Filters Display */}\n        {activeFiltersCount > 0 && (\n          <div className=\"mt-3 flex flex-wrap gap-2 items-center\">\n            <span className=\"text-sm text-neutral-600\">Active filters:</span>\n            {Object.entries(filters).map(([key, value]) => (\n              <Badge\n                key={key}\n                variant=\"primary\"\n                className=\"cursor-pointer hover:bg-orange-200\"\n                onClick={() => clearFilter(key as keyof SearchFilters)}\n              >\n                {key}: {value}\n                <X className=\"w-3 h-3 ml-1\" />\n              </Badge>\n            ))}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={clearAllFilters}\n              className=\"text-xs\"\n            >\n              Clear all\n            </Button>\n          </div>\n        )}\n      </div>\n\n      {/* Filter Panel */}\n      {showFilterPanel && (\n        <div className=\"mt-4 p-4 bg-white rounded-lg border border-orange-200 shadow-lg\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {/* Pet Type */}\n            <div>\n              <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                Pet Type\n              </label>\n              <select\n                value={filters.type || ''}\n                onChange={(e) => handleFilterChange('type', e.target.value)}\n                className=\"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n              >\n                <option value=\"\">Any Type</option>\n                <option value=\"dog\">Dog</option>\n                <option value=\"cat\">Cat</option>\n                <option value=\"bird\">Bird</option>\n                <option value=\"rabbit\">Rabbit</option>\n                <option value=\"other\">Other</option>\n              </select>\n            </div>\n\n            {/* Size */}\n            <div>\n              <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                Size\n              </label>\n              <select\n                value={filters.size || ''}\n                onChange={(e) => handleFilterChange('size', e.target.value)}\n                className=\"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n              >\n                <option value=\"\">Any Size</option>\n                <option value=\"small\">Small</option>\n                <option value=\"medium\">Medium</option>\n                <option value=\"large\">Large</option>\n                <option value=\"extra-large\">Extra Large</option>\n              </select>\n            </div>\n\n            {/* Age */}\n            <div>\n              <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                Age\n              </label>\n              <select\n                value={filters.age || ''}\n                onChange={(e) => handleFilterChange('age', e.target.value)}\n                className=\"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n              >\n                <option value=\"\">Any Age</option>\n                <option value=\"0-1\">Puppy/Kitten (0-1 year)</option>\n                <option value=\"1-3\">Young (1-3 years)</option>\n                <option value=\"3-7\">Adult (3-7 years)</option>\n                <option value=\"7-15\">Senior (7+ years)</option>\n              </select>\n            </div>\n\n            {/* Gender */}\n            <div>\n              <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                Gender\n              </label>\n              <select\n                value={filters.gender || ''}\n                onChange={(e) => handleFilterChange('gender', e.target.value)}\n                className=\"w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500\"\n              >\n                <option value=\"\">Any Gender</option>\n                <option value=\"male\">Male</option>\n                <option value=\"female\">Female</option>\n              </select>\n            </div>\n\n            {/* Location */}\n            <div>\n              <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                Location\n              </label>\n              <Input\n                value={filters.location || ''}\n                onChange={(e) => handleFilterChange('location', e.target.value)}\n                placeholder=\"City or State\"\n                variant=\"default\"\n              />\n            </div>\n\n            {/* Breed */}\n            <div>\n              <label className=\"block text-sm font-medium text-neutral-700 mb-2\">\n                Breed\n              </label>\n              <Input\n                value={filters.breed || ''}\n                onChange={(e) => handleFilterChange('breed', e.target.value)}\n                placeholder=\"Enter breed\"\n                variant=\"default\"\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { SearchBar };\nexport type { SearchFilters };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AA0BA,MAAM,YAAsC;QAAC,EAC3C,cAAc,sCAAsC,EACpD,QAAQ,EACR,eAAe,EACf,cAAc,IAAI,EAClB,SAAS,EACV;;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,CAAC;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,eAAe;QACnB,qBAAA,+BAAA,SAAW,OAAO;IACpB;IAEA,MAAM,qBAAqB,CAAC,KAA0B;QACpD,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;QAC9C,WAAW;QACX,4BAAA,sCAAA,gBAAkB;IACpB;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa;YAAE,GAAG,OAAO;QAAC;QAChC,OAAO,UAAU,CAAC,IAAI;QACtB,WAAW;QACX,4BAAA,sCAAA,gBAAkB;IACpB;IAEA,MAAM,kBAAkB;QACtB,WAAW,CAAC;QACZ,4BAAA,sCAAA,gBAAkB,CAAC;IACrB;IAEA,MAAM,qBAAqB,OAAO,IAAI,CAAC,SAAS,MAAM;IAEtD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;;0BAE7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAa;oCACb,wBAAU,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAC5B,WAAU;oCACV,SAAQ;oCACR,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;4BAI3C,6BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,kBAAkB,YAAY;gCACvC,MAAK;gCACL,SAAS,IAAM,mBAAmB,CAAC;gCACnC,wBAAU,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;oCAC7B;oCAEE,qBAAqB,mBACpB,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,MAAK;wCAAK,WAAU;kDAC5C;;;;;;;;;;;;0CAMT,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;0CACV;;;;;;;;;;;;oBAMF,qBAAqB,mBACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA2B;;;;;;4BAC1C,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC;oCAAC,CAAC,KAAK,MAAM;qDACxC,6LAAC,oIAAA,CAAA,QAAK;oCAEJ,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,YAAY;;wCAE1B;wCAAI;wCAAG;sDACR,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;mCANR;;;;;;0CAST,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;YAQN,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkD;;;;;;8CAGnE,6LAAC;oCACC,OAAO,QAAQ,IAAI,IAAI;oCACvB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAK1B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkD;;;;;;8CAGnE,6LAAC;oCACC,OAAO,QAAQ,IAAI,IAAI;oCACvB,UAAU,CAAC,IAAM,mBAAmB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;sDACvB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAc;;;;;;;;;;;;;;;;;;sCAKhC,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkD;;;;;;8CAGnE,6LAAC;oCACC,OAAO,QAAQ,GAAG,IAAI;oCACtB,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;;;;;;;;;;;;;sCAKzB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkD;;;;;;8CAGnE,6LAAC;oCACC,OAAO,QAAQ,MAAM,IAAI;oCACzB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAK3B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkD;;;;;;8CAGnE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAO,QAAQ,QAAQ,IAAI;oCAC3B,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC9D,aAAY;oCACZ,SAAQ;;;;;;;;;;;;sCAKZ,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkD;;;;;;8CAGnE,6LAAC,oIAAA,CAAA,QAAK;oCACJ,OAAO,QAAQ,KAAK,IAAI;oCACxB,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC3D,aAAY;oCACZ,SAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB;GAjNM;KAAA", "debugId": null}}, {"offset": {"line": 925, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Sun, Moon, Monitor, ChevronDown } from 'lucide-react';\nimport { useTheme } from '@/contexts/ThemeContext';\nimport { Button } from './Button';\nimport { cn } from '@/lib/utils';\n\ninterface ThemeToggleProps {\n  variant?: 'button' | 'dropdown';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nconst ThemeToggle: React.FC<ThemeToggleProps> = ({ \n  variant = 'button', \n  size = 'md',\n  className \n}) => {\n  const { theme, actualTheme, setTheme, toggleTheme } = useTheme();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  const themes = [\n    { value: 'light', label: 'Light', icon: Sun },\n    { value: 'dark', label: 'Dark', icon: Moon },\n    { value: 'system', label: 'System', icon: Monitor }\n  ] as const;\n\n  const currentThemeData = themes.find(t => t.value === theme) || themes[0];\n  const CurrentIcon = currentThemeData.icon;\n\n  if (variant === 'button') {\n    return (\n      <Button\n        variant=\"ghost\"\n        size={size}\n        onClick={toggleTheme}\n        className={cn(\n          'relative transition-all duration-200',\n          className\n        )}\n        aria-label={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}\n      >\n        <Sun className={cn(\n          'w-4 h-4 transition-all duration-300',\n          actualTheme === 'dark' ? 'rotate-90 scale-0' : 'rotate-0 scale-100'\n        )} />\n        <Moon className={cn(\n          'absolute w-4 h-4 transition-all duration-300',\n          actualTheme === 'dark' ? 'rotate-0 scale-100' : '-rotate-90 scale-0'\n        )} />\n      </Button>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      <Button\n        variant=\"ghost\"\n        size={size}\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className={cn(\n          'flex items-center gap-2',\n          className\n        )}\n        aria-label=\"Theme selector\"\n        aria-expanded={isDropdownOpen}\n      >\n        <CurrentIcon className=\"w-4 h-4\" />\n        <span className=\"hidden sm:inline\">{currentThemeData.label}</span>\n        <ChevronDown className={cn(\n          'w-3 h-3 transition-transform duration-200',\n          isDropdownOpen && 'rotate-180'\n        )} />\n      </Button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 z-10\" \n            onClick={() => setIsDropdownOpen(false)}\n          />\n          \n          {/* Dropdown */}\n          <div className=\"absolute right-0 top-full mt-2 z-20 min-w-[140px] bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg py-1\">\n            {themes.map((themeOption) => {\n              const Icon = themeOption.icon;\n              const isSelected = theme === themeOption.value;\n              \n              return (\n                <button\n                  key={themeOption.value}\n                  onClick={() => {\n                    setTheme(themeOption.value);\n                    setIsDropdownOpen(false);\n                  }}\n                  className={cn(\n                    'w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors',\n                    'hover:bg-neutral-100 dark:hover:bg-neutral-700',\n                    isSelected && 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'\n                  )}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{themeOption.label}</span>\n                  {isSelected && (\n                    <div className=\"ml-auto w-2 h-2 bg-orange-500 rounded-full\" />\n                  )}\n                </button>\n              );\n            })}\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport { ThemeToggle };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAcA,MAAM,cAA0C;QAAC,EAC/C,UAAU,QAAQ,EAClB,OAAO,IAAI,EACX,SAAS,EACV;;IACC,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS;QACb;YAAE,OAAO;YAAS,OAAO;YAAS,MAAM,mMAAA,CAAA,MAAG;QAAC;QAC5C;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC3C;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM,2MAAA,CAAA,UAAO;QAAC;KACnD;IAED,MAAM,mBAAmB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,UAAU,MAAM,CAAC,EAAE;IACzE,MAAM,cAAc,iBAAiB,IAAI;IAEzC,IAAI,YAAY,UAAU;QACxB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAM;YACN,SAAS;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA;YAEF,cAAY,AAAC,aAAuD,OAA3C,gBAAgB,UAAU,SAAS,SAAQ;;8BAEpE,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uCACA,gBAAgB,SAAS,sBAAsB;;;;;;8BAEjD,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,gDACA,gBAAgB,SAAS,uBAAuB;;;;;;;;;;;;IAIxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAM;gBACN,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA;gBAEF,cAAW;gBACX,iBAAe;;kCAEf,6LAAC;wBAAY,WAAU;;;;;;kCACvB,6LAAC;wBAAK,WAAU;kCAAoB,iBAAiB,KAAK;;;;;;kCAC1D,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACvB,6CACA,kBAAkB;;;;;;;;;;;;YAIrB,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC;4BACX,MAAM,OAAO,YAAY,IAAI;4BAC7B,MAAM,aAAa,UAAU,YAAY,KAAK;4BAE9C,qBACE,6LAAC;gCAEC,SAAS;oCACP,SAAS,YAAY,KAAK;oCAC1B,kBAAkB;gCACpB;gCACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA,kDACA,cAAc;;kDAGhB,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,YAAY,KAAK;;;;;;oCACvB,4BACC,6LAAC;wCAAI,WAAU;;;;;;;+BAdZ,YAAY,KAAK;;;;;wBAkB5B;;;;;;;;;;;;;;AAMZ;GAtGM;;QAKkD,mIAAA,CAAA,WAAQ;;;KAL1D", "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/LanguageSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Globe, ChevronDown } from 'lucide-react';\nimport { useLanguage } from '@/contexts/LanguageContext';\nimport { Button } from './Button';\nimport { cn } from '@/lib/utils';\n\ninterface LanguageSelectorProps {\n  variant?: 'button' | 'dropdown';\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nconst languages = [\n  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },\n  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳' },\n  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },\n  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' }\n] as const;\n\nconst LanguageSelector: React.FC<LanguageSelectorProps> = ({ \n  variant = 'dropdown', \n  size = 'md',\n  className \n}) => {\n  const { language, setLanguage } = useLanguage();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  const currentLanguage = languages.find(lang => lang.code === language) || languages[0];\n\n  if (variant === 'button') {\n    return (\n      <Button\n        variant=\"ghost\"\n        size={size}\n        className={cn('flex items-center gap-2', className)}\n        aria-label=\"Change language\"\n      >\n        <Globe className=\"w-4 h-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.nativeName}</span>\n      </Button>\n    );\n  }\n\n  return (\n    <div className=\"relative\">\n      <Button\n        variant=\"ghost\"\n        size={size}\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className={cn('flex items-center gap-2', className)}\n        aria-label=\"Language selector\"\n        aria-expanded={isDropdownOpen}\n      >\n        <Globe className=\"w-4 h-4\" />\n        <span className=\"hidden sm:inline\">{currentLanguage.nativeName}</span>\n        <ChevronDown className={cn(\n          'w-3 h-3 transition-transform duration-200',\n          isDropdownOpen && 'rotate-180'\n        )} />\n      </Button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 z-10\" \n            onClick={() => setIsDropdownOpen(false)}\n          />\n          \n          {/* Dropdown */}\n          <div className=\"absolute right-0 top-full mt-2 z-20 min-w-[160px] bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg py-1\">\n            {languages.map((lang) => {\n              const isSelected = language === lang.code;\n              \n              return (\n                <button\n                  key={lang.code}\n                  onClick={() => {\n                    setLanguage(lang.code as any);\n                    setIsDropdownOpen(false);\n                  }}\n                  className={cn(\n                    'w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors',\n                    'hover:bg-neutral-100 dark:hover:bg-neutral-700',\n                    isSelected && 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'\n                  )}\n                >\n                  <span className=\"text-lg\">{lang.flag}</span>\n                  <div className=\"flex-1 text-left\">\n                    <div className=\"font-medium\">{lang.nativeName}</div>\n                    <div className=\"text-xs text-neutral-500 dark:text-neutral-400\">\n                      {lang.name}\n                    </div>\n                  </div>\n                  {isSelected && (\n                    <div className=\"w-2 h-2 bg-orange-500 rounded-full\" />\n                  )}\n                </button>\n              );\n            })}\n          </div>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport { LanguageSelector };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAcA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;QAAW,MAAM;IAAO;IACnE;QAAE,MAAM;QAAM,MAAM;QAAS,YAAY;QAAU,MAAM;IAAO;IAChE;QAAE,MAAM;QAAM,MAAM;QAAW,YAAY;QAAW,MAAM;IAAO;IACnE;QAAE,MAAM;QAAM,MAAM;QAAU,YAAY;QAAY,MAAM;IAAO;CACpE;AAED,MAAM,mBAAoD;QAAC,EACzD,UAAU,UAAU,EACpB,OAAO,IAAI,EACX,SAAS,EACV;;IACC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAC5C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,aAAa,SAAS,CAAC,EAAE;IAEtF,IAAI,YAAY,UAAU;QACxB,qBACE,6LAAC,qIAAA,CAAA,SAAM;YACL,SAAQ;YACR,MAAM;YACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;YACzC,cAAW;;8BAEX,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;8BACjB,6LAAC;oBAAK,WAAU;8BAAoB,gBAAgB,UAAU;;;;;;;;;;;;IAGpE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAM;gBACN,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;gBACzC,cAAW;gBACX,iBAAe;;kCAEf,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAK,WAAU;kCAAoB,gBAAgB,UAAU;;;;;;kCAC9D,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACvB,6CACA,kBAAkB;;;;;;;;;;;;YAIrB,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC;4BACd,MAAM,aAAa,aAAa,KAAK,IAAI;4BAEzC,qBACE,6LAAC;gCAEC,SAAS;oCACP,YAAY,KAAK,IAAI;oCACrB,kBAAkB;gCACpB;gCACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA,kDACA,cAAc;;kDAGhB,6LAAC;wCAAK,WAAU;kDAAW,KAAK,IAAI;;;;;;kDACpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAe,KAAK,UAAU;;;;;;0DAC7C,6LAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;;;;;;;oCAGb,4BACC,6LAAC;wCAAI,WAAU;;;;;;;+BAnBZ,KAAK,IAAI;;;;;wBAuBpB;;;;;;;;;;;;;;AAMZ;GAtFM;;QAK8B,sIAAA,CAAA,cAAW;;;KALzC", "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/ui/SkipToContent.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface SkipToContentProps {\n  targetId?: string;\n  className?: string;\n}\n\nconst SkipToContent: React.FC<SkipToContentProps> = ({ \n  targetId = 'main-content',\n  className \n}) => {\n  const handleSkip = (e: React.MouseEvent<HTMLAnchorElement>) => {\n    e.preventDefault();\n    const target = document.getElementById(targetId);\n    if (target) {\n      target.focus();\n      target.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <a\n      href={`#${targetId}`}\n      onClick={handleSkip}\n      className={cn(\n        'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50',\n        'bg-orange-600 text-white px-4 py-2 rounded-lg font-medium',\n        'focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2',\n        'transition-all duration-200',\n        className\n      )}\n    >\n      Skip to main content\n    </a>\n  );\n};\n\nexport { SkipToContent };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAUA,MAAM,gBAA8C;QAAC,EACnD,WAAW,cAAc,EACzB,SAAS,EACV;IACC,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,MAAM,SAAS,SAAS,cAAc,CAAC;QACvC,IAAI,QAAQ;YACV,OAAO,KAAK;YACZ,OAAO,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC7C;IACF;IAEA,qBACE,6LAAC;QACC,MAAM,AAAC,IAAY,OAAT;QACV,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,6DACA,6EACA,+BACA;kBAEH;;;;;;AAIL;KA5BM", "debugId": null}}, {"offset": {"line": 1381, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/layout/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  Heart,\n  User,\n  Menu,\n  X,\n  Home,\n  Search,\n  Users,\n  Gift,\n  BookOpen,\n  Camera,\n  Info,\n  Phone,\n  LogIn,\n  UserPlus,\n  LogOut,\n  Settings\n} from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { Badge } from '@/components/ui/Badge';\nimport { ThemeToggle } from '@/components/ui/ThemeToggle';\nimport { LanguageSelector } from '@/components/ui/LanguageSelector';\nimport { cn } from '@/lib/utils';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface NavigationProps {\n  isAuthenticated?: boolean;\n  userRole?: 'user' | 'volunteer' | 'admin' | 'shelter-staff';\n  savedPetsCount?: number;\n}\n\nconst Navigation: React.FC<NavigationProps> = ({\n  isAuthenticated: propIsAuthenticated,\n  userRole = 'user',\n  savedPetsCount: propSavedPetsCount\n}) => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const pathname = usePathname();\n  const { user, logout, isAuthenticated: authIsAuthenticated } = useAuth();\n\n  // Use auth context values if available, otherwise fall back to props\n  const isAuthenticated = authIsAuthenticated || propIsAuthenticated || false;\n  const savedPetsCount = propSavedPetsCount || 0;\n\n  const navigationItems = [\n    { href: '/', label: 'Home', icon: Home },\n    { href: '/adopt', label: 'Adopt', icon: Search },\n    { href: '/volunteer', label: 'Volunteer', icon: Users },\n    { href: '/donate', label: 'Donate', icon: Gift },\n    { href: '/resources', label: 'Resources', icon: BookOpen },\n    { href: '/gallery', label: 'Gallery', icon: Camera },\n    { href: '/about', label: 'About', icon: Info },\n    { href: '/contact', label: 'Contact', icon: Phone },\n  ];\n\n  const isActivePath = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <nav className=\"bg-white/95 backdrop-blur-sm border-b border-orange-100 sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 group\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform\">\n              <span className=\"text-white font-bold text-lg\">🐾</span>\n            </div>\n            <div>\n              <h1 className=\"font-primary text-xl font-bold text-gradient\">\n                Animal Heaven\n              </h1>\n              <p className=\"text-xs text-neutral-600 -mt-1\">Find Your Perfect Companion</p>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = isActivePath(item.href);\n              \n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    'flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',\n                    isActive\n                      ? 'bg-orange-100 text-orange-700 shadow-sm'\n                      : 'text-neutral-700 hover:bg-orange-50 hover:text-orange-600'\n                  )}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-3\">\n            {isAuthenticated ? (\n              <>\n                {/* Saved Pets */}\n                <Link href=\"/dashboard\">\n                  <Button variant=\"ghost\" size=\"sm\" className=\"relative\">\n                    <Heart className=\"w-4 h-4\" />\n                    {savedPetsCount > 0 && (\n                      <Badge\n                        variant=\"primary\"\n                        size=\"sm\"\n                        className=\"absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center text-xs\"\n                      >\n                        {savedPetsCount > 99 ? '99+' : savedPetsCount}\n                      </Badge>\n                    )}\n                  </Button>\n                </Link>\n\n                {/* User Menu */}\n                <Link href=\"/dashboard\">\n                  <Button variant=\"outline\" size=\"sm\">\n                    <User className=\"w-4 h-4\" />\n                    <span className=\"hidden sm:inline ml-1\">\n                      {user ? user.firstName : 'Dashboard'}\n                    </span>\n                  </Button>\n                </Link>\n\n                {/* Language Selector */}\n                <LanguageSelector variant=\"dropdown\" size=\"sm\" />\n\n                {/* Theme Toggle */}\n                <ThemeToggle variant=\"dropdown\" size=\"sm\" />\n\n                {/* Logout */}\n                <Button variant=\"ghost\" size=\"sm\" onClick={logout}>\n                  <LogOut className=\"w-4 h-4\" />\n                  <span className=\"hidden sm:inline ml-1\">Logout</span>\n                </Button>\n\n                {/* Admin Panel (if admin) */}\n                {(user?.role === 'admin' || user?.role === 'staff') && (\n                  <Link href=\"/admin\">\n                    <Button variant=\"secondary\" size=\"sm\">\n                      Admin\n                    </Button>\n                  </Link>\n                )}\n              </>\n            ) : (\n              <>\n                {/* Language Selector */}\n                <LanguageSelector variant=\"dropdown\" size=\"sm\" />\n\n                {/* Theme Toggle */}\n                <ThemeToggle variant=\"dropdown\" size=\"sm\" />\n\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    <LogIn className=\"w-4 h-4\" />\n                    <span className=\"hidden sm:inline ml-1\">Login</span>\n                  </Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button variant=\"primary\" size=\"sm\">\n                    <UserPlus className=\"w-4 h-4\" />\n                    <span className=\"hidden sm:inline ml-1\">Sign Up</span>\n                  </Button>\n                </Link>\n              </>\n            )}\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"lg:hidden\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-5 h-5\" />\n              ) : (\n                <Menu className=\"w-5 h-5\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden border-t border-orange-100 py-4\">\n            <div className=\"space-y-2\">\n              {navigationItems.map((item) => {\n                const Icon = item.icon;\n                const isActive = isActivePath(item.href);\n                \n                return (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className={cn(\n                      'flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200',\n                      isActive\n                        ? 'bg-orange-100 text-orange-700'\n                        : 'text-neutral-700 hover:bg-orange-50 hover:text-orange-600'\n                    )}\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <Icon className=\"w-5 h-5\" />\n                    <span>{item.label}</span>\n                  </Link>\n                );\n              })}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport { Navigation };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBA;AACA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;;;;;AAoCA,MAAM,aAAwC;QAAC,EAC7C,iBAAiB,mBAAmB,EACpC,WAAW,MAAM,EACjB,gBAAgB,kBAAkB,EACnC;;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,iBAAiB,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAErE,qEAAqE;IACrE,MAAM,kBAAkB,uBAAuB,uBAAuB;IACtE,MAAM,iBAAiB,sBAAsB;IAE7C,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,sMAAA,CAAA,OAAI;QAAC;QACvC;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC/C;YAAE,MAAM;YAAc,OAAO;YAAa,MAAM,uMAAA,CAAA,QAAK;QAAC;QACtD;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC/C;YAAE,MAAM;YAAc,OAAO;YAAa,MAAM,iNAAA,CAAA,WAAQ;QAAC;QACzD;YAAE,MAAM;YAAY,OAAO;YAAW,MAAM,yMAAA,CAAA,SAAM;QAAC;QACnD;YAAE,MAAM;YAAU,OAAO;YAAS,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC7C;YAAE,MAAM;YAAY,OAAO;YAAW,MAAM,uMAAA,CAAA,QAAK;QAAC;KACnD;IAED,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAG7D,6LAAC;4CAAE,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oGACA,WACI,4CACA;;sDAGN,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;sDAAM,KAAK,KAAK;;;;;;;mCAVZ,KAAK,IAAI;;;;;4BAapB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;gCACZ,gCACC;;sDAEE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAK,WAAU;;kEAC1C,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB,iBAAiB,mBAChB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAET,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;;;;;;sDAOvC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,SAAS,GAAG;;;;;;;;;;;;;;;;;sDAM/B,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAQ;4CAAW,MAAK;;;;;;sDAG1C,6LAAC,0IAAA,CAAA,cAAW;4CAAC,SAAQ;4CAAW,MAAK;;;;;;sDAGrC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,SAAS;;8DACzC,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;wCAIzC,CAAC,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,WAAW,CAAA,iBAAA,2BAAA,KAAM,IAAI,MAAK,OAAO,mBAChD,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAY,MAAK;0DAAK;;;;;;;;;;;;iEAO5C;;sDAEE,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAQ;4CAAW,MAAK;;;;;;sDAG1C,6LAAC,0IAAA,CAAA,cAAW;4CAAC,SAAQ;4CAAW,MAAK;;;;;;sDAErC,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;;kEAC3B,6LAAC,2MAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;sDAG5C,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;;kEAC7B,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;8CAOhD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEnC,iCACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;iGAEb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAOvB,kCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC;4BACpB,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sGACA,WACI,kCACA;gCAEN,SAAS,IAAM,oBAAoB;;kDAEnC,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,KAAK,KAAK;;;;;;;+BAXZ,KAAK,IAAI;;;;;wBAcpB;;;;;;;;;;;;;;;;;;;;;;AAOd;GAjMM;;QAMa,qIAAA,CAAA,cAAW;QACmC,kIAAA,CAAA,UAAO;;;KAPlE", "debugId": null}}, {"offset": {"line": 1895, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport {\n  Heart,\n  Mail,\n  Phone,\n  MapPin,\n  Facebook,\n  Instagram,\n  Twitter,\n  Youtube,\n  PawPrint\n} from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { Input } from '@/components/ui/Input';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: 'Quick Links',\n      links: [\n        { href: '/adopt', label: 'Adopt a Pet' },\n        { href: '/volunteer', label: 'Volunteer' },\n        { href: '/donate', label: 'Donate' },\n        { href: '/resources', label: 'Pet Care Resources' },\n        { href: '/gallery', label: 'Happy Tails Gallery' },\n      ]\n    },\n    {\n      title: 'About Us',\n      links: [\n        { href: '/about', label: 'Our Story' },\n        { href: '/about/team', label: 'Meet the Team' },\n        { href: '/about/mission', label: 'Our Mission' },\n        { href: '/about/partners', label: 'Partners' },\n        { href: '/contact', label: 'Contact Us' },\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { href: '/help', label: 'Help Center' },\n        { href: '/faq', label: 'FAQ' },\n        { href: '/adoption-process', label: 'Adoption Process' },\n        { href: '/emergency', label: 'Emergency Helpline' },\n        { href: '/feedback', label: 'Feedback' },\n      ]\n    },\n    {\n      title: 'Legal',\n      links: [\n        { href: '/privacy', label: 'Privacy Policy' },\n        { href: '/terms', label: 'Terms of Service' },\n        { href: '/cookies', label: 'Cookie Policy' },\n        { href: '/disclaimer', label: 'Disclaimer' },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    { href: 'https://facebook.com', icon: Facebook, label: 'Facebook' },\n    { href: 'https://instagram.com', icon: Instagram, label: 'Instagram' },\n    { href: 'https://twitter.com', icon: Twitter, label: 'Twitter' },\n    { href: 'https://youtube.com', icon: Youtube, label: 'YouTube' },\n  ];\n\n  return (\n    <footer className=\"bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white\">\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-500 rounded-full flex items-center justify-center\">\n                <PawPrint className=\"w-6 h-6 text-white\" />\n              </div>\n              <div>\n                <h3 className=\"font-primary text-xl font-bold text-gradient\">\n                  Animal Heaven\n                </h3>\n                <p className=\"text-sm text-neutral-400\">Find Your Perfect Companion</p>\n              </div>\n            </div>\n            \n            <p className=\"text-neutral-300 mb-6 leading-relaxed\">\n              Connecting loving families with pets in need. Every adoption saves a life and \n              creates a forever bond filled with unconditional love and joy.\n            </p>\n\n            {/* Contact Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3 text-sm text-neutral-300\">\n                <Phone className=\"w-4 h-4 text-orange-400\" />\n                <span>+91 98765 43210</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-sm text-neutral-300\">\n                <Mail className=\"w-4 h-4 text-orange-400\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-sm text-neutral-300\">\n                <MapPin className=\"w-4 h-4 text-orange-400\" />\n                <span>123 Pet Street, Animal City, AC 12345</span>\n              </div>\n            </div>\n\n            {/* Emergency Helpline */}\n            <div className=\"mt-6 p-4 bg-red-900/30 border border-red-700/50 rounded-lg\">\n              <h4 className=\"font-semibold text-red-300 mb-2\">24/7 Emergency Helpline</h4>\n              <p className=\"text-red-200 text-lg font-bold\">+91 98765 00000</p>\n              <p className=\"text-red-300 text-sm\">For animal emergencies and rescue</p>\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"font-primary font-semibold text-white mb-4\">\n                {section.title}\n              </h4>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <Link\n                      href={link.href}\n                      className=\"text-neutral-300 hover:text-orange-400 transition-colors text-sm\"\n                    >\n                      {link.label}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Subscription */}\n        <div className=\"mt-12 pt-8 border-t border-neutral-700\">\n          <div className=\"max-w-md mx-auto text-center lg:text-left lg:max-w-none lg:flex lg:items-center lg:justify-between\">\n            <div className=\"lg:flex-1\">\n              <h4 className=\"font-primary text-lg font-semibold text-white mb-2\">\n                Stay Updated with Animal Heaven\n              </h4>\n              <p className=\"text-neutral-300 text-sm mb-4 lg:mb-0\">\n                Get the latest news about adoptable pets, success stories, and events.\n              </p>\n            </div>\n            \n            <div className=\"lg:ml-8 lg:flex-shrink-0\">\n              <div className=\"flex flex-col sm:flex-row gap-2 max-w-md\">\n                <Input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"flex-1 bg-neutral-800 border-neutral-600 text-white placeholder:text-neutral-400\"\n                />\n                <Button variant=\"primary\" className=\"whitespace-nowrap\">\n                  Subscribe\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Social Links */}\n        <div className=\"mt-8 pt-8 border-t border-neutral-700\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-center\">\n            <div className=\"flex space-x-4 mb-4 sm:mb-0\">\n              {socialLinks.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <a\n                    key={social.label}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"w-10 h-10 bg-neutral-800 hover:bg-orange-600 rounded-full flex items-center justify-center transition-colors group\"\n                    aria-label={social.label}\n                  >\n                    <Icon className=\"w-5 h-5 text-neutral-400 group-hover:text-white\" />\n                  </a>\n                );\n              })}\n            </div>\n\n            <div className=\"flex items-center space-x-4 text-sm text-neutral-400\">\n              <span>Made with</span>\n              <Heart className=\"w-4 h-4 text-red-500 animate-pulse\" />\n              <span>for animals in need</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"bg-neutral-900 border-t border-neutral-800\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-center text-sm text-neutral-400\">\n            <p>\n              © {currentYear} Animal Heaven. All rights reserved.\n            </p>\n            <p className=\"mt-2 sm:mt-0\">\n              Registered Non-Profit Organization | License: NPO-2024-001\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport { Footer };\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AAhBA;;;;;;AAkBA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAc;gBACvC;oBAAE,MAAM;oBAAc,OAAO;gBAAY;gBACzC;oBAAE,MAAM;oBAAW,OAAO;gBAAS;gBACnC;oBAAE,MAAM;oBAAc,OAAO;gBAAqB;gBAClD;oBAAE,MAAM;oBAAY,OAAO;gBAAsB;aAClD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAY;gBACrC;oBAAE,MAAM;oBAAe,OAAO;gBAAgB;gBAC9C;oBAAE,MAAM;oBAAkB,OAAO;gBAAc;gBAC/C;oBAAE,MAAM;oBAAmB,OAAO;gBAAW;gBAC7C;oBAAE,MAAM;oBAAY,OAAO;gBAAa;aACzC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAc;gBACtC;oBAAE,MAAM;oBAAQ,OAAO;gBAAM;gBAC7B;oBAAE,MAAM;oBAAqB,OAAO;gBAAmB;gBACvD;oBAAE,MAAM;oBAAc,OAAO;gBAAqB;gBAClD;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAe,OAAO;gBAAa;aAC5C;QACH;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAwB,MAAM,6MAAA,CAAA,WAAQ;YAAE,OAAO;QAAW;QAClE;YAAE,MAAM;YAAyB,MAAM,+MAAA,CAAA,YAAS;YAAE,OAAO;QAAY;QACrE;YAAE,MAAM;YAAuB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAU;QAC/D;YAAE,MAAM;YAAuB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAU;KAChE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA+C;;;;;;kEAG7D,6LAAC;wDAAE,WAAU;kEAA2B;;;;;;;;;;;;;;;;;;kDAI5C,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAMrD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;0DAAiC;;;;;;0DAC9C,6LAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;4BAKvC,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;8DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,KAAK;;;;;;mDALN,KAAK,IAAI;;;;;;;;;;;mCANd,QAAQ,KAAK;;;;;;;;;;;kCAqB3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqD;;;;;;sDAGnE,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAKvD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAShE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,6LAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAY,OAAO,KAAK;sDAExB,cAAA,6LAAC;gDAAK,WAAU;;;;;;2CAPX,OAAO,KAAK;;;;;oCAUvB;;;;;;8CAGF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;oCACE;oCAAY;;;;;;;0CAEjB,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC;KAlMM", "debugId": null}}, {"offset": {"line": 2484, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/pets/PetCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { Heart, MapPin, Calendar, Eye } from 'lucide-react';\nimport { <PERSON>, CardContent, CardFooter } from '@/components/ui/Card';\nimport { Badge } from '@/components/ui/Badge';\nimport { Button } from '@/components/ui/Button';\nimport { cn, formatAge } from '@/lib/utils';\n\nexport interface Pet {\n  id: string;\n  name: string;\n  type: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';\n  breed: string;\n  age: {\n    value: number;\n    unit: 'months' | 'years';\n  };\n  gender: 'male' | 'female';\n  size: 'small' | 'medium' | 'large' | 'extra-large';\n  color: string;\n  description: string;\n  personality: string[];\n  images: Array<{\n    url: string;\n    alt: string;\n    isPrimary: boolean;\n  }>;\n  status: 'available' | 'pending' | 'adopted' | 'fostered' | 'medical-hold';\n  location: {\n    shelter: string;\n    city: string;\n    state: string;\n  };\n  adoptionFee: number;\n  specialNeeds: boolean;\n  goodWith: {\n    children: boolean;\n    dogs: boolean;\n    cats: boolean;\n  };\n  views: number;\n  likes: number;\n  featured: boolean;\n}\n\ninterface PetCardProps {\n  pet: Pet;\n  onLike?: (petId: string) => void;\n  onSave?: (petId: string) => void;\n  onViewDetails?: (petId: string) => void;\n  isLiked?: boolean;\n  isSaved?: boolean;\n  className?: string;\n}\n\nconst PetCard: React.FC<PetCardProps> = ({\n  pet,\n  onLike,\n  onSave,\n  onViewDetails,\n  isLiked = false,\n  isSaved = false,\n  className\n}) => {\n  const primaryImage = pet.images.find(img => img.isPrimary) || pet.images[0];\n  \n  const statusColors = {\n    available: 'success',\n    pending: 'warning',\n    adopted: 'primary',\n    fostered: 'secondary',\n    'medical-hold': 'danger'\n  } as const;\n\n  const sizeLabels = {\n    small: 'Small',\n    medium: 'Medium',\n    large: 'Large',\n    'extra-large': 'Extra Large'\n  };\n\n  return (\n    <Card \n      variant=\"warm\" \n      padding=\"none\" \n      rounded=\"xl\"\n      className={cn(\n        'group overflow-hidden hover:scale-105 transition-all duration-300 cursor-pointer',\n        pet.featured && 'ring-2 ring-orange-300 ring-offset-2',\n        className\n      )}\n      onClick={() => onViewDetails?.(pet.id)}\n    >\n      {/* Image Section */}\n      <div className=\"relative aspect-[4/3] overflow-hidden\">\n        {primaryImage ? (\n          <Image\n            src={primaryImage.url}\n            alt={primaryImage.alt}\n            fill\n            className=\"object-cover group-hover:scale-110 transition-transform duration-300\"\n          />\n        ) : (\n          <div className=\"w-full h-full bg-gradient-to-br from-orange-100 to-amber-100 flex items-center justify-center\">\n            <div className=\"text-6xl\">🐾</div>\n          </div>\n        )}\n        \n        {/* Status Badge */}\n        <div className=\"absolute top-3 left-3\">\n          <Badge variant={statusColors[pet.status]} size=\"sm\">\n            {pet.status.charAt(0).toUpperCase() + pet.status.slice(1).replace('-', ' ')}\n          </Badge>\n        </div>\n\n        {/* Featured Badge */}\n        {pet.featured && (\n          <div className=\"absolute top-3 right-3\">\n            <Badge variant=\"primary\" size=\"sm\">\n              ⭐ Featured\n            </Badge>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"absolute bottom-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity\">\n          <Button\n            size=\"sm\"\n            variant={isLiked ? 'primary' : 'outline'}\n            onClick={(e) => {\n              e.stopPropagation();\n              onLike?.(pet.id);\n            }}\n            className=\"bg-white/90 backdrop-blur-sm\"\n          >\n            <Heart className={cn('w-4 h-4', isLiked && 'fill-current')} />\n          </Button>\n          <Button\n            size=\"sm\"\n            variant={isSaved ? 'secondary' : 'outline'}\n            onClick={(e) => {\n              e.stopPropagation();\n              onSave?.(pet.id);\n            }}\n            className=\"bg-white/90 backdrop-blur-sm\"\n          >\n            📋\n          </Button>\n        </div>\n\n        {/* Special Needs Indicator */}\n        {pet.specialNeeds && (\n          <div className=\"absolute bottom-3 left-3\">\n            <Badge variant=\"warning\" size=\"sm\">\n              Special Needs\n            </Badge>\n          </div>\n        )}\n      </div>\n\n      {/* Content Section */}\n      <CardContent className=\"p-4\">\n        <div className=\"space-y-3\">\n          {/* Name and Basic Info */}\n          <div>\n            <h3 className=\"font-primary text-lg font-semibold text-neutral-900 group-hover:text-orange-600 transition-colors\">\n              {pet.name}\n            </h3>\n            <p className=\"text-sm text-neutral-600\">\n              {pet.breed} • {formatAge(pet.age.value, pet.age.unit)} • {sizeLabels[pet.size]}\n            </p>\n          </div>\n\n          {/* Description */}\n          <p className=\"text-sm text-neutral-700 line-clamp-2\">\n            {pet.description}\n          </p>\n\n          {/* Personality Traits */}\n          <div className=\"flex flex-wrap gap-1\">\n            {pet.personality.slice(0, 3).map((trait) => (\n              <Badge key={trait} variant=\"outline\" size=\"sm\">\n                {trait}\n              </Badge>\n            ))}\n            {pet.personality.length > 3 && (\n              <Badge variant=\"outline\" size=\"sm\">\n                +{pet.personality.length - 3} more\n              </Badge>\n            )}\n          </div>\n\n          {/* Good With */}\n          <div className=\"flex gap-2 text-xs text-neutral-600\">\n            {pet.goodWith.children && <span>👶 Kids</span>}\n            {pet.goodWith.dogs && <span>🐕 Dogs</span>}\n            {pet.goodWith.cats && <span>🐱 Cats</span>}\n          </div>\n        </div>\n      </CardContent>\n\n      {/* Footer */}\n      <CardFooter className=\"px-4 pb-4 pt-0 flex justify-between items-center\">\n        <div className=\"flex items-center gap-4 text-xs text-neutral-500\">\n          <div className=\"flex items-center gap-1\">\n            <MapPin className=\"w-3 h-3\" />\n            <span>{pet.location.city}</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <Eye className=\"w-3 h-3\" />\n            <span>{pet.views}</span>\n          </div>\n        </div>\n        \n        <div className=\"text-right\">\n          <p className=\"text-sm font-semibold text-orange-600\">\n            {pet.adoptionFee === 0 ? 'Free' : `₹${pet.adoptionFee.toLocaleString()}`}\n          </p>\n          <p className=\"text-xs text-neutral-500\">Adoption Fee</p>\n        </div>\n      </CardFooter>\n    </Card>\n  );\n};\n\nexport { PetCard };\nexport type { Pet, PetCardProps };\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAyDA,MAAM,UAAkC;QAAC,EACvC,GAAG,EACH,MAAM,EACN,MAAM,EACN,aAAa,EACb,UAAU,KAAK,EACf,UAAU,KAAK,EACf,SAAS,EACV;IACC,MAAM,eAAe,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,SAAS,KAAK,IAAI,MAAM,CAAC,EAAE;IAE3E,MAAM,eAAe;QACnB,WAAW;QACX,SAAS;QACT,SAAS;QACT,UAAU;QACV,gBAAgB;IAClB;IAEA,MAAM,aAAa;QACjB,OAAO;QACP,QAAQ;QACR,OAAO;QACP,eAAe;IACjB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QACH,SAAQ;QACR,SAAQ;QACR,SAAQ;QACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA,IAAI,QAAQ,IAAI,wCAChB;QAEF,SAAS,IAAM,0BAAA,oCAAA,cAAgB,IAAI,EAAE;;0BAGrC,6LAAC;gBAAI,WAAU;;oBACZ,6BACC,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,aAAa,GAAG;wBACrB,KAAK,aAAa,GAAG;wBACrB,IAAI;wBACJ,WAAU;;;;;iFAGZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAW;;;;;;;;;;;kCAK9B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAS,YAAY,CAAC,IAAI,MAAM,CAAC;4BAAE,MAAK;sCAC5C,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;;;;;oBAK1E,IAAI,QAAQ,kBACX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;kCAOvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,UAAU,YAAY;gCAC/B,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,mBAAA,6BAAA,OAAS,IAAI,EAAE;gCACjB;gCACA,WAAU;0CAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,WAAW;;;;;;;;;;;0CAE7C,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS,UAAU,cAAc;gCACjC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,mBAAA,6BAAA,OAAS,IAAI,EAAE;gCACjB;gCACA,WAAU;0CACX;;;;;;;;;;;;oBAMF,IAAI,YAAY,kBACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,MAAK;sCAAK;;;;;;;;;;;;;;;;;0BAQzC,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CACX,IAAI,IAAI;;;;;;8CAEX,6LAAC;oCAAE,WAAU;;wCACV,IAAI,KAAK;wCAAC;wCAAI,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,GAAG,CAAC,IAAI;wCAAE;wCAAI,UAAU,CAAC,IAAI,IAAI,CAAC;;;;;;;;;;;;;sCAKlF,6LAAC;4BAAE,WAAU;sCACV,IAAI,WAAW;;;;;;sCAIlB,6LAAC;4BAAI,WAAU;;gCACZ,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,sBAChC,6LAAC,oIAAA,CAAA,QAAK;wCAAa,SAAQ;wCAAU,MAAK;kDACvC;uCADS;;;;;gCAIb,IAAI,WAAW,CAAC,MAAM,GAAG,mBACxB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,MAAK;;wCAAK;wCAC/B,IAAI,WAAW,CAAC,MAAM,GAAG;wCAAE;;;;;;;;;;;;;sCAMnC,6LAAC;4BAAI,WAAU;;gCACZ,IAAI,QAAQ,CAAC,QAAQ,kBAAI,6LAAC;8CAAK;;;;;;gCAC/B,IAAI,QAAQ,CAAC,IAAI,kBAAI,6LAAC;8CAAK;;;;;;gCAC3B,IAAI,QAAQ,CAAC,IAAI,kBAAI,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAM,IAAI,QAAQ,CAAC,IAAI;;;;;;;;;;;;0CAE1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,6LAAC;kDAAM,IAAI,KAAK;;;;;;;;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CACV,IAAI,WAAW,KAAK,IAAI,SAAS,AAAC,IAAoC,OAAjC,IAAI,WAAW,CAAC,cAAc;;;;;;0CAEtE,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;;;;;;;;;;;;;;;;;;;AAKlD;KAxKM", "debugId": null}}, {"offset": {"line": 2875, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/sections/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Heart, Search } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { SearchBar } from '@/components/ui/SearchBar';\nimport { cn } from '@/lib/utils';\n\ninterface HeroSlide {\n  id: string;\n  image: string;\n  title: string;\n  subtitle: string;\n  description: string;\n  cta: {\n    text: string;\n    href: string;\n  };\n}\n\ninterface HeroProps {\n  slides?: HeroSlide[];\n  autoPlay?: boolean;\n  autoPlayInterval?: number;\n  onSearch?: (query: string, filters: any) => void;\n}\n\nconst defaultSlides: HeroSlide[] = [\n  {\n    id: '1',\n    image: '/images/hero/hero-1.jpg',\n    title: 'Find Your Perfect Companion',\n    subtitle: 'Thousands of loving pets are waiting for their forever homes',\n    description: 'Every adoption saves a life and creates an unbreakable bond filled with unconditional love, joy, and countless precious memories.',\n    cta: {\n      text: 'Start Your Search',\n      href: '/adopt'\n    }\n  },\n  {\n    id: '2',\n    image: '/images/hero/hero-2.jpg',\n    title: 'Give Love, Get Love',\n    subtitle: 'Open your heart and home to a pet in need',\n    description: 'Experience the incredible joy of pet parenthood while giving a deserving animal the loving family they\\'ve been dreaming of.',\n    cta: {\n      text: 'Browse Pets',\n      href: '/adopt'\n    }\n  },\n  {\n    id: '3',\n    image: '/images/hero/hero-3.jpg',\n    title: 'Make a Difference Today',\n    subtitle: 'Volunteer, donate, or adopt - every action counts',\n    description: 'Join our community of animal lovers and help us create a world where every pet has a loving home and a bright future.',\n    cta: {\n      text: 'Get Involved',\n      href: '/volunteer'\n    }\n  }\n];\n\nconst Hero: React.FC<HeroProps> = ({\n  slides = defaultSlides,\n  autoPlay = true,\n  autoPlayInterval = 5000,\n  onSearch\n}) => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isPlaying, setIsPlaying] = useState(autoPlay);\n\n  useEffect(() => {\n    if (!isPlaying) return;\n\n    const interval = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % slides.length);\n    }, autoPlayInterval);\n\n    return () => clearInterval(interval);\n  }, [isPlaying, autoPlayInterval, slides.length]);\n\n  const goToSlide = (index: number) => {\n    setCurrentSlide(index);\n  };\n\n  const goToPrevious = () => {\n    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);\n  };\n\n  const goToNext = () => {\n    setCurrentSlide((prev) => (prev + 1) % slides.length);\n  };\n\n  const currentSlideData = slides[currentSlide];\n\n  return (\n    <section className=\"relative h-screen min-h-[600px] overflow-hidden\">\n      {/* Background Slides */}\n      <div className=\"absolute inset-0\">\n        {slides.map((slide, index) => (\n          <div\n            key={slide.id}\n            className={cn(\n              'absolute inset-0 transition-opacity duration-1000',\n              index === currentSlide ? 'opacity-100' : 'opacity-0'\n            )}\n          >\n            {/* Placeholder for hero images - using gradient backgrounds for now */}\n            <div className=\"w-full h-full bg-gradient-to-br from-orange-400 via-amber-500 to-orange-600 relative\">\n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-black/30\" />\n              \n              {/* Decorative Elements */}\n              <div className=\"absolute top-20 left-10 text-6xl opacity-20 animate-float\">\n                🐕\n              </div>\n              <div className=\"absolute top-40 right-20 text-4xl opacity-20 animate-bounce-gentle\">\n                🐱\n              </div>\n              <div className=\"absolute bottom-32 left-20 text-5xl opacity-20 animate-float\">\n                🐾\n              </div>\n              <div className=\"absolute bottom-20 right-10 text-3xl opacity-20 animate-bounce-gentle\">\n                ❤️\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Content */}\n      <div className=\"relative z-10 h-full flex items-center\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            {/* Text Content */}\n            <div className=\"text-white space-y-6\">\n              <div className=\"space-y-4\">\n                <h1 className=\"font-primary text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight\">\n                  {currentSlideData.title}\n                </h1>\n                <p className=\"text-xl sm:text-2xl font-medium text-orange-100\">\n                  {currentSlideData.subtitle}\n                </p>\n                <p className=\"text-lg text-neutral-200 leading-relaxed max-w-lg\">\n                  {currentSlideData.description}\n                </p>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Button\n                  size=\"lg\"\n                  variant=\"primary\"\n                  className=\"bg-white text-orange-600 hover:bg-orange-50 shadow-xl\"\n                  leftIcon={<Search className=\"w-5 h-5\" />}\n                >\n                  {currentSlideData.cta.text}\n                </Button>\n                <Button\n                  size=\"lg\"\n                  variant=\"outline\"\n                  className=\"border-white text-white hover:bg-white hover:text-orange-600\"\n                  leftIcon={<Heart className=\"w-5 h-5\" />}\n                >\n                  Learn More\n                </Button>\n              </div>\n\n              {/* Stats */}\n              <div className=\"grid grid-cols-3 gap-6 pt-8\">\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-white\">2,500+</div>\n                  <div className=\"text-sm text-orange-200\">Pets Adopted</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-white\">150+</div>\n                  <div className=\"text-sm text-orange-200\">Active Volunteers</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-white\">50+</div>\n                  <div className=\"text-sm text-orange-200\">Partner Shelters</div>\n                </div>\n              </div>\n            </div>\n\n            {/* Search Section */}\n            <div className=\"bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl\">\n              <div className=\"mb-6\">\n                <h2 className=\"font-primary text-2xl font-semibold text-neutral-900 mb-2\">\n                  Find Your Perfect Match\n                </h2>\n                <p className=\"text-neutral-600\">\n                  Search through hundreds of loving pets waiting for their forever homes.\n                </p>\n              </div>\n\n              <SearchBar\n                placeholder=\"Search by name, breed, or type...\"\n                onSearch={onSearch}\n                showFilters={false}\n              />\n\n              {/* Quick Filters */}\n              <div className=\"mt-6\">\n                <p className=\"text-sm font-medium text-neutral-700 mb-3\">Popular searches:</p>\n                <div className=\"flex flex-wrap gap-2\">\n                  {['Dogs', 'Cats', 'Puppies', 'Kittens', 'Small Dogs', 'Senior Pets'].map((filter) => (\n                    <Button\n                      key={filter}\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"text-xs\"\n                    >\n                      {filter}\n                    </Button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Controls */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Previous Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={goToPrevious}\n            className=\"bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\"\n          >\n            <ChevronLeft className=\"w-5 h-5\" />\n          </Button>\n\n          {/* Slide Indicators */}\n          <div className=\"flex space-x-2\">\n            {slides.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={cn(\n                  'w-3 h-3 rounded-full transition-all duration-300',\n                  index === currentSlide\n                    ? 'bg-white scale-125'\n                    : 'bg-white/50 hover:bg-white/75'\n                )}\n              />\n            ))}\n          </div>\n\n          {/* Next Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={goToNext}\n            className=\"bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\"\n          >\n            <ChevronRight className=\"w-5 h-5\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Play/Pause Button */}\n      <Button\n        variant=\"ghost\"\n        size=\"sm\"\n        onClick={() => setIsPlaying(!isPlaying)}\n        className=\"absolute top-4 right-4 z-20 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30\"\n      >\n        {isPlaying ? '⏸️' : '▶️'}\n      </Button>\n    </section>\n  );\n};\n\nexport { Hero };\nexport type { HeroSlide, HeroProps };\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;AA4BA,MAAM,gBAA6B;IACjC;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;YACH,MAAM;YACN,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;YACH,MAAM;YACN,MAAM;QACR;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,aAAa;QACb,KAAK;YACH,MAAM;YACN,MAAM;QACR;IACF;CACD;AAED,MAAM,OAA4B;QAAC,EACjC,SAAS,aAAa,EACtB,WAAW,IAAI,EACf,mBAAmB,IAAI,EACvB,QAAQ,EACT;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;YAEhB,MAAM,WAAW;2CAAY;oBAC3B;mDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;gBACtD;0CAAG;YAEH;kCAAO,IAAM,cAAc;;QAC7B;yBAAG;QAAC;QAAW;QAAkB,OAAO,MAAM;KAAC;IAE/C,MAAM,YAAY,CAAC;QACjB,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM;IACtE;IAEA,MAAM,WAAW;QACf,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;IACtD;IAEA,MAAM,mBAAmB,MAAM,CAAC,aAAa;IAE7C,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;wBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,UAAU,eAAe,gBAAgB;kCAI3C,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;8CAA4D;;;;;;8CAG3E,6LAAC;oCAAI,WAAU;8CAAqE;;;;;;8CAGpF,6LAAC;oCAAI,WAAU;8CAA+D;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;8CAAwE;;;;;;;;;;;;uBArBpF,MAAM,EAAE;;;;;;;;;;0BA8BnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,iBAAiB,KAAK;;;;;;0DAEzB,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,QAAQ;;;;;;0DAE5B,6LAAC;gDAAE,WAAU;0DACV,iBAAiB,WAAW;;;;;;;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,wBAAU,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;0DAE3B,iBAAiB,GAAG,CAAC,IAAI;;;;;;0DAE5B,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;gDACV,wBAAU,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;0DAC5B;;;;;;;;;;;;kDAMH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;;;;;;;0CAM/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAE,WAAU;0DAAmB;;;;;;;;;;;;kDAKlC,6LAAC,wIAAA,CAAA,YAAS;wCACR,aAAY;wCACZ,UAAU;wCACV,aAAa;;;;;;kDAIf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA4C;;;;;;0DACzD,6LAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAQ;oDAAQ;oDAAW;oDAAW;oDAAc;iDAAc,CAAC,GAAG,CAAC,CAAC,uBACxE,6LAAC,qIAAA,CAAA,SAAM;wDAEL,SAAQ;wDACR,MAAK;wDACL,WAAU;kEAET;uDALI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgBrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;;;;;;sCAIzB,6LAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,GAAG,sBACd,6LAAC;oCAEC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA,UAAU,eACN,uBACA;mCAND;;;;;;;;;;sCAaX,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,CAAC;gBAC7B,WAAU;0BAET,YAAY,OAAO;;;;;;;;;;;;AAI5B;GApNM;KAAA", "debugId": null}}, {"offset": {"line": 3402, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/sections/SuccessStories.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Image from 'next/image';\nimport { ChevronLeft, ChevronRight, Heart, Calendar, MapPin } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Badge } from '@/components/ui/Badge';\nimport { cn } from '@/lib/utils';\n\ninterface SuccessStory {\n  id: string;\n  petName: string;\n  petType: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';\n  adopterName: string;\n  adopterLocation: string;\n  adoptionDate: string;\n  story: string;\n  beforeImage: string;\n  afterImage: string;\n  featured: boolean;\n}\n\ninterface SuccessStoriesProps {\n  stories?: SuccessStory[];\n  autoPlay?: boolean;\n  autoPlayInterval?: number;\n}\n\nconst defaultStories: SuccessStory[] = [\n  {\n    id: '1',\n    petName: 'Max',\n    petType: 'dog',\n    adopterName: 'The Johnson Family',\n    adopterLocation: 'Mumbai, Maharashtra',\n    adoptionDate: '2024-01-15',\n    story: '<PERSON> was a shy rescue dog who had been at the shelter for 8 months. The <PERSON> family fell in love with his gentle nature and gave him the patient, loving home he needed. Now <PERSON> is a confident, happy dog who loves playing with the kids and going on family adventures.',\n    beforeImage: '/images/success/max-before.jpg',\n    afterImage: '/images/success/max-after.jpg',\n    featured: true\n  },\n  {\n    id: '2',\n    petName: 'Luna',\n    petType: 'cat',\n    adopterName: 'Sarah & Mike',\n    adopterLocation: 'Delhi, Delhi',\n    adoptionDate: '2024-02-20',\n    story: 'Luna was found as a tiny kitten on the streets. After months of care at the shelter, Sarah and Mike adopted her. Luna has transformed into a confident, playful cat who brings joy and laughter to their home every day.',\n    beforeImage: '/images/success/luna-before.jpg',\n    afterImage: '/images/success/luna-after.jpg',\n    featured: true\n  },\n  {\n    id: '3',\n    petName: 'Buddy',\n    petType: 'dog',\n    adopterName: 'The Patel Family',\n    adopterLocation: 'Bangalore, Karnataka',\n    adoptionDate: '2024-03-10',\n    story: 'Buddy was an older dog who many thought would never find a home. The Patel family saw past his age and gave him the loving retirement he deserved. Buddy now spends his days being spoiled and loved by his new family.',\n    beforeImage: '/images/success/buddy-before.jpg',\n    afterImage: '/images/success/buddy-after.jpg',\n    featured: false\n  }\n];\n\nconst SuccessStories: React.FC<SuccessStoriesProps> = ({\n  stories = defaultStories,\n  autoPlay = true,\n  autoPlayInterval = 6000\n}) => {\n  const [currentStory, setCurrentStory] = useState(0);\n  const [showAfterImage, setShowAfterImage] = useState(false);\n\n  React.useEffect(() => {\n    if (!autoPlay) return;\n\n    const interval = setInterval(() => {\n      setCurrentStory((prev) => (prev + 1) % stories.length);\n      setShowAfterImage(false);\n    }, autoPlayInterval);\n\n    return () => clearInterval(interval);\n  }, [autoPlay, autoPlayInterval, stories.length]);\n\n  const goToPrevious = () => {\n    setCurrentStory((prev) => (prev - 1 + stories.length) % stories.length);\n    setShowAfterImage(false);\n  };\n\n  const goToNext = () => {\n    setCurrentStory((prev) => (prev + 1) % stories.length);\n    setShowAfterImage(false);\n  };\n\n  const currentStoryData = stories[currentStory];\n\n  return (\n    <section className=\"py-16 bg-gradient-to-br from-green-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"font-primary text-4xl font-bold text-neutral-900 mb-4\">\n            Happy Tails Success Stories\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Every adoption creates a beautiful story. Here are some of our favorite success stories \n            that show the incredible bond between pets and their new families.\n          </p>\n        </div>\n\n        {/* Main Story Display */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-8\">\n          {/* Image Section */}\n          <div className=\"relative\">\n            <Card variant=\"elevated\" padding=\"none\" className=\"overflow-hidden\">\n              <div className=\"relative aspect-[4/3]\">\n                {/* Before/After Images */}\n                <div className=\"relative w-full h-full\">\n                  <Image\n                    src={showAfterImage ? currentStoryData.afterImage : currentStoryData.beforeImage}\n                    alt={`${currentStoryData.petName} ${showAfterImage ? 'after' : 'before'} adoption`}\n                    fill\n                    className=\"object-cover transition-opacity duration-500\"\n                    onError={(e) => {\n                      // Fallback to gradient background if image fails to load\n                      const target = e.target as HTMLImageElement;\n                      target.style.display = 'none';\n                    }}\n                  />\n                  \n                  {/* Fallback gradient background */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-orange-200 to-green-200 flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"text-6xl mb-2\">\n                        {currentStoryData.petType === 'dog' ? '🐕' : \n                         currentStoryData.petType === 'cat' ? '🐱' : '🐾'}\n                      </div>\n                      <p className=\"text-neutral-600 font-medium\">{currentStoryData.petName}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Before/After Toggle */}\n                <div className=\"absolute bottom-4 left-4 right-4\">\n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant={!showAfterImage ? 'primary' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setShowAfterImage(false)}\n                      className=\"flex-1\"\n                    >\n                      Before\n                    </Button>\n                    <Button\n                      variant={showAfterImage ? 'primary' : 'outline'}\n                      size=\"sm\"\n                      onClick={() => setShowAfterImage(true)}\n                      className=\"flex-1\"\n                    >\n                      After\n                    </Button>\n                  </div>\n                </div>\n\n                {/* Pet Type Badge */}\n                <div className=\"absolute top-4 left-4\">\n                  <Badge variant=\"primary\" className=\"capitalize\">\n                    {currentStoryData.petType}\n                  </Badge>\n                </div>\n              </div>\n            </Card>\n          </div>\n\n          {/* Story Content */}\n          <div className=\"space-y-6\">\n            <div>\n              <h3 className=\"font-primary text-3xl font-bold text-neutral-900 mb-2\">\n                {currentStoryData.petName}'s Journey\n              </h3>\n              <div className=\"flex flex-wrap gap-4 text-sm text-neutral-600 mb-4\">\n                <div className=\"flex items-center gap-1\">\n                  <Heart className=\"w-4 h-4 text-red-500\" />\n                  <span>Adopted by {currentStoryData.adopterName}</span>\n                </div>\n                <div className=\"flex items-center gap-1\">\n                  <MapPin className=\"w-4 h-4 text-orange-500\" />\n                  <span>{currentStoryData.adopterLocation}</span>\n                </div>\n                <div className=\"flex items-center gap-1\">\n                  <Calendar className=\"w-4 h-4 text-green-500\" />\n                  <span>{new Date(currentStoryData.adoptionDate).toLocaleDateString()}</span>\n                </div>\n              </div>\n            </div>\n\n            <p className=\"text-neutral-700 leading-relaxed text-lg\">\n              {currentStoryData.story}\n            </p>\n\n            <div className=\"flex gap-4\">\n              <Button variant=\"primary\" size=\"lg\">\n                Share This Story\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                View More Stories\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation Controls */}\n        <div className=\"flex items-center justify-center gap-6\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={goToPrevious}\n            leftIcon={<ChevronLeft className=\"w-4 h-4\" />}\n          >\n            Previous\n          </Button>\n\n          {/* Story Indicators */}\n          <div className=\"flex gap-2\">\n            {stories.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => {\n                  setCurrentStory(index);\n                  setShowAfterImage(false);\n                }}\n                className={cn(\n                  'w-3 h-3 rounded-full transition-all duration-300',\n                  index === currentStory\n                    ? 'bg-orange-500 scale-125'\n                    : 'bg-neutral-300 hover:bg-neutral-400'\n                )}\n              />\n            ))}\n          </div>\n\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={goToNext}\n            rightIcon={<ChevronRight className=\"w-4 h-4\" />}\n          >\n            Next\n          </Button>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-12\">\n          <Card variant=\"warm\" className=\"max-w-2xl mx-auto\">\n            <CardContent className=\"text-center py-8\">\n              <h3 className=\"font-primary text-2xl font-semibold text-neutral-900 mb-4\">\n                Ready to Create Your Own Success Story?\n              </h3>\n              <p className=\"text-neutral-600 mb-6\">\n                Browse our available pets and find your perfect companion today.\n              </p>\n              <Button variant=\"primary\" size=\"lg\">\n                Start Your Adoption Journey\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport { SuccessStories };\nexport type { SuccessStory, SuccessStoriesProps };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AA6BA,MAAM,iBAAiC;IACrC;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;QACT,SAAS;QACT,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,OAAO;QACP,aAAa;QACb,YAAY;QACZ,UAAU;IACZ;CACD;AAED,MAAM,iBAAgD;QAAC,EACrD,UAAU,cAAc,EACxB,WAAW,IAAI,EACf,mBAAmB,IAAI,EACxB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,6JAAA,CAAA,UAAK,CAAC,SAAS;oCAAC;YACd,IAAI,CAAC,UAAU;YAEf,MAAM,WAAW;qDAAY;oBAC3B;6DAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;;oBACrD,kBAAkB;gBACpB;oDAAG;YAEH;4CAAO,IAAM,cAAc;;QAC7B;mCAAG;QAAC;QAAU;QAAkB,QAAQ,MAAM;KAAC;IAE/C,MAAM,eAAe;QACnB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM;QACtE,kBAAkB;IACpB;IAEA,MAAM,WAAW;QACf,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;QACrD,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,OAAO,CAAC,aAAa;IAE9C,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAO5D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAW,SAAQ;gCAAO,WAAU;0CAChD,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK,iBAAiB,iBAAiB,UAAU,GAAG,iBAAiB,WAAW;oDAChF,KAAK,AAAC,GAA8B,OAA5B,iBAAiB,OAAO,EAAC,KAAuC,OAApC,iBAAiB,UAAU,UAAS;oDACxE,IAAI;oDACJ,WAAU;oDACV,SAAS,CAAC;wDACR,yDAAyD;wDACzD,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;oDACzB;;;;;;8DAIF,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,iBAAiB,OAAO,KAAK,QAAQ,OACrC,iBAAiB,OAAO,KAAK,QAAQ,OAAO;;;;;;0EAE/C,6LAAC;gEAAE,WAAU;0EAAgC,iBAAiB,OAAO;;;;;;;;;;;;;;;;;;;;;;;sDAM3E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,CAAC,iBAAiB,YAAY;wDACvC,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,WAAU;kEACX;;;;;;kEAGD,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS,iBAAiB,YAAY;wDACtC,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,WAAU;kEACX;;;;;;;;;;;;;;;;;sDAOL,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,iBAAiB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDACX,iBAAiB,OAAO;gDAAC;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;;gEAAK;gEAAY,iBAAiB,WAAW;;;;;;;;;;;;;8DAEhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,6LAAC;sEAAM,iBAAiB,eAAe;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;sEAAM,IAAI,KAAK,iBAAiB,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;8CAKvE,6LAAC;oCAAE,WAAU;8CACV,iBAAiB,KAAK;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDAGpC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,wBAAU,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;sCAClC;;;;;;sCAKD,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,GAAG,sBACf,6LAAC;oCAEC,SAAS;wCACP,gBAAgB;wCAChB,kBAAkB;oCACpB;oCACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA,UAAU,eACN,4BACA;mCATD;;;;;;;;;;sCAeX,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,yBAAW,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;sCACpC;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAO,WAAU;kCAC7B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD;GA7MM;KAAA", "debugId": null}}, {"offset": {"line": 3953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/sections/Testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Image from 'next/image';\nimport { Star, Quote } from 'lucide-react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Badge } from '@/components/ui/Badge';\n\ninterface Testimonial {\n  id: string;\n  name: string;\n  location: string;\n  petName: string;\n  petType: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';\n  rating: number;\n  testimonial: string;\n  avatar: string;\n  adoptionDate: string;\n  verified: boolean;\n}\n\ninterface TestimonialsProps {\n  testimonials?: Testimonial[];\n}\n\nconst defaultTestimonials: Testimonial[] = [\n  {\n    id: '1',\n    name: '<PERSON><PERSON>',\n    location: 'Mumbai, Maharashtra',\n    petName: 'Rocky',\n    petType: 'dog',\n    rating: 5,\n    testimonial: 'The adoption process was seamless and the staff was incredibly supportive. <PERSON> has brought so much joy to our family. The follow-up care and guidance we received was exceptional.',\n    avatar: '/images/testimonials/priya.jpg',\n    adoptionDate: '2024-01-15',\n    verified: true\n  },\n  {\n    id: '2',\n    name: '<PERSON><PERSON>',\n    location: 'Delhi, Delhi',\n    petName: 'Whiskers',\n    petType: 'cat',\n    rating: 5,\n    testimonial: 'Animal Heaven made our dream of pet parenthood come true. Whiskers was exactly as described, and the team helped us prepare our home perfectly. Highly recommended!',\n    avatar: '/images/testimonials/rajesh.jpg',\n    adoptionDate: '2024-02-20',\n    verified: true\n  },\n  {\n    id: '3',\n    name: 'Anita Patel',\n    location: 'Bangalore, Karnataka',\n    petName: 'Buddy',\n    petType: 'dog',\n    rating: 5,\n    testimonial: 'The care and love shown by the Animal Heaven team is remarkable. They matched us with Buddy perfectly, and the ongoing support has been invaluable for first-time pet owners.',\n    avatar: '/images/testimonials/anita.jpg',\n    adoptionDate: '2024-03-10',\n    verified: true\n  },\n  {\n    id: '4',\n    name: 'Vikram Singh',\n    location: 'Chennai, Tamil Nadu',\n    petName: 'Luna',\n    petType: 'cat',\n    rating: 5,\n    testimonial: 'Professional, caring, and thorough. The adoption process was transparent and they ensured Luna was the right fit for our lifestyle. She\\'s now the queen of our house!',\n    avatar: '/images/testimonials/vikram.jpg',\n    adoptionDate: '2024-04-05',\n    verified: true\n  },\n  {\n    id: '5',\n    name: 'Meera Reddy',\n    location: 'Hyderabad, Telangana',\n    petName: 'Charlie',\n    petType: 'dog',\n    rating: 5,\n    testimonial: 'Animal Heaven doesn\\'t just find homes for pets, they create families. Charlie has transformed our lives, and the support we received made the transition smooth and joyful.',\n    avatar: '/images/testimonials/meera.jpg',\n    adoptionDate: '2024-04-20',\n    verified: true\n  },\n  {\n    id: '6',\n    name: 'Arjun Gupta',\n    location: 'Pune, Maharashtra',\n    petName: 'Milo',\n    petType: 'dog',\n    rating: 5,\n    testimonial: 'The team\\'s dedication to animal welfare is inspiring. They took time to understand our needs and matched us with Milo perfectly. The post-adoption support is outstanding.',\n    avatar: '/images/testimonials/arjun.jpg',\n    adoptionDate: '2024-05-15',\n    verified: true\n  }\n];\n\nconst Testimonials: React.FC<TestimonialsProps> = ({\n  testimonials = defaultTestimonials\n}) => {\n  const renderStars = (rating: number) => {\n    return Array.from({ length: 5 }, (_, index) => (\n      <Star\n        key={index}\n        className={`w-4 h-4 ${\n          index < rating ? 'text-yellow-400 fill-current' : 'text-neutral-300'\n        }`}\n      />\n    ));\n  };\n\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"font-primary text-4xl font-bold text-neutral-900 mb-4\">\n            What Our Families Say\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Don't just take our word for it. Here's what our adopting families have to say \n            about their experience with Animal Heaven and their new furry family members.\n          </p>\n        </div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n          {testimonials.map((testimonial) => (\n            <Card key={testimonial.id} variant=\"elevated\" className=\"h-full\">\n              <CardContent className=\"p-6 flex flex-col h-full\">\n                {/* Quote Icon */}\n                <div className=\"mb-4\">\n                  <Quote className=\"w-8 h-8 text-orange-400\" />\n                </div>\n\n                {/* Rating */}\n                <div className=\"flex items-center gap-1 mb-4\">\n                  {renderStars(testimonial.rating)}\n                </div>\n\n                {/* Testimonial Text */}\n                <blockquote className=\"text-neutral-700 leading-relaxed mb-6 flex-grow\">\n                  \"{testimonial.testimonial}\"\n                </blockquote>\n\n                {/* Author Info */}\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"relative w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-orange-200 to-green-200 flex items-center justify-center\">\n                    <Image\n                      src={testimonial.avatar}\n                      alt={testimonial.name}\n                      width={48}\n                      height={48}\n                      className=\"object-cover\"\n                      onError={(e) => {\n                        // Fallback to initials if image fails to load\n                        const target = e.target as HTMLImageElement;\n                        target.style.display = 'none';\n                      }}\n                    />\n                    {/* Fallback initials */}\n                    <span className=\"text-neutral-600 font-semibold text-sm\">\n                      {testimonial.name.split(' ').map(n => n[0]).join('')}\n                    </span>\n                  </div>\n                  \n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center gap-2 mb-1\">\n                      <h4 className=\"font-semibold text-neutral-900\">\n                        {testimonial.name}\n                      </h4>\n                      {testimonial.verified && (\n                        <Badge variant=\"success\" size=\"sm\">\n                          ✓ Verified\n                        </Badge>\n                      )}\n                    </div>\n                    <p className=\"text-sm text-neutral-600\">\n                      {testimonial.location}\n                    </p>\n                    <p className=\"text-xs text-neutral-500\">\n                      Adopted {testimonial.petName} • {new Date(testimonial.adoptionDate).toLocaleDateString()}\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Statistics Section */}\n        <div className=\"bg-gradient-to-r from-orange-500 to-green-500 rounded-2xl p-8 text-white\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 text-center\">\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">2,500+</div>\n              <div className=\"text-orange-100\">Happy Adoptions</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">4.9/5</div>\n              <div className=\"text-orange-100\">Average Rating</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">98%</div>\n              <div className=\"text-orange-100\">Success Rate</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">150+</div>\n              <div className=\"text-orange-100\">Active Volunteers</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-12\">\n          <h3 className=\"font-primary text-2xl font-semibold text-neutral-900 mb-4\">\n            Ready to Join Our Happy Families?\n          </h3>\n          <p className=\"text-neutral-600 mb-6 max-w-xl mx-auto\">\n            Start your adoption journey today and create your own success story with a loving pet.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <button className=\"bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-medium transition-colors\">\n              Browse Available Pets\n            </button>\n            <button className=\"border border-orange-500 text-orange-600 hover:bg-orange-50 px-6 py-3 rounded-lg font-medium transition-colors\">\n              Read More Reviews\n            </button>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport { Testimonials };\nexport type { Testimonial, TestimonialsProps };\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AANA;;;;;;AAyBA,MAAM,sBAAqC;IACzC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;QACd,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;QACd,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;QACd,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;QACd,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;QACd,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,cAAc;QACd,UAAU;IACZ;CACD;AAED,MAAM,eAA4C;QAAC,EACjD,eAAe,mBAAmB,EACnC;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,CAAC,GAAG,sBACnC,6LAAC,qMAAA,CAAA,OAAI;gBAEH,WAAW,AAAC,WAEX,OADC,QAAQ,SAAS,iCAAiC;eAF/C;;;;;IAMX;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAO5D,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,6LAAC,mIAAA,CAAA,OAAI;4BAAsB,SAAQ;4BAAW,WAAU;sCACtD,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAInB,6LAAC;wCAAI,WAAU;kDACZ,YAAY,YAAY,MAAM;;;;;;kDAIjC,6LAAC;wCAAW,WAAU;;4CAAkD;4CACpE,YAAY,WAAW;4CAAC;;;;;;;kDAI5B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,YAAY,MAAM;wDACvB,KAAK,YAAY,IAAI;wDACrB,OAAO;wDACP,QAAQ;wDACR,WAAU;wDACV,SAAS,CAAC;4DACR,8CAA8C;4DAC9C,MAAM,SAAS,EAAE,MAAM;4DACvB,OAAO,KAAK,CAAC,OAAO,GAAG;wDACzB;;;;;;kEAGF,6LAAC;wDAAK,WAAU;kEACb,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;0DAIrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,YAAY,IAAI;;;;;;4DAElB,YAAY,QAAQ,kBACnB,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,MAAK;0EAAK;;;;;;;;;;;;kEAKvC,6LAAC;wDAAE,WAAU;kEACV,YAAY,QAAQ;;;;;;kEAEvB,6LAAC;wDAAE,WAAU;;4DAA2B;4DAC7B,YAAY,OAAO;4DAAC;4DAAI,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;2BArDrF,YAAY,EAAE;;;;;;;;;;8BA+D7B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;0CAEnC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;0CAEnC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;0CAEnC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;8BAMvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;sCAGtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAkG;;;;;;8CAGpH,6LAAC;oCAAO,WAAU;8CAAiH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/I;KAvIM", "debugId": null}}, {"offset": {"line": 4428, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/sections/QuickFilters.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { Badge } from '@/components/ui/Badge';\n\ninterface FilterOption {\n  id: string;\n  label: string;\n  icon: string;\n  count: number;\n  description: string;\n  color: 'primary' | 'secondary' | 'success' | 'warning';\n}\n\ninterface QuickFiltersProps {\n  onFilterSelect?: (filterId: string) => void;\n}\n\nconst filterOptions: FilterOption[] = [\n  {\n    id: 'dogs',\n    label: 'Dogs',\n    icon: '🐕',\n    count: 156,\n    description: 'Loyal companions ready for adventure',\n    color: 'primary'\n  },\n  {\n    id: 'cats',\n    label: 'Cats',\n    icon: '🐱',\n    count: 89,\n    description: 'Independent friends who love to cuddle',\n    color: 'secondary'\n  },\n  {\n    id: 'puppies',\n    label: 'Puppies',\n    icon: '🐶',\n    count: 34,\n    description: 'Young pups full of energy and love',\n    color: 'warning'\n  },\n  {\n    id: 'kittens',\n    label: 'Kittens',\n    icon: '🐾',\n    count: 28,\n    description: 'Tiny bundles of joy and mischief',\n    color: 'success'\n  },\n  {\n    id: 'senior',\n    label: 'Senior Pets',\n    icon: '💝',\n    count: 42,\n    description: 'Wise souls looking for peaceful homes',\n    color: 'primary'\n  },\n  {\n    id: 'special-needs',\n    label: 'Special Needs',\n    icon: '❤️',\n    count: 23,\n    description: 'Extra special pets needing extra love',\n    color: 'secondary'\n  }\n];\n\nconst QuickFilters: React.FC<QuickFiltersProps> = ({ onFilterSelect }) => {\n  return (\n    <section className=\"py-16 bg-gradient-to-br from-neutral-50 to-orange-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"font-primary text-4xl font-bold text-neutral-900 mb-4\">\n            Find Your Perfect Match\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Browse by category to find the type of companion that fits your lifestyle. \n            Each pet is unique and waiting for their special someone.\n          </p>\n        </div>\n\n        {/* Quick Filter Cards */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n          {filterOptions.map((option) => (\n            <Card \n              key={option.id} \n              variant=\"warm\" \n              className=\"group hover:scale-105 transition-all duration-300 cursor-pointer\"\n              onClick={() => onFilterSelect?.(option.id)}\n            >\n              <CardContent className=\"p-6 text-center\">\n                {/* Icon */}\n                <div className=\"text-6xl mb-4 group-hover:animate-bounce-gentle\">\n                  {option.icon}\n                </div>\n\n                {/* Title and Count */}\n                <div className=\"mb-3\">\n                  <h3 className=\"font-primary text-xl font-semibold text-neutral-900 mb-1\">\n                    {option.label}\n                  </h3>\n                  <Badge variant={option.color} size=\"sm\">\n                    {option.count} available\n                  </Badge>\n                </div>\n\n                {/* Description */}\n                <p className=\"text-neutral-600 text-sm mb-4 leading-relaxed\">\n                  {option.description}\n                </p>\n\n                {/* Action Button */}\n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  className=\"w-full group-hover:bg-orange-500 group-hover:text-white group-hover:border-orange-500 transition-all duration-300\"\n                >\n                  Browse {option.label}\n                </Button>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {/* Additional Quick Actions */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          {/* Emergency Adoptions */}\n          <Card variant=\"outlined\" className=\"border-red-200 bg-red-50\">\n            <CardContent className=\"p-6 text-center\">\n              <div className=\"text-4xl mb-3\">🚨</div>\n              <h3 className=\"font-primary text-lg font-semibold text-red-800 mb-2\">\n                Urgent Adoptions\n              </h3>\n              <p className=\"text-red-600 text-sm mb-4\">\n                Pets in immediate need of homes\n              </p>\n              <Button variant=\"danger\" size=\"sm\" className=\"w-full\">\n                View Urgent Cases\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Featured Pets */}\n          <Card variant=\"outlined\" className=\"border-yellow-200 bg-yellow-50\">\n            <CardContent className=\"p-6 text-center\">\n              <div className=\"text-4xl mb-3\">⭐</div>\n              <h3 className=\"font-primary text-lg font-semibold text-yellow-800 mb-2\">\n                Featured Pets\n              </h3>\n              <p className=\"text-yellow-600 text-sm mb-4\">\n                Staff picks and special personalities\n              </p>\n              <Button variant=\"warning\" size=\"sm\" className=\"w-full\">\n                Meet Our Stars\n              </Button>\n            </CardContent>\n          </Card>\n\n          {/* Recently Added */}\n          <Card variant=\"outlined\" className=\"border-green-200 bg-green-50\">\n            <CardContent className=\"p-6 text-center\">\n              <div className=\"text-4xl mb-3\">✨</div>\n              <h3 className=\"font-primary text-lg font-semibold text-green-800 mb-2\">\n                New Arrivals\n              </h3>\n              <p className=\"text-green-600 text-sm mb-4\">\n                Recently rescued and ready for love\n              </p>\n              <Button variant=\"secondary\" size=\"sm\" className=\"w-full\">\n                See New Pets\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Search Encouragement */}\n        <div className=\"text-center mt-12\">\n          <Card variant=\"elevated\" className=\"max-w-2xl mx-auto\">\n            <CardContent className=\"p-8\">\n              <h3 className=\"font-primary text-2xl font-semibold text-neutral-900 mb-4\">\n                Can't Find What You're Looking For?\n              </h3>\n              <p className=\"text-neutral-600 mb-6\">\n                Use our advanced search to filter by specific breeds, ages, sizes, and personality traits. \n                We'll help you find the perfect match for your family.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button variant=\"primary\" size=\"lg\">\n                  Advanced Search\n                </Button>\n                <Button variant=\"outline\" size=\"lg\">\n                  Get Adoption Help\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Statistics Bar */}\n        <div className=\"mt-16 bg-white rounded-2xl shadow-lg p-6\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 text-center\">\n            <div>\n              <div className=\"text-2xl font-bold text-orange-600 mb-1\">245</div>\n              <div className=\"text-sm text-neutral-600\">Pets Available</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold text-green-600 mb-1\">2,500+</div>\n              <div className=\"text-sm text-neutral-600\">Successful Adoptions</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold text-blue-600 mb-1\">50+</div>\n              <div className=\"text-sm text-neutral-600\">Partner Shelters</div>\n            </div>\n            <div>\n              <div className=\"text-2xl font-bold text-purple-600 mb-1\">24/7</div>\n              <div className=\"text-sm text-neutral-600\">Support Available</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport { QuickFilters };\nexport type { FilterOption, QuickFiltersProps };\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAoBA,MAAM,gBAAgC;IACpC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAED,MAAM,eAA4C;QAAC,EAAE,cAAc,EAAE;IACnE,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAO5D,6LAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,mIAAA,CAAA,OAAI;4BAEH,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,2BAAA,qCAAA,eAAiB,OAAO,EAAE;sCAEzC,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6LAAC;wCAAI,WAAU;kDACZ,OAAO,IAAI;;;;;;kDAId,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,OAAO,KAAK;;;;;;0DAEf,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,OAAO,KAAK;gDAAE,MAAK;;oDAChC,OAAO,KAAK;oDAAC;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAE,WAAU;kDACV,OAAO,WAAW;;;;;;kDAIrB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;;4CACX;4CACS,OAAO,KAAK;;;;;;;;;;;;;2BAhCnB,OAAO,EAAE;;;;;;;;;;8BAwCpB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAW,WAAU;sCACjC,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAuD;;;;;;kDAGrE,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;kDAGzC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAS,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAO1D,6LAAC,mIAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAW,WAAU;sCACjC,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,6LAAC;wCAAE,WAAU;kDAA+B;;;;;;kDAG5C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAO3D,6LAAC,mIAAA,CAAA,OAAI;4BAAC,SAAQ;4BAAW,WAAU;sCACjC,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAG3C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,MAAK;wCAAK,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;8BAQ/D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAW,WAAU;kCACjC,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAIrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;sDAGpC,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDAA2B;;;;;;;;;;;;0CAE5C,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAyC;;;;;;kDACxD,6LAAC;wCAAI,WAAU;kDAA2B;;;;;;;;;;;;0CAE5C,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAA2B;;;;;;;;;;;;0CAE5C,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxD;KA5JM", "debugId": null}}, {"offset": {"line": 4971, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/components/index.ts"], "sourcesContent": ["// UI Components\nexport { Button } from './ui/Button';\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } from './ui/Card';\nexport { Input } from './ui/Input';\nexport { Badge } from './ui/Badge';\nexport { SearchBar } from './ui/SearchBar';\nexport { ThemeToggle } from './ui/ThemeToggle';\nexport { LanguageSelector } from './ui/LanguageSelector';\nexport { SkipToContent } from './ui/SkipToContent';\n\n// Layout Components\nexport { Navigation } from './layout/Navigation';\nexport { Footer } from './layout/Footer';\n\n// Pet Components\nexport { PetCard } from './pets/PetCard';\n\n// Section Components\nexport { Hero } from './sections/Hero';\nexport { SuccessStories } from './sections/SuccessStories';\nexport { Testimonials } from './sections/Testimonials';\nexport { QuickFilters } from './sections/QuickFilters';\n\n// Types\nexport type { ButtonProps } from './ui/Button';\nexport type { CardProps } from './ui/Card';\nexport type { InputProps } from './ui/Input';\nexport type { BadgeProps } from './ui/Badge';\nexport type { SearchFilters } from './ui/SearchBar';\nexport type { Pet, PetCardProps } from './pets/PetCard';\nexport type { HeroSlide, HeroProps } from './sections/Hero';\nexport type { SuccessStory, SuccessStoriesProps } from './sections/SuccessStories';\nexport type { Testimonial, TestimonialsProps } from './sections/Testimonials';\nexport type { FilterOption, QuickFiltersProps } from './sections/QuickFilters';\n"], "names": [], "mappings": "AAAA,gBAAgB;;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AAEA,iBAAiB;AACjB;AAEA,qBAAqB;AACrB;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { \n  Heart, \n  Calendar, \n  FileText, \n  Bell, \n  User, \n  Settings,\n  PlusCircle,\n  Clock,\n  CheckCircle,\n  AlertCircle,\n  Star,\n  MessageCircle,\n  Camera,\n  Award\n} from 'lucide-react';\nimport { \n  <PERSON><PERSON>, \n  Card, \n  CardContent, \n  CardHeader, \n  CardTitle, \n  Badge,\n  Navigation,\n  Footer,\n  PetCard\n} from '@/components';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Pet } from '@/components/pets/PetCard';\n\ninterface Application {\n  id: string;\n  petId: string;\n  petName: string;\n  petImage: string;\n  status: 'pending' | 'approved' | 'rejected' | 'interview' | 'home-visit';\n  submittedDate: string;\n  lastUpdate: string;\n  nextStep?: string;\n}\n\ninterface Appointment {\n  id: string;\n  type: 'meet-greet' | 'home-visit' | 'interview' | 'pickup';\n  petName: string;\n  date: string;\n  time: string;\n  location: string;\n  status: 'scheduled' | 'completed' | 'cancelled';\n}\n\nconst sampleApplications: Application[] = [\n  {\n    id: '1',\n    petId: '1',\n    petName: 'Buddy',\n    petImage: '/images/pets/buddy.jpg',\n    status: 'approved',\n    submittedDate: '2024-01-15',\n    lastUpdate: '2024-01-20',\n    nextStep: 'Schedule pickup appointment'\n  },\n  {\n    id: '2',\n    petId: '2',\n    petName: 'Luna',\n    petImage: '/images/pets/luna.jpg',\n    status: 'interview',\n    submittedDate: '2024-01-18',\n    lastUpdate: '2024-01-22',\n    nextStep: 'Phone interview scheduled for Jan 25'\n  },\n  {\n    id: '3',\n    petId: '3',\n    petName: 'Charlie',\n    petImage: '/images/pets/charlie.jpg',\n    status: 'pending',\n    submittedDate: '2024-01-20',\n    lastUpdate: '2024-01-20',\n    nextStep: 'Application under review'\n  }\n];\n\nconst sampleAppointments: Appointment[] = [\n  {\n    id: '1',\n    type: 'pickup',\n    petName: 'Buddy',\n    date: '2024-01-25',\n    time: '2:00 PM',\n    location: 'Animal Heaven Main Shelter',\n    status: 'scheduled'\n  },\n  {\n    id: '2',\n    type: 'interview',\n    petName: 'Luna',\n    date: '2024-01-25',\n    time: '10:00 AM',\n    location: 'Phone Interview',\n    status: 'scheduled'\n  }\n];\n\nconst savedPets: Pet[] = [\n  {\n    id: '4',\n    name: 'Max',\n    type: 'dog',\n    breed: 'Labrador Mix',\n    age: { value: 2, unit: 'years' },\n    gender: 'male',\n    size: 'large',\n    color: 'Brown',\n    description: 'Friendly and energetic dog looking for an active family.',\n    personality: ['friendly', 'energetic', 'playful'],\n    images: [{ url: '/images/pets/max.jpg', alt: 'Max', isPrimary: true }],\n    status: 'available',\n    location: { shelter: 'Happy Paws', city: 'Mumbai', state: 'Maharashtra' },\n    adoptionFee: 4500,\n    specialNeeds: false,\n    goodWith: { children: true, dogs: true, cats: false },\n    views: 156,\n    likes: 78,\n    featured: false\n  }\n];\n\nconst DashboardPage: React.FC = () => {\n  const { user, logout } = useAuth();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50\">\n        <Navigation isAuthenticated={false} savedPetsCount={0} />\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center\">\n          <h1 className=\"text-2xl font-bold text-neutral-900 mb-4\">Please log in to access your dashboard</h1>\n          <Button variant=\"primary\" onClick={() => window.location.href = '/login'}>\n            Go to Login\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'approved': return 'text-green-600 bg-green-100';\n      case 'pending': return 'text-yellow-600 bg-yellow-100';\n      case 'rejected': return 'text-red-600 bg-red-100';\n      case 'interview': return 'text-blue-600 bg-blue-100';\n      case 'home-visit': return 'text-purple-600 bg-purple-100';\n      default: return 'text-neutral-600 bg-neutral-100';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'approved': return <CheckCircle className=\"w-4 h-4\" />;\n      case 'pending': return <Clock className=\"w-4 h-4\" />;\n      case 'rejected': return <AlertCircle className=\"w-4 h-4\" />;\n      case 'interview': return <MessageCircle className=\"w-4 h-4\" />;\n      case 'home-visit': return <Calendar className=\"w-4 h-4\" />;\n      default: return <FileText className=\"w-4 h-4\" />;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50\">\n      <Navigation isAuthenticated={true} savedPetsCount={savedPets.length} />\n      \n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n            <div>\n              <h1 className=\"font-primary text-3xl font-bold text-neutral-900\">\n                Welcome back, {user.firstName}!\n              </h1>\n              <p className=\"text-neutral-600 mt-1\">\n                Track your adoption journey and manage your account\n              </p>\n            </div>\n            <div className=\"flex gap-3\">\n              <Button variant=\"outline\" leftIcon={<Bell className=\"w-4 h-4\" />}>\n                Notifications\n              </Button>\n              <Button variant=\"outline\" leftIcon={<Settings className=\"w-4 h-4\" />}>\n                Settings\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n          <Card variant=\"warm\">\n            <CardContent className=\"p-6 text-center\">\n              <FileText className=\"w-8 h-8 text-orange-600 mx-auto mb-3\" />\n              <div className=\"text-2xl font-bold text-neutral-900 mb-1\">\n                {sampleApplications.length}\n              </div>\n              <div className=\"text-sm text-neutral-600\">Active Applications</div>\n            </CardContent>\n          </Card>\n\n          <Card variant=\"elevated\">\n            <CardContent className=\"p-6 text-center\">\n              <Heart className=\"w-8 h-8 text-red-600 mx-auto mb-3\" />\n              <div className=\"text-2xl font-bold text-neutral-900 mb-1\">\n                {savedPets.length}\n              </div>\n              <div className=\"text-sm text-neutral-600\">Saved Pets</div>\n            </CardContent>\n          </Card>\n\n          <Card variant=\"outlined\">\n            <CardContent className=\"p-6 text-center\">\n              <Calendar className=\"w-8 h-8 text-blue-600 mx-auto mb-3\" />\n              <div className=\"text-2xl font-bold text-neutral-900 mb-1\">\n                {sampleAppointments.length}\n              </div>\n              <div className=\"text-sm text-neutral-600\">Upcoming Appointments</div>\n            </CardContent>\n          </Card>\n\n          <Card variant=\"outlined\">\n            <CardContent className=\"p-6 text-center\">\n              <Award className=\"w-8 h-8 text-green-600 mx-auto mb-3\" />\n              <div className=\"text-2xl font-bold text-neutral-900 mb-1\">0</div>\n              <div className=\"text-sm text-neutral-600\">Successful Adoptions</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Main Content */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Left Column - Applications & Appointments */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Recent Applications */}\n            <Card variant=\"elevated\">\n              <CardHeader>\n                <div className=\"flex justify-between items-center\">\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <FileText className=\"w-5 h-5\" />\n                    Adoption Applications\n                  </CardTitle>\n                  <Button variant=\"outline\" size=\"sm\">\n                    View All\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {sampleApplications.map((application) => (\n                    <div key={application.id} className=\"flex items-center gap-4 p-4 border border-neutral-200 rounded-lg\">\n                      <div className=\"w-16 h-16 bg-gradient-to-br from-orange-200 to-green-200 rounded-lg flex items-center justify-center\">\n                        <span className=\"text-2xl\">🐕</span>\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2 mb-1\">\n                          <h3 className=\"font-semibold text-lg\">{application.petName}</h3>\n                          <Badge className={getStatusColor(application.status)} size=\"sm\">\n                            <span className=\"flex items-center gap-1\">\n                              {getStatusIcon(application.status)}\n                              {application.status.charAt(0).toUpperCase() + application.status.slice(1)}\n                            </span>\n                          </Badge>\n                        </div>\n                        <p className=\"text-neutral-600 text-sm mb-2\">\n                          Applied on {new Date(application.submittedDate).toLocaleDateString()}\n                        </p>\n                        {application.nextStep && (\n                          <p className=\"text-blue-600 text-sm font-medium\">\n                            Next: {application.nextStep}\n                          </p>\n                        )}\n                      </div>\n                      \n                      <Button variant=\"outline\" size=\"sm\">\n                        View Details\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Upcoming Appointments */}\n            <Card variant=\"warm\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Calendar className=\"w-5 h-5\" />\n                  Upcoming Appointments\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {sampleAppointments.map((appointment) => (\n                    <div key={appointment.id} className=\"flex items-center gap-4 p-4 bg-white rounded-lg border border-orange-200\">\n                      <div className=\"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center\">\n                        <Calendar className=\"w-6 h-6 text-orange-600\" />\n                      </div>\n                      \n                      <div className=\"flex-1\">\n                        <h3 className=\"font-semibold text-lg mb-1\">\n                          {appointment.type.split('-').map(word => \n                            word.charAt(0).toUpperCase() + word.slice(1)\n                          ).join(' ')} - {appointment.petName}\n                        </h3>\n                        <p className=\"text-neutral-600 text-sm\">\n                          {new Date(appointment.date).toLocaleDateString()} at {appointment.time}\n                        </p>\n                        <p className=\"text-neutral-500 text-xs\">\n                          {appointment.location}\n                        </p>\n                      </div>\n                      \n                      <div className=\"flex gap-2\">\n                        <Button variant=\"outline\" size=\"sm\">\n                          Reschedule\n                        </Button>\n                        <Button variant=\"primary\" size=\"sm\">\n                          Join\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Right Column - Profile & Saved Pets */}\n          <div className=\"space-y-8\">\n            {/* Profile Summary */}\n            <Card variant=\"outlined\">\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <User className=\"w-5 h-5\" />\n                  Profile Summary\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-center mb-6\">\n                  <div className=\"w-20 h-20 bg-gradient-to-br from-orange-200 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <span className=\"text-2xl font-semibold text-neutral-700\">\n                      {user.firstName[0]}{user.lastName[0]}\n                    </span>\n                  </div>\n                  <h3 className=\"font-semibold text-lg\">{user.firstName} {user.lastName}</h3>\n                  <p className=\"text-neutral-600 text-sm\">{user.email}</p>\n                  <Badge variant=\"primary\" size=\"sm\" className=\"mt-2\">\n                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}\n                  </Badge>\n                </div>\n                \n                <div className=\"space-y-3 text-sm\">\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-neutral-600\">Member since:</span>\n                    <span className=\"font-medium\">\n                      {new Date(user.createdAt).toLocaleDateString()}\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span className=\"text-neutral-600\">Profile completion:</span>\n                    <span className=\"font-medium text-green-600\">85%</span>\n                  </div>\n                </div>\n                \n                <Button variant=\"outline\" className=\"w-full mt-4\">\n                  Edit Profile\n                </Button>\n              </CardContent>\n            </Card>\n\n            {/* Saved Pets */}\n            <Card variant=\"elevated\">\n              <CardHeader>\n                <div className=\"flex justify-between items-center\">\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <Heart className=\"w-5 h-5\" />\n                    Saved Pets\n                  </CardTitle>\n                  <Button variant=\"outline\" size=\"sm\">\n                    View All\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent>\n                {savedPets.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {savedPets.slice(0, 2).map((pet) => (\n                      <div key={pet.id} className=\"border border-neutral-200 rounded-lg p-3\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"w-12 h-12 bg-gradient-to-br from-orange-200 to-green-200 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-lg\">🐕</span>\n                          </div>\n                          <div className=\"flex-1\">\n                            <h4 className=\"font-semibold\">{pet.name}</h4>\n                            <p className=\"text-neutral-600 text-sm\">{pet.breed}</p>\n                          </div>\n                          <Button variant=\"ghost\" size=\"sm\">\n                            <Heart className=\"w-4 h-4 text-red-500 fill-current\" />\n                          </Button>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <Heart className=\"w-12 h-12 text-neutral-400 mx-auto mb-3\" />\n                    <p className=\"text-neutral-600 mb-4\">No saved pets yet</p>\n                    <Button variant=\"outline\" size=\"sm\">\n                      Browse Pets\n                    </Button>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Quick Actions */}\n            <Card variant=\"warm\">\n              <CardHeader>\n                <CardTitle>Quick Actions</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <Button variant=\"primary\" className=\"w-full\" leftIcon={<PlusCircle className=\"w-4 h-4\" />}>\n                    Start New Application\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\" leftIcon={<Calendar className=\"w-4 h-4\" />}>\n                    Schedule Visit\n                  </Button>\n                  <Button variant=\"outline\" className=\"w-full\" leftIcon={<Camera className=\"w-4 h-4\" />}>\n                    Share Pet Photo\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      <Footer />\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AA9BA;;;;;AAsDA,MAAM,qBAAoC;IACxC;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAe;QACf,YAAY;QACZ,UAAU;IACZ;CACD;AAED,MAAM,qBAAoC;IACxC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;IACV;CACD;AAED,MAAM,YAAmB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,KAAK;YAAE,OAAO;YAAG,MAAM;QAAQ;QAC/B,QAAQ;QACR,MAAM;QACN,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;YAAa;SAAU;QACjD,QAAQ;YAAC;gBAAE,KAAK;gBAAwB,KAAK;gBAAO,WAAW;YAAK;SAAE;QACtE,QAAQ;QACR,UAAU;YAAE,SAAS;YAAc,MAAM;YAAU,OAAO;QAAc;QACxE,aAAa;QACb,cAAc;QACd,UAAU;YAAE,UAAU;YAAM,MAAM;YAAM,MAAM;QAAM;QACpD,OAAO;QACP,OAAO;QACP,UAAU;IACZ;CACD;AAED,MAAM,gBAA0B;;IAC9B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,aAAU;oBAAC,iBAAiB;oBAAO,gBAAgB;;;;;;8BACpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;sCAAU;;;;;;;;;;;;;;;;;;IAMlF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAY,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC/C,KAAK;gBAAW,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YACxC,KAAK;gBAAY,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAC/C,KAAK;gBAAa,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClD,KAAK;gBAAc,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC9C;gBAAS,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QACtC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6IAAA,CAAA,aAAU;gBAAC,iBAAiB;gBAAM,gBAAgB,UAAU,MAAM;;;;;;0BAEnE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;;gDAAmD;gDAChD,KAAK,SAAS;gDAAC;;;;;;;sDAEhC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,wBAAU,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;sDAAc;;;;;;sDAGlE,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;sDAAc;;;;;;;;;;;;;;;;;;;;;;;kCAQ5E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;0CACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAI,WAAU;sDACZ,mBAAmB,MAAM;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;0CAI9C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;0CACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAI,WAAU;sDACZ,UAAU,MAAM;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;0CAI9C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;0CACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;4CAAI,WAAU;sDACZ,mBAAmB,MAAM;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;0CAI9C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,SAAQ;0CACZ,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;kCAMhD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;wCAAC,SAAQ;;0DACZ,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAGlC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;0DAKxC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,GAAG,CAAC,CAAC,4BACvB,6LAAC;4DAAyB,WAAU;;8EAClC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAK,WAAU;kFAAW;;;;;;;;;;;8EAG7B,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAyB,YAAY,OAAO;;;;;;8FAC1D,6LAAC,oIAAA,CAAA,QAAK;oFAAC,WAAW,eAAe,YAAY,MAAM;oFAAG,MAAK;8FACzD,cAAA,6LAAC;wFAAK,WAAU;;4FACb,cAAc,YAAY,MAAM;4FAChC,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,YAAY,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;sFAI7E,6LAAC;4EAAE,WAAU;;gFAAgC;gFAC/B,IAAI,KAAK,YAAY,aAAa,EAAE,kBAAkB;;;;;;;wEAEnE,YAAY,QAAQ,kBACnB,6LAAC;4EAAE,WAAU;;gFAAoC;gFACxC,YAAY,QAAQ;;;;;;;;;;;;;8EAKjC,6LAAC,qIAAA,CAAA,SAAM;oEAAC,SAAQ;oEAAU,MAAK;8EAAK;;;;;;;2DAzB5B,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;kDAmChC,6LAAC,mIAAA,CAAA,OAAI;wCAAC,SAAQ;;0DACZ,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIpC,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,GAAG,CAAC,CAAC,4BACvB,6LAAC;4DAAyB,WAAU;;8EAClC,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;;;;;;8EAGtB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;;gFACX,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAC/B,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAC1C,IAAI,CAAC;gFAAK;gFAAI,YAAY,OAAO;;;;;;;sFAErC,6LAAC;4EAAE,WAAU;;gFACV,IAAI,KAAK,YAAY,IAAI,EAAE,kBAAkB;gFAAG;gFAAK,YAAY,IAAI;;;;;;;sFAExE,6LAAC;4EAAE,WAAU;sFACV,YAAY,QAAQ;;;;;;;;;;;;8EAIzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAAK;;;;;;sFAGpC,6LAAC,qIAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAU,MAAK;sFAAK;;;;;;;;;;;;;2DAvB9B,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAmClC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC,mIAAA,CAAA,OAAI;wCAAC,SAAQ;;0DACZ,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;0DAIhC,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;;wEACb,KAAK,SAAS,CAAC,EAAE;wEAAE,KAAK,QAAQ,CAAC,EAAE;;;;;;;;;;;;0EAGxC,6LAAC;gEAAG,WAAU;;oEAAyB,KAAK,SAAS;oEAAC;oEAAE,KAAK,QAAQ;;;;;;;0EACrE,6LAAC;gEAAE,WAAU;0EAA4B,KAAK,KAAK;;;;;;0EACnD,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,MAAK;gEAAK,WAAU;0EAC1C,KAAK,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;kEAIzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,6LAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0EAGhD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmB;;;;;;kFACnC,6LAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;kEAIjD,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,WAAU;kEAAc;;;;;;;;;;;;;;;;;;kDAOtD,6LAAC,mIAAA,CAAA,OAAI;wCAAC,SAAQ;;0DACZ,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAY;;;;;;;sEAG/B,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;0DAKxC,6LAAC,mIAAA,CAAA,cAAW;0DACT,UAAU,MAAM,GAAG,kBAClB,6LAAC;oDAAI,WAAU;8DACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC;4DAAiB,WAAU;sEAC1B,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;kFAE5B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAAiB,IAAI,IAAI;;;;;;0FACvC,6LAAC;gFAAE,WAAU;0FAA4B,IAAI,KAAK;;;;;;;;;;;;kFAEpD,6LAAC,qIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,MAAK;kFAC3B,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;;;;;;;2DAVb,IAAI,EAAE;;;;;;;;;6GAiBpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,MAAK;sEAAK;;;;;;;;;;;;;;;;;;;;;;;kDAS5C,6LAAC,mIAAA,CAAA,OAAI;wCAAC,SAAQ;;0DACZ,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;0DAEb,6LAAC,mIAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;4DAAS,wBAAU,6LAAC,qNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;sEAAc;;;;;;sEAG3F,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;4DAAS,wBAAU,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;sEAAc;;;;;;sEAGzF,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAU,WAAU;4DAAS,wBAAU,6LAAC,yMAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;sEAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnG,6LAAC,yIAAA,CAAA,SAAM;;;;;;;;;;;AAGb;GAjUM;;QACqB,kIAAA,CAAA,UAAO;;;KAD5B;uCAmUS", "debugId": null}}]}