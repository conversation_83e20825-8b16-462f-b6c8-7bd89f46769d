{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/portal/index.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react'\nimport { createPortal } from 'react-dom'\n\ntype PortalProps = {\n  children: React.ReactNode\n  type: string\n}\n\nexport const Portal = ({ children, type }: PortalProps) => {\n  const [portalNode, setPortalNode] = useState<HTMLElement | null>(null)\n\n  useEffect(() => {\n    const element = document.createElement(type)\n    document.body.appendChild(element)\n    setPortalNode(element)\n    return () => {\n      document.body.removeChild(element)\n    }\n  }, [type])\n\n  return portalNode ? createPortal(children, portalNode) : null\n}\n"], "names": ["Portal", "children", "type", "portalNode", "setPortalNode", "useState", "useEffect", "element", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "createPortal"], "mappings": ";;;+BAQaA,UAAAA;;;eAAAA;;;uBARuB;0BACP;AAOtB,MAAMA,SAAS,CAAA;QAAC,EAAEC,QAAQ,EAAEC,IAAI,EAAe,GAAA;IACpD,MAAM,CAACC,YAAYC,cAAc,GAAGC,CAAAA,GAAAA,OAAAA,QAAQ,EAAqB;IAEjEC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,MAAMC,UAAUC,SAASC,aAAa,CAACP;QACvCM,SAASE,IAAI,CAACC,WAAW,CAACJ;QAC1BH,cAAcG;QACd,OAAO;YACLC,SAASE,IAAI,CAACE,WAAW,CAACL;QAC5B;IACF,GAAG;QAACL;KAAK;IAET,OAAOC,aAAAA,WAAAA,GAAaU,CAAAA,GAAAA,UAAAA,YAAY,EAACZ,UAAUE,cAAc;AAC3D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/set-attributes-from-props.ts"], "sourcesContent": ["const DOMAttributeNames: Record<string, string> = {\n  acceptCharset: 'accept-charset',\n  className: 'class',\n  htmlFor: 'for',\n  httpEquiv: 'http-equiv',\n  noModule: 'noModule',\n}\n\nconst ignoreProps = [\n  'onLoad',\n  'onReady',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n  'stylesheets',\n]\n\nfunction isBooleanScriptAttribute(\n  attr: string\n): attr is 'async' | 'defer' | 'noModule' {\n  return ['async', 'defer', 'noModule'].includes(attr)\n}\n\nexport function setAttributesFromProps(el: HTMLElement, props: object) {\n  for (const [p, value] of Object.entries(props)) {\n    if (!props.hasOwnProperty(p)) continue\n    if (ignoreProps.includes(p)) continue\n\n    // we don't render undefined props to the DOM\n    if (value === undefined) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[p] || p.toLowerCase()\n\n    if (el.tagName === 'SCRIPT' && isBooleanScriptAttribute(attr)) {\n      // Correctly assign boolean script attributes\n      // https://github.com/vercel/next.js/pull/20748\n      ;(el as HTMLScriptElement)[attr] = !!value\n    } else {\n      el.setAttribute(attr, String(value))\n    }\n\n    // Remove falsy non-zero boolean attributes so they are correctly interpreted\n    // (e.g. if we set them to false, this coerces to the string \"false\", which the browser interprets as true)\n    if (\n      value === false ||\n      (el.tagName === 'SCRIPT' &&\n        isBooleanScriptAttribute(attr) &&\n        (!value || value === 'false'))\n    ) {\n      // Call setAttribute before, as we need to set and unset the attribute to override force async:\n      // https://html.spec.whatwg.org/multipage/scripting.html#script-force-async\n      el.setAttribute(attr, '')\n      el.removeAttribute(attr)\n    }\n  }\n}\n"], "names": ["setAttributesFromProps", "DOMAttributeNames", "acceptCharset", "className", "htmlFor", "httpEquiv", "noModule", "ignoreProps", "isBooleanScriptAttribute", "attr", "includes", "el", "props", "p", "value", "Object", "entries", "hasOwnProperty", "undefined", "toLowerCase", "tagName", "setAttribute", "String", "removeAttribute"], "mappings": ";;;+BAwBgBA,0BAAAA;;;eAAAA;;;AAxBhB,MAAMC,oBAA4C;IAChDC,eAAe;IACfC,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC,UAAU;AACZ;AAEA,MAAMC,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,yBACPC,IAAY;IAEZ,OAAO;QAAC;QAAS;QAAS;KAAW,CAACC,QAAQ,CAACD;AACjD;AAEO,SAAST,uBAAuBW,EAAe,EAAEC,KAAa;IACnE,KAAK,MAAM,CAACC,GAAGC,MAAM,IAAIC,OAAOC,OAAO,CAACJ,OAAQ;QAC9C,IAAI,CAACA,MAAMK,cAAc,CAACJ,IAAI;QAC9B,IAAIN,YAAYG,QAAQ,CAACG,IAAI;QAE7B,6CAA6C;QAC7C,IAAIC,UAAUI,WAAW;YACvB;QACF;QAEA,MAAMT,OAAOR,iBAAiB,CAACY,EAAE,IAAIA,EAAEM,WAAW;QAElD,IAAIR,GAAGS,OAAO,KAAK,YAAYZ,yBAAyBC,OAAO;YAC7D,6CAA6C;YAC7C,+CAA+C;;YAC7CE,EAAwB,CAACF,KAAK,GAAG,CAAC,CAACK;QACvC,OAAO;YACLH,GAAGU,YAAY,CAACZ,MAAMa,OAAOR;QAC/B;QAEA,6EAA6E;QAC7E,2GAA2G;QAC3G,IACEA,UAAU,SACTH,GAAGS,OAAO,KAAK,YACdZ,yBAAyBC,SACxB,CAAA,CAACK,SAASA,UAAU,OAAM,GAC7B;YACA,+FAA+F;YAC/F,2EAA2E;YAC3EH,GAAGU,YAAY,CAACZ,MAAM;YACtBE,GAAGY,eAAe,CAACd;QACrB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/head-manager.ts"], "sourcesContent": ["import { setAttributesFromProps } from './set-attributes-from-props'\n\nimport type { JSX } from 'react'\n\nfunction reactElementToDOM({ type, props }: JSX.Element): HTMLElement {\n  const el: HTMLElement = document.createElement(type)\n  setAttributesFromProps(el, props)\n\n  const { children, dangerouslySetInnerHTML } = props\n  if (dangerouslySetInnerHTML) {\n    el.innerHTML = dangerouslySetInnerHTML.__html || ''\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n  }\n  return el\n}\n\n/**\n * When a `nonce` is present on an element, browsers such as Chrome and Firefox strip it out of the\n * actual HTML attributes for security reasons *when the element is added to the document*. Thus,\n * given two equivalent elements that have nonces, `Element,isEqualNode()` will return false if one\n * of those elements gets added to the document. Although the `element.nonce` property will be the\n * same for both elements, the one that was added to the document will return an empty string for\n * its nonce HTML attribute value.\n *\n * This custom `isEqualNode()` function therefore removes the nonce value from the `newTag` before\n * comparing it to `oldTag`, restoring it afterwards.\n *\n * For more information, see:\n * https://bugs.chromium.org/p/chromium/issues/detail?id=1211471#c12\n */\nexport function isEqualNode(oldTag: Element, newTag: Element) {\n  if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n    const nonce = newTag.getAttribute('nonce')\n    // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n    // be stripped if there is no content security policy response header that includes a nonce.\n    if (nonce && !oldTag.getAttribute('nonce')) {\n      const cloneTag = newTag.cloneNode(true) as typeof newTag\n      cloneTag.setAttribute('nonce', '')\n      cloneTag.nonce = nonce\n      return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag)\n    }\n  }\n\n  return oldTag.isEqualNode(newTag)\n}\n\nlet updateElements: (type: string, components: JSX.Element[]) => void\n\nif (process.env.__NEXT_STRICT_NEXT_HEAD) {\n  updateElements = (type, components) => {\n    const headEl = document.querySelector('head')\n    if (!headEl) return\n\n    const oldTags = new Set(headEl.querySelectorAll(`${type}[data-next-head]`))\n\n    if (type === 'meta') {\n      const metaCharset = headEl.querySelector('meta[charset]')\n      if (metaCharset !== null) {\n        oldTags.add(metaCharset)\n      }\n    }\n\n    const newTags: Element[] = []\n    for (let i = 0; i < components.length; i++) {\n      const component = components[i]\n      const newTag = reactElementToDOM(component)\n      newTag.setAttribute('data-next-head', '')\n\n      let isNew = true\n      for (const oldTag of oldTags) {\n        if (isEqualNode(oldTag, newTag)) {\n          oldTags.delete(oldTag)\n          isNew = false\n          break\n        }\n      }\n\n      if (isNew) {\n        newTags.push(newTag)\n      }\n    }\n\n    for (const oldTag of oldTags) {\n      oldTag.parentNode?.removeChild(oldTag)\n    }\n\n    for (const newTag of newTags) {\n      // meta[charset] must be first element so special case\n      if (\n        newTag.tagName.toLowerCase() === 'meta' &&\n        newTag.getAttribute('charset') !== null\n      ) {\n        headEl.prepend(newTag)\n      }\n      headEl.appendChild(newTag)\n    }\n  }\n} else {\n  updateElements = (type, components) => {\n    const headEl = document.getElementsByTagName('head')[0]\n    const headCountEl: HTMLMetaElement = headEl.querySelector(\n      'meta[name=next-head-count]'\n    ) as HTMLMetaElement\n    if (process.env.NODE_ENV !== 'production') {\n      if (!headCountEl) {\n        console.error(\n          'Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing'\n        )\n        return\n      }\n    }\n\n    const headCount = Number(headCountEl.content)\n    const oldTags: Element[] = []\n\n    for (\n      let i = 0, j = headCountEl.previousElementSibling;\n      i < headCount;\n      i++, j = j?.previousElementSibling || null\n    ) {\n      if (j?.tagName?.toLowerCase() === type) {\n        oldTags.push(j)\n      }\n    }\n    const newTags = (components.map(reactElementToDOM) as HTMLElement[]).filter(\n      (newTag) => {\n        for (let k = 0, len = oldTags.length; k < len; k++) {\n          const oldTag = oldTags[k]\n          if (isEqualNode(oldTag, newTag)) {\n            oldTags.splice(k, 1)\n            return false\n          }\n        }\n        return true\n      }\n    )\n\n    oldTags.forEach((t) => t.parentNode?.removeChild(t))\n    newTags.forEach((t) => headEl.insertBefore(t, headCountEl))\n    headCountEl.content = (\n      headCount -\n      oldTags.length +\n      newTags.length\n    ).toString()\n  }\n}\n\nexport default function initHeadManager(): {\n  mountedInstances: Set<unknown>\n  updateHead: (head: JSX.Element[]) => void\n} {\n  return {\n    mountedInstances: new Set(),\n    updateHead: (head: JSX.Element[]) => {\n      const tags: Record<string, JSX.Element[]> = {}\n\n      head.forEach((h) => {\n        if (\n          // If the font tag is loaded only on client navigation\n          // it won't be inlined. In this case revert to the original behavior\n          h.type === 'link' &&\n          h.props['data-optimized-fonts']\n        ) {\n          if (\n            document.querySelector(`style[data-href=\"${h.props['data-href']}\"]`)\n          ) {\n            return\n          } else {\n            h.props.href = h.props['data-href']\n            h.props['data-href'] = undefined\n          }\n        }\n\n        const components = tags[h.type] || []\n        components.push(h)\n        tags[h.type] = components\n      })\n\n      const titleComponent = tags.title ? tags.title[0] : null\n      let title = ''\n      if (titleComponent) {\n        const { children } = titleComponent.props\n        title =\n          typeof children === 'string'\n            ? children\n            : Array.isArray(children)\n              ? children.join('')\n              : ''\n      }\n      if (title !== document.title) document.title = title\n      ;['meta', 'base', 'link', 'style', 'script'].forEach((type) => {\n        updateElements(type, tags[type] || [])\n      })\n    },\n  }\n}\n"], "names": ["initHeadManager", "isEqualNode", "reactElementToDOM", "type", "props", "el", "document", "createElement", "setAttributesFromProps", "children", "dangerouslySetInnerHTML", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "oldTag", "newTag", "HTMLElement", "nonce", "getAttribute", "cloneTag", "cloneNode", "setAttribute", "updateElements", "process", "env", "__NEXT_STRICT_NEXT_HEAD", "components", "headEl", "querySelector", "oldTags", "Set", "querySelectorAll", "metaCharset", "add", "newTags", "i", "length", "component", "isNew", "delete", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "tagName", "toLowerCase", "prepend", "append<PERSON><PERSON><PERSON>", "getElementsByTagName", "headCountEl", "NODE_ENV", "console", "error", "headCount", "Number", "content", "j", "previousElementSibling", "map", "filter", "k", "len", "splice", "for<PERSON>ach", "t", "insertBefore", "toString", "mountedInstances", "updateHead", "head", "tags", "h", "href", "undefined", "titleComponent", "title"], "mappings": "AAsDI0B,QAAQC,GAAG,CAACC,uBAAuB;;;;;;;;;;;;;;;;IAmGvC,OAgDC,EAAA;eAhDuB5B;;IArHRC,WAAW,EAAA;eAAXA;;;wCApCuB;AAIvC,SAASC,kBAAkB,KAA4B;IAA5B,IAAA,EAAEC,IAAI,EAAEC,KAAK,EAAe,GAA5B;IACzB,MAAMC,KAAkBC,SAASC,aAAa,CAACJ;IAC/CK,CAAAA,GAAAA,wBAAAA,sBAAsB,EAACH,IAAID;IAE3B,MAAM,EAAEK,QAAQ,EAAEC,uBAAuB,EAAE,GAAGN;IAC9C,IAAIM,yBAAyB;QAC3BL,GAAGM,SAAS,GAAGD,wBAAwBE,MAAM,IAAI;IACnD,OAAO,IAAIH,UAAU;QACnBJ,GAAGQ,WAAW,GACZ,OAAOJ,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACZA,SAASO,IAAI,CAAC,MACd;IACV;IACA,OAAOX;AACT;AAgBO,SAASJ,YAAYgB,MAAe,EAAEC,MAAe;IAC1D,IAAID,kBAAkBE,eAAeD,kBAAkBC,aAAa;QAClE,MAAMC,QAAQF,OAAOG,YAAY,CAAC;QAClC,8FAA8F;QAC9F,4FAA4F;QAC5F,IAAID,SAAS,CAACH,OAAOI,YAAY,CAAC,UAAU;YAC1C,MAAMC,WAAWJ,OAAOK,SAAS,CAAC;YAClCD,SAASE,YAAY,CAAC,SAAS;YAC/BF,SAASF,KAAK,GAAGA;YACjB,OAAOA,UAAUH,OAAOG,KAAK,IAAIH,OAAOhB,WAAW,CAACqB;QACtD;IACF;IAEA,OAAOL,OAAOhB,WAAW,CAACiB;AAC5B;AAEA,IAAIO;AAEJ,wCAAyC;IACvCA,iBAAiB,CAACtB,MAAM0B;QACtB,MAAMC,SAASxB,SAASyB,aAAa,CAAC;QACtC,IAAI,CAACD,QAAQ;QAEb,MAAME,UAAU,IAAIC,IAAIH,OAAOI,gBAAgB,CAAE,KAAE/B,OAAK;QAExD,IAAIA,SAAS,QAAQ;YACnB,MAAMgC,cAAcL,OAAOC,aAAa,CAAC;YACzC,IAAII,gBAAgB,MAAM;gBACxBH,QAAQI,GAAG,CAACD;YACd;QACF;QAEA,MAAME,UAAqB,EAAE;QAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIT,WAAWU,MAAM,EAAED,IAAK;YAC1C,MAAME,YAAYX,UAAU,CAACS,EAAE;YAC/B,MAAMpB,SAAShB,kBAAkBsC;YACjCtB,OAAOM,YAAY,CAAC,kBAAkB;YAEtC,IAAIiB,QAAQ;YACZ,KAAK,MAAMxB,UAAUe,QAAS;gBAC5B,IAAI/B,YAAYgB,QAAQC,SAAS;oBAC/Bc,QAAQU,MAAM,CAACzB;oBACfwB,QAAQ;oBACR;gBACF;YACF;YAEA,IAAIA,OAAO;gBACTJ,QAAQM,IAAI,CAACzB;YACf;QACF;QAEA,KAAK,MAAMD,UAAUe,QAAS;gBAC5Bf;aAAAA,qBAAAA,OAAO2B,UAAU,KAAA,OAAA,KAAA,IAAjB3B,mBAAmB4B,WAAW,CAAC5B;QACjC;QAEA,KAAK,MAAMC,UAAUmB,QAAS;YAC5B,sDAAsD;YACtD,IACEnB,OAAO4B,OAAO,CAACC,WAAW,OAAO,UACjC7B,OAAOG,YAAY,CAAC,eAAe,MACnC;gBACAS,OAAOkB,OAAO,CAAC9B;YACjB;YACAY,OAAOmB,WAAW,CAAC/B;QACrB;IACF;AACF,OAAO;;AAkDQ,SAASlB;IAItB,OAAO;QACLqE,kBAAkB,IAAIpC;QACtBqC,YAAY,CAACC;YACX,MAAMC,OAAsC,CAAC;YAE7CD,KAAKN,OAAO,CAAC,CAACQ;gBACZ,IACE,AACA,sDADsD,cACc;gBACpEA,EAAEtE,IAAI,KAAK,UACXsE,EAAErE,KAAK,CAAC,uBAAuB,EAC/B;oBACA,IACEE,SAASyB,aAAa,CAAE,sBAAmB0C,EAAErE,KAAK,CAAC,YAAY,GAAC,OAChE;wBACA;oBACF,OAAO;wBACLqE,EAAErE,KAAK,CAACsE,IAAI,GAAGD,EAAErE,KAAK,CAAC,YAAY;wBACnCqE,EAAErE,KAAK,CAAC,YAAY,GAAGuE;oBACzB;gBACF;gBAEA,MAAM9C,aAAa2C,IAAI,CAACC,EAAEtE,IAAI,CAAC,IAAI,EAAE;gBACrC0B,WAAWc,IAAI,CAAC8B;gBAChBD,IAAI,CAACC,EAAEtE,IAAI,CAAC,GAAG0B;YACjB;YAEA,MAAM+C,iBAAiBJ,KAAKK,KAAK,GAAGL,KAAKK,KAAK,CAAC,EAAE,GAAG;YACpD,IAAIA,QAAQ;YACZ,IAAID,gBAAgB;gBAClB,MAAM,EAAEnE,QAAQ,EAAE,GAAGmE,eAAexE,KAAK;gBACzCyE,QACE,OAAOpE,aAAa,WAChBA,WACAK,MAAMC,OAAO,CAACN,YACZA,SAASO,IAAI,CAAC,MACd;YACV;YACA,IAAI6D,UAAUvE,SAASuE,KAAK,EAAEvE,SAASuE,KAAK,GAAGA;YAC9C;gBAAC;gBAAQ;gBAAQ;gBAAQ;gBAAS;aAAS,CAACZ,OAAO,CAAC,CAAC9D;gBACpDsB,eAAetB,MAAMqE,IAAI,CAACrE,KAAK,IAAI,EAAE;YACvC;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/normalize-trailing-slash.ts"], "sourcesContent": ["import { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { parsePath } from '../shared/lib/router/utils/parse-path'\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = (path: string) => {\n  if (!path.startsWith('/') || process.env.__NEXT_MANUAL_TRAILING_SLASH) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  if (process.env.__NEXT_TRAILING_SLASH) {\n    if (/\\.[^/]+\\/?$/.test(pathname)) {\n      return `${removeTrailingSlash(pathname)}${query}${hash}`\n    } else if (pathname.endsWith('/')) {\n      return `${pathname}${query}${hash}`\n    } else {\n      return `${pathname}/${query}${hash}`\n    }\n  }\n\n  return `${removeTrailingSlash(pathname)}${query}${hash}`\n}\n"], "names": ["normalizePathTrailingSlash", "path", "startsWith", "process", "env", "__NEXT_MANUAL_TRAILING_SLASH", "pathname", "query", "hash", "parsePath", "__NEXT_TRAILING_SLASH", "test", "removeTrailingSlash", "endsWith"], "mappings": "AAQ+BG,QAAQC,GAAG,CAACC,4BAA4B;;;;;+BAD1DL,8BAAAA;;;eAAAA;;;qCAPuB;2BACV;AAMnB,MAAMA,6BAA6B,CAACC;IACzC,IAAI,CAACA,KAAKC,UAAU,CAAC,kDAAkD;QACrE,OAAOD;IACT;IAEA,MAAM,EAAEK,QAAQ,EAAEC,KAAK,EAAEC,IAAI,EAAE,GAAGC,CAAAA,GAAAA,WAAAA,SAAS,EAACR;IAC5C,IAAIE,QAAQC,GAAG,CAACM,qBAAqB,EAAE;;IAUvC,OAAQ,KAAEE,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACN,YAAYC,QAAQC;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/add-base-path.ts"], "sourcesContent": ["import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n"], "names": ["addBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "required", "normalizePathTrailingSlash", "__NEXT_MANUAL_CLIENT_BASE_PATH", "addPathPrefix"], "mappings": "AAGkBE,QAAQC,GAAG,CAACC,sBAAsB;;;;;+BAEpCJ,eAAAA;;;eAAAA;;;+BALc;wCACa;AAE3C,MAAMC,mDAA6D;AAE5D,SAASD,YAAYK,IAAY,EAAEC,QAAkB;IAC1D,OAAOC,CAAAA,GAAAA,wBAAAA,0BAA0B,EAC/BL,QAAQC,GAAG,CAACK,0BACRH,IADsC,IAAI,CAACC,iBAE3CG,CAAAA,GAAAA,eAAAA,aAAa,EAACJ,MAAMJ;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/add-locale.ts"], "sourcesContent": ["import type { addLocale as Fn } from '../shared/lib/router/utils/add-locale'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nexport const addLocale: typeof Fn = (path, ...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return normalizePathTrailingSlash(\n      (\n        require('../shared/lib/router/utils/add-locale') as typeof import('../shared/lib/router/utils/add-locale')\n      ).addLocale(path, ...args)\n    )\n  }\n  return path\n}\n"], "names": ["addLocale", "path", "args", "process", "env", "__NEXT_I18N_SUPPORT", "normalizePathTrailingSlash", "require"], "mappings": "AAIMG,QAAQC,GAAG,CAACC,mBAAmB,EAAE;;;;;+BAD1BL,aAAAA;;;eAAAA;;;wCAF8B;AAEpC,MAAMA,YAAuB,SAACC,IAAAA;qCAASC,OAAAA,IAAAA,MAAAA,OAAAA,IAAAA,OAAAA,IAAAA,IAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,IAAAA,CAAAA,OAAAA,EAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAC5C;;IAOA,OAAOD;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/trusted-types.ts"], "sourcesContent": ["/**\n * Stores the Trusted Types Policy. Starts as undefined and can be set to null\n * if Trusted Types is not supported in the browser.\n */\nlet policy: TrustedTypePolicy | null | undefined\n\n/**\n * Getter for the Trusted Types Policy. If it is undefined, it is instantiated\n * here or set to null if Trusted Types is not supported in the browser.\n */\nfunction getPolicy() {\n  if (typeof policy === 'undefined' && typeof window !== 'undefined') {\n    policy =\n      window.trustedTypes?.createPolicy('nextjs', {\n        createHTML: (input) => input,\n        createScript: (input) => input,\n        createScriptURL: (input) => input,\n      }) || null\n  }\n\n  return policy\n}\n\n/**\n * Unsafely promote a string to a TrustedScriptURL, falling back to strings\n * when Trusted Types are not available.\n * This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will cause a browser to load and execute a resource, e.g. when\n * assigning to script.src.\n */\nexport function __unsafeCreateTrustedScriptURL(\n  url: string\n): TrustedScriptURL | string {\n  return getPolicy()?.createScriptURL(url) || url\n}\n"], "names": ["__unsafeCreateTrustedScriptURL", "policy", "getPolicy", "window", "trustedTypes", "createPolicy", "createHTML", "input", "createScript", "createScriptURL", "url"], "mappings": "AAAA;;;CAGC;;;+BA6BeA,kCAAAA;;;eAAAA;;;AA5BhB,IAAIC;AAEJ;;;CAGC,GACD,SAASC;IACP,IAAI,OAAOD,WAAW,eAAe,OAAOE,WAAW,aAAa;YAEhEA;QADFF,SACEE,CAAAA,CAAAA,uBAAAA,OAAOC,YAAY,KAAA,OAAA,KAAA,IAAnBD,qBAAqBE,YAAY,CAAC,UAAU;YAC1CC,YAAY,CAACC,QAAUA;YACvBC,cAAc,CAACD,QAAUA;YACzBE,iBAAiB,CAACF,QAAUA;QAC9B,EAAA,KAAM;IACV;IAEA,OAAON;AACT;AAWO,SAASD,+BACdU,GAAW;QAEJR;IAAP,OAAOA,CAAAA,CAAAA,aAAAA,WAAAA,KAAAA,OAAAA,KAAAA,IAAAA,WAAaO,eAAe,CAACC,IAAAA,KAAQA;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/request-idle-callback.ts"], "sourcesContent": ["export const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (cb: IdleRequestCallback): number {\n    let start = Date.now()\n    return self.setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: number) {\n    return clearTimeout(id)\n  }\n"], "names": ["cancelIdleCallback", "requestIdleCallback", "self", "bind", "window", "cb", "start", "Date", "now", "setTimeout", "didTimeout", "timeRemaining", "Math", "max", "id", "clearTimeout"], "mappings": ";;;;;;;;;;;;;;IAgBaA,kBAAkB,EAAA;eAAlBA;;IAhBAC,mBAAmB,EAAA;eAAnBA;;;AAAN,MAAMA,sBACV,OAAOC,SAAS,eACfA,KAAKD,mBAAmB,IACxBC,KAAKD,mBAAmB,CAACE,IAAI,CAACC,WAChC,SAAUC,EAAuB;IAC/B,IAAIC,QAAQC,KAAKC,GAAG;IACpB,OAAON,KAAKO,UAAU,CAAC;QACrBJ,GAAG;YACDK,YAAY;YACZC,eAAe;gBACb,OAAOC,KAAKC,GAAG,CAAC,GAAG,KAAMN,CAAAA,KAAKC,GAAG,KAAKF,KAAI;YAC5C;QACF;IACF,GAAG;AACL;AAEK,MAAMN,qBACV,OAAOE,SAAS,eACfA,KAAKF,kBAAkB,IACvBE,KAAKF,kBAAkB,CAACG,IAAI,CAACC,WAC/B,SAAUU,EAAU;IAClB,OAAOC,aAAaD;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/route-loader.ts"], "sourcesContent": ["import type { ComponentType } from 'react'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { __unsafeCreateTrustedScriptURL } from './trusted-types'\nimport { requestIdleCallback } from './request-idle-callback'\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\n\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800\n\ndeclare global {\n  interface Window {\n    __BUILD_MANIFEST?: Record<string, string[]>\n    __BUILD_MANIFEST_CB?: Function\n    __MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __MIDDLEWARE_MANIFEST_CB?: Function\n    __REACT_LOADABLE_MANIFEST?: any\n    __DYNAMIC_CSS_MANIFEST?: any\n    __RSC_MANIFEST?: any\n    __RSC_SERVER_MANIFEST?: any\n    __NEXT_FONT_MANIFEST?: any\n    __SUBRESOURCE_INTEGRITY_MANIFEST?: string\n    __INTERCEPTION_ROUTE_REWRITE_MANIFEST?: string\n  }\n}\n\ninterface LoadedEntrypointSuccess {\n  component: ComponentType\n  exports: any\n}\ninterface LoadedEntrypointFailure {\n  error: unknown\n}\ntype RouteEntrypoint = LoadedEntrypointSuccess | LoadedEntrypointFailure\n\ninterface RouteStyleSheet {\n  href: string\n  content: string\n}\n\ninterface LoadedRouteSuccess extends LoadedEntrypointSuccess {\n  styles: RouteStyleSheet[]\n}\ninterface LoadedRouteFailure {\n  error: unknown\n}\ntype RouteLoaderEntry = LoadedRouteSuccess | LoadedRouteFailure\n\ninterface Future<V> {\n  resolve: (entrypoint: V) => void\n  future: Promise<V>\n}\nfunction withFuture<T extends object>(\n  key: string,\n  map: Map<string, Future<T> | T>,\n  generator?: () => Promise<T>\n): Promise<T> {\n  let entry = map.get(key)\n  if (entry) {\n    if ('future' in entry) {\n      return entry.future\n    }\n    return Promise.resolve(entry)\n  }\n  let resolver: (entrypoint: T) => void\n  const prom: Promise<T> = new Promise<T>((resolve) => {\n    resolver = resolve\n  })\n  map.set(key, { resolve: resolver!, future: prom })\n  return generator\n    ? generator()\n        .then((value) => {\n          resolver(value)\n          return value\n        })\n        .catch((err) => {\n          map.delete(key)\n          throw err\n        })\n    : prom\n}\n\nexport interface RouteLoader {\n  whenEntrypoint(route: string): Promise<RouteEntrypoint>\n  onEntrypoint(route: string, execute: () => unknown): void\n  loadRoute(route: string, prefetch?: boolean): Promise<RouteLoaderEntry>\n  prefetch(route: string): Promise<void>\n}\n\nconst ASSET_LOAD_ERROR = Symbol('ASSET_LOAD_ERROR')\n// TODO: unexport\nexport function markAssetError(err: Error): Error {\n  return Object.defineProperty(err, ASSET_LOAD_ERROR, {})\n}\n\nexport function isAssetError(err?: Error): boolean | undefined {\n  return err && ASSET_LOAD_ERROR in err\n}\n\nfunction hasPrefetch(link?: HTMLLinkElement): boolean {\n  try {\n    link = document.createElement('link')\n    return (\n      // detect IE11 since it supports prefetch but isn't detected\n      // with relList.support\n      (!!window.MSInputMethodContext && !!(document as any).documentMode) ||\n      link.relList.supports('prefetch')\n    )\n  } catch {\n    return false\n  }\n}\n\nconst canPrefetch: boolean = hasPrefetch()\n\nconst getAssetQueryString = () => {\n  return getDeploymentIdQueryOrEmptyString()\n}\n\nfunction prefetchViaDom(\n  href: string,\n  as: string,\n  link?: HTMLLinkElement\n): Promise<any> {\n  return new Promise<void>((resolve, reject) => {\n    const selector = `\n      link[rel=\"prefetch\"][href^=\"${href}\"],\n      link[rel=\"preload\"][href^=\"${href}\"],\n      script[src^=\"${href}\"]`\n    if (document.querySelector(selector)) {\n      return resolve()\n    }\n\n    link = document.createElement('link')\n\n    // The order of property assignment here is intentional:\n    if (as) link!.as = as\n    link!.rel = `prefetch`\n    link!.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n    link!.onload = resolve as any\n    link!.onerror = () =>\n      reject(markAssetError(new Error(`Failed to prefetch: ${href}`)))\n\n    // `href` should always be last:\n    link!.href = href\n\n    document.head.appendChild(link)\n  })\n}\n\nfunction appendScript(\n  src: TrustedScriptURL | string,\n  script?: HTMLScriptElement\n): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    script = document.createElement('script')\n\n    // The order of property assignment here is intentional.\n    // 1. Setup success/failure hooks in case the browser synchronously\n    //    executes when `src` is set.\n    script.onload = resolve\n    script.onerror = () =>\n      reject(markAssetError(new Error(`Failed to load script: ${src}`)))\n\n    // 2. Configure the cross-origin attribute before setting `src` in case the\n    //    browser begins to fetch.\n    script.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n\n    // 3. Finally, set the source and inject into the DOM in case the child\n    //    must be appended for fetching to start.\n    script.src = src as string\n    document.body.appendChild(script)\n  })\n}\n\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise: Promise<void> | undefined\n\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout<T>(\n  p: Promise<T>,\n  ms: number,\n  err: Error\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    let cancelled = false\n\n    p.then((r) => {\n      // Resolved, cancel the timeout\n      cancelled = true\n      resolve(r)\n    }).catch(reject)\n\n    // We wrap these checks separately for better dead-code elimination in\n    // production bundles.\n    if (process.env.NODE_ENV === 'development') {\n      ;(devBuildPromise || Promise.resolve()).then(() => {\n        requestIdleCallback(() =>\n          setTimeout(() => {\n            if (!cancelled) {\n              reject(err)\n            }\n          }, ms)\n        )\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      requestIdleCallback(() =>\n        setTimeout(() => {\n          if (!cancelled) {\n            reject(err)\n          }\n        }, ms)\n      )\n    }\n  })\n}\n\n// TODO: stop exporting or cache the failure\n// It'd be best to stop exporting this. It's an implementation detail. We're\n// only exporting it for backwards compatibility with the `page-loader`.\n// Only cache this response as a last resort if we cannot eliminate all other\n// code branches that use the Build Manifest Callback and push them through\n// the Route Loader interface.\nexport function getClientBuildManifest() {\n  if (self.__BUILD_MANIFEST) {\n    return Promise.resolve(self.__BUILD_MANIFEST)\n  }\n\n  const onBuildManifest = new Promise<Record<string, string[]>>((resolve) => {\n    // Mandatory because this is not concurrent safe:\n    const cb = self.__BUILD_MANIFEST_CB\n    self.__BUILD_MANIFEST_CB = () => {\n      resolve(self.__BUILD_MANIFEST!)\n      cb && cb()\n    }\n  })\n\n  return resolvePromiseWithTimeout(\n    onBuildManifest,\n    MS_MAX_IDLE_DELAY,\n    markAssetError(new Error('Failed to load client build manifest'))\n  )\n}\n\ninterface RouteFiles {\n  scripts: (TrustedScriptURL | string)[]\n  css: string[]\n}\nfunction getFilesForRoute(\n  assetPrefix: string,\n  route: string\n): Promise<RouteFiles> {\n  if (process.env.NODE_ENV === 'development') {\n    const scriptUrl =\n      assetPrefix +\n      '/_next/static/chunks/pages' +\n      encodeURIPath(getAssetPathFromRoute(route, '.js')) +\n      getAssetQueryString()\n    return Promise.resolve({\n      scripts: [__unsafeCreateTrustedScriptURL(scriptUrl)],\n      // Styles are handled by `style-loader` in development:\n      css: [],\n    })\n  }\n  return getClientBuildManifest().then((manifest) => {\n    if (!(route in manifest)) {\n      throw markAssetError(new Error(`Failed to lookup route: ${route}`))\n    }\n    const allFiles = manifest[route].map(\n      (entry) => assetPrefix + '/_next/' + encodeURIPath(entry)\n    )\n    return {\n      scripts: allFiles\n        .filter((v) => v.endsWith('.js'))\n        .map((v) => __unsafeCreateTrustedScriptURL(v) + getAssetQueryString()),\n      css: allFiles\n        .filter((v) => v.endsWith('.css'))\n        .map((v) => v + getAssetQueryString()),\n    }\n  })\n}\n\nexport function createRouteLoader(assetPrefix: string): RouteLoader {\n  const entrypoints: Map<string, Future<RouteEntrypoint> | RouteEntrypoint> =\n    new Map()\n  const loadedScripts: Map<string, Promise<unknown>> = new Map()\n  const styleSheets: Map<string, Promise<RouteStyleSheet>> = new Map()\n  const routes: Map<string, Future<RouteLoaderEntry> | RouteLoaderEntry> =\n    new Map()\n\n  function maybeExecuteScript(\n    src: TrustedScriptURL | string\n  ): Promise<unknown> {\n    // With HMR we might need to \"reload\" scripts when they are\n    // disposed and readded. Executing scripts twice has no functional\n    // differences\n    if (process.env.NODE_ENV !== 'development') {\n      let prom: Promise<unknown> | undefined = loadedScripts.get(src.toString())\n      if (prom) {\n        return prom\n      }\n\n      // Skip executing script if it's already in the DOM:\n      if (document.querySelector(`script[src^=\"${src}\"]`)) {\n        return Promise.resolve()\n      }\n\n      loadedScripts.set(src.toString(), (prom = appendScript(src)))\n      return prom\n    } else {\n      return appendScript(src)\n    }\n  }\n\n  function fetchStyleSheet(href: string): Promise<RouteStyleSheet> {\n    let prom: Promise<RouteStyleSheet> | undefined = styleSheets.get(href)\n    if (prom) {\n      return prom\n    }\n\n    styleSheets.set(\n      href,\n      (prom = fetch(href, { credentials: 'same-origin' })\n        .then((res) => {\n          if (!res.ok) {\n            throw new Error(`Failed to load stylesheet: ${href}`)\n          }\n          return res.text().then((text) => ({ href: href, content: text }))\n        })\n        .catch((err) => {\n          throw markAssetError(err)\n        }))\n    )\n    return prom\n  }\n\n  return {\n    whenEntrypoint(route: string) {\n      return withFuture(route, entrypoints)\n    },\n    onEntrypoint(route: string, execute: undefined | (() => unknown)) {\n      ;(execute\n        ? Promise.resolve()\n            .then(() => execute())\n            .then(\n              (exports: any) => ({\n                component: (exports && exports.default) || exports,\n                exports: exports,\n              }),\n              (err) => ({ error: err })\n            )\n        : Promise.resolve(undefined)\n      ).then((input: RouteEntrypoint | undefined) => {\n        const old = entrypoints.get(route)\n        if (old && 'resolve' in old) {\n          if (input) {\n            entrypoints.set(route, input)\n            old.resolve(input)\n          }\n        } else {\n          if (input) {\n            entrypoints.set(route, input)\n          } else {\n            entrypoints.delete(route)\n          }\n          // when this entrypoint has been resolved before\n          // the route is outdated and we want to invalidate\n          // this cache entry\n          routes.delete(route)\n        }\n      })\n    },\n    loadRoute(route: string, prefetch?: boolean) {\n      return withFuture<RouteLoaderEntry>(route, routes, () => {\n        let devBuildPromiseResolve: () => void\n\n        if (process.env.NODE_ENV === 'development') {\n          devBuildPromise = new Promise<void>((resolve) => {\n            devBuildPromiseResolve = resolve\n          })\n        }\n\n        return resolvePromiseWithTimeout(\n          getFilesForRoute(assetPrefix, route)\n            .then(({ scripts, css }) => {\n              return Promise.all([\n                entrypoints.has(route)\n                  ? []\n                  : Promise.all(scripts.map(maybeExecuteScript)),\n                Promise.all(css.map(fetchStyleSheet)),\n              ] as const)\n            })\n            .then((res) => {\n              return this.whenEntrypoint(route).then((entrypoint) => ({\n                entrypoint,\n                styles: res[1],\n              }))\n            }),\n          MS_MAX_IDLE_DELAY,\n          markAssetError(new Error(`Route did not complete loading: ${route}`))\n        )\n          .then(({ entrypoint, styles }) => {\n            const res: RouteLoaderEntry = Object.assign<\n              { styles: RouteStyleSheet[] },\n              RouteEntrypoint\n            >({ styles: styles! }, entrypoint)\n            return 'error' in entrypoint ? entrypoint : res\n          })\n          .catch((err) => {\n            if (prefetch) {\n              // we don't want to cache errors during prefetch\n              throw err\n            }\n            return { error: err }\n          })\n          .finally(() => devBuildPromiseResolve?.())\n      })\n    },\n    prefetch(route: string): Promise<void> {\n      // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n      // License: Apache 2.0\n      let cn\n      if ((cn = (navigator as any).connection)) {\n        // Don't prefetch if using 2G or if Save-Data is enabled.\n        if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve()\n      }\n      return getFilesForRoute(assetPrefix, route)\n        .then((output) =>\n          Promise.all(\n            canPrefetch\n              ? output.scripts.map((script) =>\n                  prefetchViaDom(script.toString(), 'script')\n                )\n              : []\n          )\n        )\n        .then(() => {\n          requestIdleCallback(() => this.loadRoute(route, true).catch(() => {}))\n        })\n        .catch(\n          // swallow prefetch errors\n          () => {}\n        )\n    },\n  }\n}\n"], "names": ["createRouteLoader", "getClientBuildManifest", "isAssetError", "<PERSON><PERSON><PERSON><PERSON>", "MS_MAX_IDLE_DELAY", "withFuture", "key", "map", "generator", "entry", "get", "future", "Promise", "resolve", "resolver", "prom", "set", "then", "value", "catch", "err", "delete", "ASSET_LOAD_ERROR", "Symbol", "Object", "defineProperty", "hasPrefetch", "link", "document", "createElement", "window", "MSInputMethodContext", "documentMode", "relList", "supports", "canPrefetch", "getAssetQueryString", "getDeploymentIdQueryOrEmptyString", "prefetchViaDom", "href", "as", "reject", "selector", "querySelector", "rel", "crossOrigin", "process", "env", "__NEXT_CROSS_ORIGIN", "onload", "onerror", "Error", "head", "append<PERSON><PERSON><PERSON>", "appendScript", "src", "script", "body", "devBuildPromise", "resolvePromiseWithTimeout", "p", "ms", "cancelled", "r", "NODE_ENV", "requestIdleCallback", "setTimeout", "self", "__BUILD_MANIFEST", "onBuildManifest", "cb", "__BUILD_MANIFEST_CB", "getFilesForRoute", "assetPrefix", "route", "scriptUrl", "encodeURIPath", "getAssetPathFromRoute", "scripts", "__unsafeCreateTrustedScriptURL", "css", "manifest", "allFiles", "filter", "v", "endsWith", "entrypoints", "Map", "loadedScripts", "styleSheets", "routes", "maybeExecuteScript", "toString", "fetchStyleSheet", "fetch", "credentials", "res", "ok", "text", "content", "whenEntrypoint", "onEntrypoint", "execute", "exports", "component", "default", "error", "undefined", "input", "old", "loadRoute", "prefetch", "devBuildPromiseResolve", "all", "has", "entrypoint", "styles", "assign", "finally", "cn", "navigator", "connection", "saveData", "test", "effectiveType", "output"], "mappings": "AA8IwB8C,QAAQC,GAAG,CAACC,mBAAmB;;;;;;;;;;;;;;;;;;IAmJvChD,iBAAiB,EAAA;eAAjBA;;IA3DAC,sBAAsB,EAAA;eAAtBA;;IAnIAC,YAAY,EAAA;eAAZA;;IAJAC,cAAc,EAAA;eAAdA;;;;gFA7FkB;8BACa;qCACX;8BACc;+BACpB;AAE9B,uEAAuE;AACvE,yEAAyE;AACzE,2EAA2E;AAC3E,oCAAoC;AACpC,MAAMC,oBAAoB;AA4C1B,SAASC,WACPC,GAAW,EACXC,GAA+B,EAC/BC,SAA4B;IAE5B,IAAIC,QAAQF,IAAIG,GAAG,CAACJ;IACpB,IAAIG,OAAO;QACT,IAAI,YAAYA,OAAO;YACrB,OAAOA,MAAME,MAAM;QACrB;QACA,OAAOC,QAAQC,OAAO,CAACJ;IACzB;IACA,IAAIK;IACJ,MAAMC,OAAmB,IAAIH,QAAW,CAACC;QACvCC,WAAWD;IACb;IACAN,IAAIS,GAAG,CAACV,KAAK;QAAEO,SAASC;QAAWH,QAAQI;IAAK;IAChD,OAAOP,YACHA,YACGS,IAAI,CAAC,CAACC;QACLJ,SAASI;QACT,OAAOA;IACT,GACCC,KAAK,CAAC,CAACC;QACNb,IAAIc,MAAM,CAACf;QACX,MAAMc;IACR,KACFL;AACN;AASA,MAAMO,mBAAmBC,OAAO;AAEzB,SAASpB,eAAeiB,GAAU;IACvC,OAAOI,OAAOC,cAAc,CAACL,KAAKE,kBAAkB,CAAC;AACvD;AAEO,SAASpB,aAAakB,GAAW;IACtC,OAAOA,OAAOE,oBAAoBF;AACpC;AAEA,SAASM,YAAYC,IAAsB;IACzC,IAAI;QACFA,OAAOC,SAASC,aAAa,CAAC;QAC9B,OAGE,AAFA,AACA,uBAAuB,qCADqC;QAE3D,CAAC,CAACC,OAAOC,oBAAoB,IAAI,CAAC,CAAEH,SAAiBI,YAAY,IAClEL,KAAKM,OAAO,CAACC,QAAQ,CAAC;IAE1B,EAAE,OAAA,GAAM;QACN,OAAO;IACT;AACF;AAEA,MAAMC,cAAuBT;AAE7B,MAAMU,sBAAsB;IAC1B,OAAOC,CAAAA,GAAAA,cAAAA,iCAAiC;AAC1C;AAEA,SAASC,eACPC,IAAY,EACZC,EAAU,EACVb,IAAsB;IAEtB,OAAO,IAAIf,QAAc,CAACC,SAAS4B;QACjC,MAAMC,WAAY,yCACcH,OAAK,2CACNA,OAAK,6BACnBA,OAAK;QACtB,IAAIX,SAASe,aAAa,CAACD,WAAW;YACpC,OAAO7B;QACT;QAEAc,OAAOC,SAASC,aAAa,CAAC;QAE9B,wDAAwD;QACxD,IAAIW,IAAIb,KAAMa,EAAE,GAAGA;QACnBb,KAAMiB,GAAG,GAAI;QACbjB,KAAMkB,WAAW;QACjBlB,KAAMsB,MAAM,GAAGpC;QACfc,KAAMuB,OAAO,GAAG,IACdT,OAAOtC,eAAe,OAAA,cAAwC,CAAxC,IAAIgD,MAAO,yBAAsBZ,OAAjC,qBAAA;uBAAA;4BAAA;8BAAA;YAAuC;QAE/D,gCAAgC;QAChCZ,KAAMY,IAAI,GAAGA;QAEbX,SAASwB,IAAI,CAACC,WAAW,CAAC1B;IAC5B;AACF;AAEA,SAAS2B,aACPC,GAA8B,EAC9BC,MAA0B;IAE1B,OAAO,IAAI5C,QAAQ,CAACC,SAAS4B;QAC3Be,SAAS5B,SAASC,aAAa,CAAC;QAEhC,wDAAwD;QACxD,mEAAmE;QACnE,iCAAiC;QACjC2B,OAAOP,MAAM,GAAGpC;QAChB2C,OAAON,OAAO,GAAG,IACfT,OAAOtC,eAAe,OAAA,cAA0C,CAA1C,IAAIgD,MAAO,4BAAyBI,MAApC,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QAEjE,2EAA2E;QAC3E,8BAA8B;QAC9BC,OAAOX,WAAW,GAAGC,QAAQC,GAAG,CAACC,mBAAmB;QAEpD,uEAAuE;QACvE,6CAA6C;QAC7CQ,OAAOD,GAAG,GAAGA;QACb3B,SAAS6B,IAAI,CAACJ,WAAW,CAACG;IAC5B;AACF;AAEA,4EAA4E;AAC5E,qEAAqE;AACrE,IAAIE;AAEJ,uEAAuE;AACvE,SAASC,0BACPC,CAAa,EACbC,EAAU,EACVzC,GAAU;IAEV,OAAO,IAAIR,QAAQ,CAACC,SAAS4B;QAC3B,IAAIqB,YAAY;QAEhBF,EAAE3C,IAAI,CAAC,CAAC8C;YACN,+BAA+B;YAC/BD,YAAY;YACZjD,QAAQkD;QACV,GAAG5C,KAAK,CAACsB;QAET,sEAAsE;QACtE,sBAAsB;QACtB,IAAIK,QAAQC,GAAG,CAACiB,QAAQ,KAAK,WAAe;;YACxCN,CAAAA,mBAAmB9C,QAAQC,OAAO,EAAC,EAAGI,IAAI,CAAC;gBAC3CgD,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAClBC,WAAW;wBACT,IAAI,CAACJ,WAAW;4BACdrB,OAAOrB;wBACT;oBACF,GAAGyC;YAEP;QACF;QAEA,IAAIf,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;;IAS9C;AACF;AAQO,SAAS/D;IACd,IAAIkE,KAAKC,gBAAgB,EAAE;QACzB,OAAOxD,QAAQC,OAAO,CAACsD,KAAKC,gBAAgB;IAC9C;IAEA,MAAMC,kBAAkB,IAAIzD,QAAkC,CAACC;QAC7D,iDAAiD;QACjD,MAAMyD,KAAKH,KAAKI,mBAAmB;QACnCJ,KAAKI,mBAAmB,GAAG;YACzB1D,QAAQsD,KAAKC,gBAAgB;YAC7BE,MAAMA;QACR;IACF;IAEA,OAAOX,0BACLU,iBACAjE,mBACAD,eAAe,OAAA,cAAiD,CAAjD,IAAIgD,MAAM,yCAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgD;AAEnE;AAMA,SAASqB,iBACPC,WAAmB,EACnBC,KAAa;IAEb,IAAI5B,QAAQC,GAAG,CAACiB,QAAQ,KAAK,WAAe;QAC1C,MAAMW,YACJF,cACA,+BACAG,CAAAA,GAAAA,eAAAA,aAAa,EAACC,CAAAA,GAAAA,uBAAAA,OAAqB,EAACH,OAAO,UAC3CtC;QACF,OAAOxB,QAAQC,OAAO,CAAC;YACrBiE,SAAS;gBAACC,CAAAA,GAAAA,cAAAA,8BAA8B,EAACJ;aAAW;YACpD,uDAAuD;YACvDK,KAAK,EAAE;QACT;IACF;;;AAiBF;AAEO,SAAShF,kBAAkByE,WAAmB;IACnD,MAAMa,cACJ,IAAIC;IACN,MAAMC,gBAA+C,IAAID;IACzD,MAAME,cAAqD,IAAIF;IAC/D,MAAMG,SACJ,IAAIH;IAEN,SAASI,mBACPpC,GAA8B;QAE9B,2DAA2D;QAC3D,kEAAkE;QAClE,cAAc;QACd,IAAIT,QAAQC,GAAG,CAACiB,QAAQ,KAAK,eAAe;;aAarC;YACL,OAAOV,aAAaC;QACtB;IACF;IAEA,SAASsC,gBAAgBtD,IAAY;QACnC,IAAIxB,OAA6C0E,YAAY/E,GAAG,CAAC6B;QACjE,IAAIxB,MAAM;YACR,OAAOA;QACT;QAEA0E,YAAYzE,GAAG,CACbuB,MACCxB,OAAO+E,MAAMvD,MAAM;YAAEwD,aAAa;QAAc,GAC9C9E,IAAI,CAAC,CAAC+E;YACL,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAM,OAAA,cAA+C,CAA/C,IAAI9C,MAAO,gCAA6BZ,OAAxC,qBAAA;2BAAA;gCAAA;kCAAA;gBAA8C;YACtD;YACA,OAAOyD,IAAIE,IAAI,GAAGjF,IAAI,CAAC,CAACiF,OAAU,CAAA;oBAAE3D,MAAMA;oBAAM4D,SAASD;gBAAK,CAAA;QAChE,GACC/E,KAAK,CAAC,CAACC;YACN,MAAMjB,eAAeiB;QACvB;QAEJ,OAAOL;IACT;IAEA,OAAO;QACLqF,gBAAe1B,KAAa;YAC1B,OAAOrE,WAAWqE,OAAOY;QAC3B;QACAe,cAAa3B,KAAa,EAAE4B,OAAoC;;YAC5DA,CAAAA,UACE1F,QAAQC,OAAO,GACZI,IAAI,CAAC,IAAMqF,WACXrF,IAAI,CACH,CAACsF,WAAkB,CAAA;oBACjBC,WAAYD,YAAWA,SAAQE,OAAO,IAAKF;oBAC3CA,SAASA;gBACX,CAAA,GACA,CAACnF,MAAS,CAAA;oBAAEsF,OAAOtF;gBAAI,CAAA,KAE3BR,QAAQC,OAAO,CAAC8F,UAAS,EAC3B1F,IAAI,CAAC,CAAC2F;gBACN,MAAMC,MAAMvB,YAAY5E,GAAG,CAACgE;gBAC5B,IAAImC,OAAO,aAAaA,KAAK;oBAC3B,IAAID,OAAO;wBACTtB,YAAYtE,GAAG,CAAC0D,OAAOkC;wBACvBC,IAAIhG,OAAO,CAAC+F;oBACd;gBACF,OAAO;oBACL,IAAIA,OAAO;wBACTtB,YAAYtE,GAAG,CAAC0D,OAAOkC;oBACzB,OAAO;wBACLtB,YAAYjE,MAAM,CAACqD;oBACrB;oBACA,gDAAgD;oBAChD,kDAAkD;oBAClD,mBAAmB;oBACnBgB,OAAOrE,MAAM,CAACqD;gBAChB;YACF;QACF;QACAoC,WAAUpC,KAAa,EAAEqC,QAAkB;YACzC,OAAO1G,WAA6BqE,OAAOgB,QAAQ;gBACjD,IAAIsB;gBAEJ,IAAIlE,QAAQC,GAAG,CAACiB,QAAQ,KAAK,WAAe;oBAC1CN,kBAAkB,IAAI9C,QAAc,CAACC;wBACnCmG,yBAAyBnG;oBAC3B;gBACF;gBAEA,OAAO8C,0BACLa,iBAAiBC,aAAaC,OAC3BzD,IAAI,CAAC,CAAA;wBAAC,EAAE6D,OAAO,EAAEE,GAAG,EAAE,GAAA;oBACrB,OAAOpE,QAAQqG,GAAG,CAAC;wBACjB3B,YAAY4B,GAAG,CAACxC,SACZ,EAAE,GACF9D,QAAQqG,GAAG,CAACnC,QAAQvE,GAAG,CAACoF;wBAC5B/E,QAAQqG,GAAG,CAACjC,IAAIzE,GAAG,CAACsF;qBACrB;gBACH,GACC5E,IAAI,CAAC,CAAC+E;oBACL,OAAO,IAAI,CAACI,cAAc,CAAC1B,OAAOzD,IAAI,CAAC,CAACkG,aAAgB,CAAA;4BACtDA;4BACAC,QAAQpB,GAAG,CAAC,EAAE;wBAChB,CAAA;gBACF,IACF5F,mBACAD,eAAe,OAAA,cAAqD,CAArD,IAAIgD,MAAO,qCAAkCuB,QAA7C,qBAAA;2BAAA;gCAAA;kCAAA;gBAAoD,KAElEzD,IAAI,CAAC,CAAA;wBAAC,EAAEkG,UAAU,EAAEC,MAAM,EAAE,GAAA;oBAC3B,MAAMpB,MAAwBxE,OAAO6F,MAAM,CAGzC;wBAAED,QAAQA;oBAAQ,GAAGD;oBACvB,OAAO,WAAWA,aAAaA,aAAanB;gBAC9C,GACC7E,KAAK,CAAC,CAACC;oBACN,IAAI2F,UAAU;wBACZ,gDAAgD;wBAChD,MAAM3F;oBACR;oBACA,OAAO;wBAAEsF,OAAOtF;oBAAI;gBACtB,GACCkG,OAAO,CAAC,IAAMN,0BAAAA,OAAAA,KAAAA,IAAAA;YACnB;QACF;QACAD,UAASrC,KAAa;YACpB,sHAAsH;YACtH,sBAAsB;YACtB,IAAI6C;YACJ,IAAKA,KAAMC,UAAkBC,UAAU,EAAG;gBACxC,yDAAyD;gBACzD,IAAIF,GAAGG,QAAQ,IAAI,KAAKC,IAAI,CAACJ,GAAGK,aAAa,GAAG,OAAOhH,QAAQC,OAAO;YACxE;YACA,OAAO2D,iBAAiBC,aAAaC,OAClCzD,IAAI,CAAC,CAAC4G,SACLjH,QAAQqG,GAAG,CACT9E,cACI0F,OAAO/C,OAAO,CAACvE,GAAG,CAAC,CAACiD,SAClBlB,eAAekB,OAAOoC,QAAQ,IAAI,aAEpC,EAAE,GAGT3E,IAAI,CAAC;gBACJgD,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAM,IAAI,CAAC6C,SAAS,CAACpC,OAAO,MAAMvD,KAAK,CAAC,KAAO;YACrE,GACCA,KAAK,CACJ,AACA,KAAO,qBADmB;QAGhC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 778, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/page-loader.ts"], "sourcesContent": ["import type { ComponentType } from 'react'\nimport type { RouteLoader } from './route-loader'\nimport type { MiddlewareMatcher } from '../build/analysis/get-page-static-info'\nimport { addBasePath } from './add-base-path'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { addLocale } from './add-locale'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { parseRelativeUrl } from '../shared/lib/router/utils/parse-relative-url'\nimport { removeTrailingSlash } from '../shared/lib/router/utils/remove-trailing-slash'\nimport { createRouteLoader, getClientBuildManifest } from './route-loader'\nimport {\n  DEV_CLIENT_PAGES_MANIFEST,\n  DEV_CLIENT_MIDDLEWARE_MANIFEST,\n  TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST,\n} from '../shared/lib/constants'\n\ndeclare global {\n  interface Window {\n    __DEV_MIDDLEWARE_MATCHERS?: MiddlewareMatcher[]\n    __DEV_PAGES_MANIFEST?: { pages: string[] }\n    __SSG_MANIFEST_CB?: () => void\n    __SSG_MANIFEST?: Set<string>\n  }\n}\n\nexport type StyleSheetTuple = { href: string; text: string }\nexport type GoodPageCache = {\n  page: ComponentType\n  mod: any\n  styleSheets: StyleSheetTuple[]\n}\n\nexport default class PageLoader {\n  private buildId: string\n  private assetPrefix: string\n  private promisedSsgManifest: Promise<Set<string>>\n  private promisedDevPagesManifest?: Promise<string[]>\n  private promisedMiddlewareMatchers?: Promise<MiddlewareMatcher[]>\n\n  public routeLoader: RouteLoader\n\n  constructor(buildId: string, assetPrefix: string) {\n    this.routeLoader = createRouteLoader(assetPrefix)\n\n    this.buildId = buildId\n    this.assetPrefix = assetPrefix\n\n    this.promisedSsgManifest = new Promise((resolve) => {\n      if (window.__SSG_MANIFEST) {\n        resolve(window.__SSG_MANIFEST)\n      } else {\n        window.__SSG_MANIFEST_CB = () => {\n          resolve(window.__SSG_MANIFEST!)\n        }\n      }\n    })\n  }\n\n  getPageList() {\n    if (process.env.NODE_ENV === 'production') {\n      return getClientBuildManifest().then((manifest) => manifest.sortedPages)\n    } else {\n      if (window.__DEV_PAGES_MANIFEST) {\n        return window.__DEV_PAGES_MANIFEST.pages\n      } else {\n        this.promisedDevPagesManifest ||= fetch(\n          `${this.assetPrefix}/_next/static/development/${DEV_CLIENT_PAGES_MANIFEST}`,\n          { credentials: 'same-origin' }\n        )\n          .then((res) => res.json())\n          .then((manifest: { pages: string[] }) => {\n            window.__DEV_PAGES_MANIFEST = manifest\n            return manifest.pages\n          })\n          .catch((err) => {\n            console.log(`Failed to fetch devPagesManifest:`, err)\n            throw new Error(\n              `Failed to fetch _devPagesManifest.json. Is something blocking that network request?\\n` +\n                'Read more: https://nextjs.org/docs/messages/failed-to-fetch-devpagesmanifest'\n            )\n          })\n        return this.promisedDevPagesManifest\n      }\n    }\n  }\n\n  getMiddleware() {\n    // Webpack production\n    if (\n      process.env.NODE_ENV === 'production' &&\n      process.env.__NEXT_MIDDLEWARE_MATCHERS\n    ) {\n      const middlewareMatchers = process.env.__NEXT_MIDDLEWARE_MATCHERS\n      window.__MIDDLEWARE_MATCHERS = middlewareMatchers\n        ? (middlewareMatchers as any as MiddlewareMatcher[])\n        : undefined\n      return window.__MIDDLEWARE_MATCHERS\n      // Turbopack production\n    } else if (process.env.NODE_ENV === 'production') {\n      if (window.__MIDDLEWARE_MATCHERS) {\n        return window.__MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n      // Development both Turbopack and Webpack\n    } else {\n      if (window.__DEV_MIDDLEWARE_MATCHERS) {\n        return window.__DEV_MIDDLEWARE_MATCHERS\n      } else {\n        if (!this.promisedMiddlewareMatchers) {\n          // TODO: Decide what should happen when fetching fails instead of asserting\n          // @ts-ignore\n          this.promisedMiddlewareMatchers = fetch(\n            `${this.assetPrefix}/_next/static/${this.buildId}/${DEV_CLIENT_MIDDLEWARE_MANIFEST}`,\n            { credentials: 'same-origin' }\n          )\n            .then((res) => res.json())\n            .then((matchers: MiddlewareMatcher[]) => {\n              window.__DEV_MIDDLEWARE_MATCHERS = matchers\n              return matchers\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch _devMiddlewareManifest`, err)\n            })\n        }\n        // TODO Remove this assertion as this could be undefined\n        return this.promisedMiddlewareMatchers!\n      }\n    }\n  }\n\n  getDataHref(params: {\n    asPath: string\n    href: string\n    locale?: string | false\n    skipInterpolation?: boolean\n  }): string {\n    const { asPath, href, locale } = params\n    const { pathname: hrefPathname, query, search } = parseRelativeUrl(href)\n    const { pathname: asPathname } = parseRelativeUrl(asPath)\n    const route = removeTrailingSlash(hrefPathname)\n    if (route[0] !== '/') {\n      throw new Error(`Route name should start with a \"/\", got \"${route}\"`)\n    }\n\n    const getHrefForSlug = (path: string) => {\n      const dataRoute = getAssetPathFromRoute(\n        removeTrailingSlash(addLocale(path, locale)),\n        '.json'\n      )\n      return addBasePath(\n        `/_next/data/${this.buildId}${dataRoute}${search}`,\n        true\n      )\n    }\n\n    return getHrefForSlug(\n      params.skipInterpolation\n        ? asPathname\n        : isDynamicRoute(route)\n          ? interpolateAs(hrefPathname, asPathname, query).result\n          : route\n    )\n  }\n\n  _isSsg(\n    /** the route (file-system path) */\n    route: string\n  ): Promise<boolean> {\n    return this.promisedSsgManifest.then((manifest) => manifest.has(route))\n  }\n\n  loadPage(route: string): Promise<GoodPageCache> {\n    return this.routeLoader.loadRoute(route).then((res) => {\n      if ('component' in res) {\n        return {\n          page: res.component,\n          mod: res.exports,\n          styleSheets: res.styles.map((o) => ({\n            href: o.href,\n            text: o.content,\n          })),\n        }\n      }\n      throw res.error\n    })\n  }\n\n  prefetch(route: string): Promise<void> {\n    return this.routeLoader.prefetch(route)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON>", "getPageList", "process", "env", "NODE_ENV", "getClientBuildManifest", "then", "manifest", "sortedPages", "window", "__DEV_PAGES_MANIFEST", "pages", "promisedDevPagesManifest", "fetch", "assetPrefix", "DEV_CLIENT_PAGES_MANIFEST", "credentials", "res", "json", "catch", "err", "console", "log", "Error", "getMiddleware", "__NEXT_MIDDLEWARE_MATCHERS", "middlewareMatchers", "__MIDDLEWARE_MATCHERS", "undefined", "promisedMiddlewareMatchers", "buildId", "TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST", "matchers", "__DEV_MIDDLEWARE_MATCHERS", "DEV_CLIENT_MIDDLEWARE_MANIFEST", "getDataHref", "params", "<PERSON><PERSON><PERSON>", "href", "locale", "pathname", "hrefPathname", "query", "search", "parseRelativeUrl", "asPathname", "route", "removeTrailingSlash", "getHrefForSlug", "path", "dataRoute", "getAssetPathFromRoute", "addLocale", "addBasePath", "skipInterpolation", "isDynamicRoute", "interpolateAs", "result", "_isSsg", "promisedSsgManifest", "has", "loadPage", "routeLoader", "loadRoute", "page", "component", "mod", "exports", "styleSheets", "styles", "map", "o", "text", "content", "error", "prefetch", "constructor", "createRouteLoader", "Promise", "resolve", "__SSG_MANIFEST", "__SSG_MANIFEST_CB"], "mappings": "AA4DQE,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;;;;;;;eA3B1BJ;;;;6BA9BO;+BACE;gFACI;2BACR;2BACK;kCACE;qCACG;6BACsB;2BAKnD;AAkBQ,MAAMA;IA0BnBC,cAAc;QACZ;;aAEO;YACL,IAAIQ,OAAOC,oBAAoB,EAAE;gBAC/B,OAAOD,OAAOC,oBAAoB,CAACC,KAAK;YAC1C,OAAO;gBACL,IAAI,CAACC,wBAAAA,IAAAA,CAAL,IAAI,CAACA,wBAAAA,GAA6BC,MAC7B,IAAI,CAACC,WAAW,GAAC,+BAA4BC,WAAAA,yBAAyB,EACzE;oBAAEC,aAAa;gBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAACC;oBACLE,OAAOC,oBAAoB,GAAGH;oBAC9B,OAAOA,SAASI,KAAK;gBACvB,GACCQ,KAAK,CAAC,CAACC;oBACNC,QAAQC,GAAG,CAAE,qCAAoCF;oBACjD,MAAM,OAAA,cAGL,CAHK,IAAIG,MACP,0FACC,iFAFE,qBAAA;+BAAA;oCAAA;sCAAA;oBAGN;gBACF,EAAA;gBACF,OAAO,IAAI,CAACX,wBAAwB;YACtC;QACF;IACF;IAEAY,gBAAgB;QACd,qBAAqB;QACrB,IACEtB,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACsB,0BAA0B,EACtC;;aAOK,IAAIvB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;aAwB3C;YACL,IAAIK,OAAOwB,yBAAyB,EAAE;gBACpC,OAAOxB,OAAOwB,yBAAyB;YACzC,OAAO;gBACL,IAAI,CAAC,IAAI,CAACJ,0BAA0B,EAAE;oBACpC,2EAA2E;oBAC3E,aAAa;oBACb,IAAI,CAACA,0BAA0B,GAAGhB,MAC7B,IAAI,CAACC,WAAW,GAAC,mBAAgB,IAAI,CAACgB,OAAO,GAAC,MAAGI,WAAAA,8BAA8B,EAClF;wBAAElB,aAAa;oBAAc,GAE5BV,IAAI,CAAC,CAACW,MAAQA,IAAIC,IAAI,IACtBZ,IAAI,CAAC,CAAC0B;wBACLvB,OAAOwB,yBAAyB,GAAGD;wBACnC,OAAOA;oBACT,GACCb,KAAK,CAAC,CAACC;wBACNC,QAAQC,GAAG,CAAE,0CAAyCF;oBACxD;gBACJ;gBACA,wDAAwD;gBACxD,OAAO,IAAI,CAACS,0BAA0B;YACxC;QACF;IACF;IAEAM,YAAYC,MAKX,EAAU;QACT,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE,GAAGH;QACjC,MAAM,EAAEI,UAAUC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACN;QACnE,MAAM,EAAEE,UAAUK,UAAU,EAAE,GAAGD,CAAAA,GAAAA,kBAAAA,gBAAgB,EAACP;QAClD,MAAMS,QAAQC,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACN;QAClC,IAAIK,KAAK,CAAC,EAAE,KAAK,KAAK;YACpB,MAAM,OAAA,cAA+D,CAA/D,IAAIvB,MAAO,8CAA2CuB,QAAM,MAA5D,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;QAEA,MAAME,iBAAiB,CAACC;YACtB,MAAMC,YAAYC,CAAAA,GAAAA,uBAAAA,OAAqB,EACrCJ,CAAAA,GAAAA,qBAAAA,mBAAmB,EAACK,CAAAA,GAAAA,WAAAA,SAAS,EAACH,MAAMV,UACpC;YAEF,OAAOc,CAAAA,GAAAA,aAAAA,WAAW,EACf,iBAAc,IAAI,CAACvB,OAAO,GAAGoB,YAAYP,QAC1C;QAEJ;QAEA,OAAOK,eACLZ,OAAOkB,iBAAiB,GACpBT,aACAU,CAAAA,GAAAA,WAAAA,cAAc,EAACT,SACbU,CAAAA,GAAAA,eAAAA,aAAa,EAACf,cAAcI,YAAYH,OAAOe,MAAM,GACrDX;IAEV;IAEAY,OACE,iCAAiC,GACjCZ,KAAa,EACK;QAClB,OAAO,IAAI,CAACa,mBAAmB,CAACrD,IAAI,CAAC,CAACC,WAAaA,SAASqD,GAAG,CAACd;IAClE;IAEAe,SAASf,KAAa,EAA0B;QAC9C,OAAO,IAAI,CAACgB,WAAW,CAACC,SAAS,CAACjB,OAAOxC,IAAI,CAAC,CAACW;YAC7C,IAAI,eAAeA,KAAK;gBACtB,OAAO;oBACL+C,MAAM/C,IAAIgD,SAAS;oBACnBC,KAAKjD,IAAIkD,OAAO;oBAChBC,aAAanD,IAAIoD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAO,CAAA;4BAClCjC,MAAMiC,EAAEjC,IAAI;4BACZkC,MAAMD,EAAEE,OAAO;wBACjB,CAAA;gBACF;YACF;YACA,MAAMxD,IAAIyD,KAAK;QACjB;IACF;IAEAC,SAAS7B,KAAa,EAAiB;QACrC,OAAO,IAAI,CAACgB,WAAW,CAACa,QAAQ,CAAC7B;IACnC;IAtKA8B,YAAY9C,OAAe,EAAEhB,WAAmB,CAAE;QAChD,IAAI,CAACgD,WAAW,GAAGe,CAAAA,GAAAA,aAAAA,iBAAiB,EAAC/D;QAErC,IAAI,CAACgB,OAAO,GAAGA;QACf,IAAI,CAAChB,WAAW,GAAGA;QAEnB,IAAI,CAAC6C,mBAAmB,GAAG,IAAImB,QAAQ,CAACC;YACtC,IAAItE,OAAOuE,cAAc,EAAE;gBACzBD,QAAQtE,OAAOuE,cAAc;YAC/B,OAAO;gBACLvE,OAAOwE,iBAAiB,GAAG;oBACzBF,QAAQtE,OAAOuE,cAAc;gBAC/B;YACF;QACF;IACF;AAwJF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/script.tsx"], "sourcesContent": ["'use client'\n\nimport ReactDOM from 'react-dom'\nimport React, { useEffect, useContext, useRef, type JSX } from 'react'\nimport type { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { setAttributesFromProps } from './set-attributes-from-props'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive' | 'worker'\n  id?: string\n  onLoad?: (e: any) => void\n  onReady?: () => void | null\n  onError?: (e: any) => void\n  children?: React.ReactNode\n  stylesheets?: string[]\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst insertStylesheets = (stylesheets: string[]) => {\n  // Case 1: Styles for afterInteractive/lazyOnload with appDir injected via handleClientScriptLoad\n  //\n  // Using ReactDOM.preinit to feature detect appDir and inject styles\n  // Stylesheets might have already been loaded if initialized with Script component\n  // Re-inject styles here to handle scripts loaded via handleClientScriptLoad\n  // ReactDOM.preinit handles dedup and ensures the styles are loaded only once\n  if (ReactDOM.preinit) {\n    stylesheets.forEach((stylesheet: string) => {\n      ReactDOM.preinit(stylesheet, { as: 'style' })\n    })\n\n    return\n  }\n\n  // Case 2: Styles for afterInteractive/lazyOnload with pages injected via handleClientScriptLoad\n  //\n  // We use this function to load styles when appdir is not detected\n  // TODO: Use React float APIs to load styles once available for pages dir\n  if (typeof window !== 'undefined') {\n    let head = document.head\n    stylesheets.forEach((stylesheet: string) => {\n      let link = document.createElement('link')\n\n      link.type = 'text/css'\n      link.rel = 'stylesheet'\n      link.href = stylesheet\n\n      head.appendChild(link)\n    })\n  }\n}\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    onReady = null,\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n    // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  /** Execute after the script first loaded */\n  const afterLoad = () => {\n    // Run onReady for the first time after load event\n    if (onReady) {\n      onReady()\n    }\n    // add cacheKey to LoadCache when load successfully\n    LoadCache.add(cacheKey)\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n      afterLoad()\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (dangerouslySetInnerHTML) {\n    // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n    el.innerHTML = (dangerouslySetInnerHTML.__html as string) || ''\n\n    afterLoad()\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n          ? children.join('')\n          : ''\n\n    afterLoad()\n  } else if (src) {\n    el.src = src\n    // do not add cacheKey into LoadCache for remote script here\n    // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n\n    ScriptCache.set(src, loadPromise)\n  }\n\n  setAttributesFromProps(el, props)\n\n  if (strategy === 'worker') {\n    el.setAttribute('type', 'text/partytown')\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  // Load styles associated with this script\n  if (stylesheets) {\n    insertStylesheets(stylesheets)\n  }\n\n  document.body.appendChild(el)\n}\n\nexport function handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  } else {\n    loadScript(props)\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction addBeforeInteractiveToCache() {\n  const scripts = [\n    ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n    ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]'),\n  ]\n  scripts.forEach((script) => {\n    const cacheKey = script.id || script.getAttribute('src')\n    LoadCache.add(cacheKey)\n  })\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n  addBeforeInteractiveToCache()\n}\n\n/**\n * Load a third-party scripts in an optimized way.\n *\n * Read more: [Next.js Docs: `next/script`](https://nextjs.org/docs/app/api-reference/components/script)\n */\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    id,\n    src = '',\n    onLoad = () => {},\n    onReady = null,\n    strategy = 'afterInteractive',\n    onError,\n    stylesheets,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  let { updateScripts, scripts, getIsSsr, appDir, nonce } =\n    useContext(HeadManagerContext)\n\n  // if a nonce is explicitly passed to the script tag, favor that over the automatic handling\n  nonce = restProps.nonce || nonce\n\n  /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */\n  const hasOnReadyEffectCalled = useRef(false)\n\n  useEffect(() => {\n    const cacheKey = id || src\n    if (!hasOnReadyEffectCalled.current) {\n      // Run onReady if script has loaded before but component is re-mounted\n      if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n        onReady()\n      }\n\n      hasOnReadyEffectCalled.current = true\n    }\n  }, [onReady, id, src])\n\n  const hasLoadScriptEffectCalled = useRef(false)\n\n  useEffect(() => {\n    if (!hasLoadScriptEffectCalled.current) {\n      if (strategy === 'afterInteractive') {\n        loadScript(props)\n      } else if (strategy === 'lazyOnload') {\n        loadLazyScript(props)\n      }\n\n      hasLoadScriptEffectCalled.current = true\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive' || strategy === 'worker') {\n    if (updateScripts) {\n      scripts[strategy] = (scripts[strategy] || []).concat([\n        {\n          id,\n          src,\n          onLoad,\n          onReady,\n          onError,\n          ...restProps,\n          nonce,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript({\n        ...props,\n        nonce,\n      })\n    }\n  }\n\n  // For the app directory, we need React Float to preload these scripts.\n  if (appDir) {\n    // Injecting stylesheets here handles beforeInteractive and worker scripts correctly\n    // For other strategies injecting here ensures correct stylesheet order\n    // ReactDOM.preinit handles loading the styles in the correct order,\n    // also ensures the stylesheet is loaded only once and in a consistent manner\n    //\n    // Case 1: Styles for beforeInteractive/worker with appDir - handled here\n    // Case 2: Styles for beforeInteractive/worker with pages dir - Not handled yet\n    // Case 3: Styles for afterInteractive/lazyOnload with appDir - handled here\n    // Case 4: Styles for afterInteractive/lazyOnload with pages dir - handled in insertStylesheets function\n    if (stylesheets) {\n      stylesheets.forEach((styleSrc) => {\n        ReactDOM.preinit(styleSrc, { as: 'style' })\n      })\n    }\n\n    // Before interactive scripts need to be loaded by Next.js' runtime instead\n    // of native <script> tags, because they no longer have `defer`.\n    if (strategy === 'beforeInteractive') {\n      if (!src) {\n        // For inlined scripts, we put the content in `children`.\n        if (restProps.dangerouslySetInnerHTML) {\n          // Casting since lib.dom.d.ts doesn't have TrustedHTML yet.\n          restProps.children = restProps.dangerouslySetInnerHTML\n            .__html as string\n          delete restProps.dangerouslySetInnerHTML\n        }\n\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                0,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      } else {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n        return (\n          <script\n            nonce={nonce}\n            dangerouslySetInnerHTML={{\n              __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                src,\n                { ...restProps, id },\n              ])})`,\n            }}\n          />\n        )\n      }\n    } else if (strategy === 'afterInteractive') {\n      if (src) {\n        // @ts-ignore\n        ReactDOM.preload(\n          src,\n          restProps.integrity\n            ? {\n                as: 'script',\n                integrity: restProps.integrity,\n                nonce,\n                crossOrigin: restProps.crossOrigin,\n              }\n            : { as: 'script', nonce, crossOrigin: restProps.crossOrigin }\n        )\n      }\n    }\n  }\n\n  return null\n}\n\nObject.defineProperty(Script, '__nextScript', { value: true })\n\nexport default Script\n"], "names": ["handleClientScriptLoad", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Map", "Load<PERSON>ache", "Set", "insertStylesheets", "stylesheets", "ReactDOM", "preinit", "for<PERSON>ach", "stylesheet", "as", "window", "head", "document", "link", "createElement", "type", "rel", "href", "append<PERSON><PERSON><PERSON>", "loadScript", "props", "src", "id", "onLoad", "onReady", "dangerouslySetInnerHTML", "children", "strategy", "onError", "cache<PERSON>ey", "has", "add", "get", "then", "afterLoad", "el", "loadPromise", "Promise", "resolve", "reject", "addEventListener", "e", "call", "catch", "innerHTML", "__html", "textContent", "Array", "isArray", "join", "set", "setAttributesFromProps", "setAttribute", "body", "requestIdleCallback", "loadLazyScript", "readyState", "addBeforeInteractiveToCache", "scripts", "querySelectorAll", "script", "getAttribute", "scriptLoaderItems", "<PERSON><PERSON><PERSON>", "restProps", "updateScripts", "getIsSsr", "appDir", "nonce", "useContext", "HeadManagerContext", "hasOnReadyEffectCalled", "useRef", "useEffect", "current", "hasLoadScriptEffectCalled", "concat", "styleSrc", "JSON", "stringify", "preload", "integrity", "crossOrigin", "Object", "defineProperty", "value"], "mappings": ";;;;;;;;;;;;;;;IAgYA,OAAqB,EAAA;eAArB;;IApOgBA,sBAAsB,EAAA;eAAtBA;;IAgCAC,gBAAgB,EAAA;eAAhBA;;;;;;mEA1LK;iEAC0C;iDAE5B;wCACI;qCACH;AAEpC,MAAMC,cAAc,IAAIC;AACxB,MAAMC,YAAY,IAAIC;AAiBtB,MAAMC,oBAAoB,CAACC;IACzB,iGAAiG;IACjG,EAAE;IACF,oEAAoE;IACpE,kFAAkF;IAClF,4EAA4E;IAC5E,6EAA6E;IAC7E,IAAIC,UAAAA,OAAQ,CAACC,OAAO,EAAE;QACpBF,YAAYG,OAAO,CAAC,CAACC;YACnBH,UAAAA,OAAQ,CAACC,OAAO,CAACE,YAAY;gBAAEC,IAAI;YAAQ;QAC7C;QAEA;IACF;IAEA,gGAAgG;IAChG,EAAE;IACF,kEAAkE;IAClE,yEAAyE;IACzE,IAAI,OAAOC,WAAW,aAAa;QACjC,IAAIC,OAAOC,SAASD,IAAI;QACxBP,YAAYG,OAAO,CAAC,CAACC;YACnB,IAAIK,OAAOD,SAASE,aAAa,CAAC;YAElCD,KAAKE,IAAI,GAAG;YACZF,KAAKG,GAAG,GAAG;YACXH,KAAKI,IAAI,GAAGT;YAEZG,KAAKO,WAAW,CAACL;QACnB;IACF;AACF;AAEA,MAAMM,aAAa,CAACC;IAClB,MAAM,EACJC,GAAG,EACHC,EAAE,EACFC,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdC,uBAAuB,EACvBC,WAAW,EAAE,EACbC,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACZ,GAAGgB;IAEJ,MAAMS,WAAWP,MAAMD;IAEvB,4BAA4B;IAC5B,IAAIQ,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;QACvC;IACF;IAEA,qDAAqD;IACrD,IAAI9B,YAAY+B,GAAG,CAACT,MAAM;QACxBpB,UAAU8B,GAAG,CAACF;QACd,wGAAwG;QACxG,sGAAsG;QACtG9B,YAAYiC,GAAG,CAACX,KAAKY,IAAI,CAACV,QAAQK;QAClC;IACF;IAEA,0CAA0C,GAC1C,MAAMM,YAAY;QAChB,kDAAkD;QAClD,IAAIV,SAAS;YACXA;QACF;QACA,mDAAmD;QACnDvB,UAAU8B,GAAG,CAACF;IAChB;IAEA,MAAMM,KAAKvB,SAASE,aAAa,CAAC;IAElC,MAAMsB,cAAc,IAAIC,QAAc,CAACC,SAASC;QAC9CJ,GAAGK,gBAAgB,CAAC,QAAQ,SAAUC,CAAC;YACrCH;YACA,IAAIf,QAAQ;gBACVA,OAAOmB,IAAI,CAAC,IAAI,EAAED;YACpB;YACAP;QACF;QACAC,GAAGK,gBAAgB,CAAC,SAAS,SAAUC,CAAC;YACtCF,OAAOE;QACT;IACF,GAAGE,KAAK,CAAC,SAAUF,CAAC;QAClB,IAAIb,SAAS;YACXA,QAAQa;QACV;IACF;IAEA,IAAIhB,yBAAyB;QAC3B,2DAA2D;QAC3DU,GAAGS,SAAS,GAAInB,wBAAwBoB,MAAM,IAAe;QAE7DX;IACF,OAAO,IAAIR,UAAU;QACnBS,GAAGW,WAAW,GACZ,OAAOpB,aAAa,WAChBA,WACAqB,MAAMC,OAAO,CAACtB,YACZA,SAASuB,IAAI,CAAC,MACd;QAERf;IACF,OAAO,IAAIb,KAAK;QACdc,GAAGd,GAAG,GAAGA;QACT,4DAA4D;QAC5D,yFAAyF;QAEzFtB,YAAYmD,GAAG,CAAC7B,KAAKe;IACvB;IAEAe,CAAAA,GAAAA,wBAAAA,sBAAsB,EAAChB,IAAIf;IAE3B,IAAIO,aAAa,UAAU;QACzBQ,GAAGiB,YAAY,CAAC,QAAQ;IAC1B;IAEAjB,GAAGiB,YAAY,CAAC,gBAAgBzB;IAEhC,0CAA0C;IAC1C,IAAIvB,aAAa;QACfD,kBAAkBC;IACpB;IAEAQ,SAASyC,IAAI,CAACnC,WAAW,CAACiB;AAC5B;AAEO,SAAStC,uBAAuBuB,KAAkB;IACvD,MAAM,EAAEO,WAAW,kBAAkB,EAAE,GAAGP;IAC1C,IAAIO,aAAa,cAAc;QAC7BjB,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;QACvC;IACF,OAAO;QACLD,WAAWC;IACb;AACF;AAEA,SAASmC,eAAenC,KAAkB;IACxC,IAAIR,SAAS4C,UAAU,KAAK,YAAY;QACtCF,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;IACvC,OAAO;QACLV,OAAO8B,gBAAgB,CAAC,QAAQ;YAC9Bc,CAAAA,GAAAA,qBAAAA,mBAAmB,EAAC,IAAMnC,WAAWC;QACvC;IACF;AACF;AAEA,SAASqC;IACP,MAAMC,UAAU;WACX9C,SAAS+C,gBAAgB,CAAC;WAC1B/C,SAAS+C,gBAAgB,CAAC;KAC9B;IACDD,QAAQnD,OAAO,CAAC,CAACqD;QACf,MAAM/B,WAAW+B,OAAOtC,EAAE,IAAIsC,OAAOC,YAAY,CAAC;QAClD5D,UAAU8B,GAAG,CAACF;IAChB;AACF;AAEO,SAAS/B,iBAAiBgE,iBAAgC;IAC/DA,kBAAkBvD,OAAO,CAACV;IAC1B4D;AACF;AAEA;;;;CAIC,GACD,SAASM,OAAO3C,KAAkB;IAChC,MAAM,EACJE,EAAE,EACFD,MAAM,EAAE,EACRE,SAAS,KAAO,CAAC,EACjBC,UAAU,IAAI,EACdG,WAAW,kBAAkB,EAC7BC,OAAO,EACPxB,WAAW,EACX,GAAG4D,WACJ,GAAG5C;IAEJ,uCAAuC;IACvC,IAAI,EAAE6C,aAAa,EAAEP,OAAO,EAAEQ,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAE,GACrDC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,iCAAAA,kBAAkB;IAE/B,4FAA4F;IAC5FF,QAAQJ,UAAUI,KAAK,IAAIA;IAE3B;;;;;;;;;;;;;;;;;;;;;;;;;GAyBC,GACD,MAAMG,yBAAyBC,CAAAA,GAAAA,OAAAA,MAAM,EAAC;IAEtCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,MAAM5C,WAAWP,MAAMD;QACvB,IAAI,CAACkD,uBAAuBG,OAAO,EAAE;YACnC,sEAAsE;YACtE,IAAIlD,WAAWK,YAAY5B,UAAU6B,GAAG,CAACD,WAAW;gBAClDL;YACF;YAEA+C,uBAAuBG,OAAO,GAAG;QACnC;IACF,GAAG;QAAClD;QAASF;QAAID;KAAI;IAErB,MAAMsD,4BAA4BH,CAAAA,GAAAA,OAAAA,MAAM,EAAC;IAEzCC,CAAAA,GAAAA,OAAAA,SAAS,EAAC;QACR,IAAI,CAACE,0BAA0BD,OAAO,EAAE;YACtC,IAAI/C,aAAa,oBAAoB;gBACnCR,WAAWC;YACb,OAAO,IAAIO,aAAa,cAAc;gBACpC4B,eAAenC;YACjB;YAEAuD,0BAA0BD,OAAO,GAAG;QACtC;IACF,GAAG;QAACtD;QAAOO;KAAS;IAEpB,IAAIA,aAAa,uBAAuBA,aAAa,UAAU;QAC7D,IAAIsC,eAAe;YACjBP,OAAO,CAAC/B,SAAS,GAAI+B,CAAAA,OAAO,CAAC/B,SAAS,IAAI,EAAC,EAAGiD,MAAM,CAAC;gBACnD;oBACEtD;oBACAD;oBACAE;oBACAC;oBACAI;oBACA,GAAGoC,SAAS;oBACZI;gBACF;aACD;YACDH,cAAcP;QAChB,OAAO,IAAIQ,YAAYA,YAAY;YACjC,uCAAuC;YACvCjE,UAAU8B,GAAG,CAACT,MAAMD;QACtB,OAAO,IAAI6C,YAAY,CAACA,YAAY;YAClC/C,WAAW;gBACT,GAAGC,KAAK;gBACRgD;YACF;QACF;IACF;IAEA,uEAAuE;IACvE,IAAID,QAAQ;QACV,oFAAoF;QACpF,uEAAuE;QACvE,oEAAoE;QACpE,6EAA6E;QAC7E,EAAE;QACF,yEAAyE;QACzE,+EAA+E;QAC/E,4EAA4E;QAC5E,wGAAwG;QACxG,IAAI/D,aAAa;YACfA,YAAYG,OAAO,CAAC,CAACsE;gBACnBxE,UAAAA,OAAQ,CAACC,OAAO,CAACuE,UAAU;oBAAEpE,IAAI;gBAAQ;YAC3C;QACF;QAEA,2EAA2E;QAC3E,gEAAgE;QAChE,IAAIkB,aAAa,qBAAqB;YACpC,IAAI,CAACN,KAAK;gBACR,yDAAyD;gBACzD,IAAI2C,UAAUvC,uBAAuB,EAAE;oBACrC,2DAA2D;oBAC3DuC,UAAUtC,QAAQ,GAAGsC,UAAUvC,uBAAuB,CACnDoB,MAAM;oBACT,OAAOmB,UAAUvC,uBAAuB;gBAC1C;gBAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACmC,UAAAA;oBACCQ,OAAOA;oBACP3C,yBAAyB;wBACvBoB,QAAS,4CAAyCiC,KAAKC,SAAS,CAAC;4BAC/D;4BACA;gCAAE,GAAGf,SAAS;gCAAE1C;4BAAG;yBACpB,IAAE;oBACL;;YAGN,OAAO;gBACL,aAAa;gBACbjB,UAAAA,OAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI;oBACJwE,WAAWjB,UAAUiB,SAAS;oBAC9Bb;oBACAc,aAAalB,UAAUkB,WAAW;gBACpC,IACA;oBAAEzE,IAAI;oBAAU2D;oBAAOc,aAAalB,UAAUkB,WAAW;gBAAC;gBAEhE,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACtB,UAAAA;oBACCQ,OAAOA;oBACP3C,yBAAyB;wBACvBoB,QAAS,4CAAyCiC,KAAKC,SAAS,CAAC;4BAC/D1D;4BACA;gCAAE,GAAG2C,SAAS;gCAAE1C;4BAAG;yBACpB,IAAE;oBACL;;YAGN;QACF,OAAO,IAAIK,aAAa,oBAAoB;YAC1C,IAAIN,KAAK;gBACP,aAAa;gBACbhB,UAAAA,OAAQ,CAAC2E,OAAO,CACd3D,KACA2C,UAAUiB,SAAS,GACf;oBACExE,IAAI;oBACJwE,WAAWjB,UAAUiB,SAAS;oBAC9Bb;oBACAc,aAAalB,UAAUkB,WAAW;gBACpC,IACA;oBAAEzE,IAAI;oBAAU2D;oBAAOc,aAAalB,UAAUkB,WAAW;gBAAC;YAElE;QACF;IACF;IAEA,OAAO;AACT;AAEAC,OAAOC,cAAc,CAACrB,QAAQ,gBAAgB;IAAEsB,OAAO;AAAK;MAE5D,WAAetB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/detect-domain-locale.ts"], "sourcesContent": ["import type { detectDomainLocale as Fn } from '../shared/lib/i18n/detect-domain-locale'\n\nexport const detectDomainLocale: typeof Fn = (...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return (\n      require('../shared/lib/i18n/detect-domain-locale') as typeof import('../shared/lib/i18n/detect-domain-locale')\n    ).detectDomainLocale(...args)\n  }\n}\n"], "names": ["detectDomainLocale", "args", "process", "env", "__NEXT_I18N_SUPPORT", "require"], "mappings": "AAGME,QAAQC,GAAG,CAACC,mBAAmB,EAAE;;;;;+BAD1BJ,sBAAAA;;;eAAAA;;;AAAN,MAAMA,qBAAgC;qCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;QAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;IAC/C;;AAKF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/remove-locale.ts"], "sourcesContent": ["import { parsePath } from '../shared/lib/router/utils/parse-path'\n\nexport function removeLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { pathname } = parsePath(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale?.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith(`/${localeLower}/`) ||\n        pathLower === `/${localeLower}`)\n      ? `${pathname.length === locale.length + 1 ? `/` : ``}${path.slice(\n          locale.length + 1\n        )}`\n      : path\n  }\n  return path\n}\n"], "names": ["removeLocale", "path", "locale", "process", "env", "__NEXT_I18N_SUPPORT", "pathname", "parsePath", "pathLower", "toLowerCase", "localeLower", "startsWith", "length", "slice"], "mappings": "AAGMG,QAAQC,GAAG,CAACC,mBAAmB,EAAE;;;;;+BADvBL,gBAAAA;;;eAAAA;;;2BAFU;AAEnB,SAASA,aAAaC,IAAY,EAAEC,MAAe;IACxD;;IAaA,OAAOD;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1329, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/has-base-path.ts"], "sourcesContent": ["import { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function hasBasePath(path: string): boolean {\n  return pathHasPrefix(path, basePath)\n}\n"], "names": ["has<PERSON>ase<PERSON><PERSON>", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "pathHasPrefix"], "mappings": "AAEkBE,QAAQC,GAAG,CAACC,sBAAsB;;;;;+BAEpCJ,eAAAA;;;eAAAA;;;+BAJc;AAE9B,MAAMC,mDAA6D;AAE5D,SAASD,YAAYK,IAAY;IACtC,OAAOC,CAAAA,GAAAA,eAAAA,aAAa,EAACD,MAAMJ;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/remove-base-path.ts"], "sourcesContent": ["import { hasBasePath } from './has-base-path'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function removeBasePath(path: string): string {\n  if (process.env.__NEXT_MANUAL_CLIENT_BASE_PATH) {\n    if (!hasBasePath(path)) {\n      return path\n    }\n  }\n\n  // Can't trim the basePath if it has zero length!\n  if (basePath.length === 0) return path\n\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n"], "names": ["removeBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "__NEXT_MANUAL_CLIENT_BASE_PATH", "has<PERSON>ase<PERSON><PERSON>", "length", "slice", "startsWith"], "mappings": "AAEkBE,QAAQC,GAAG,CAACC,sBAAsB;;;;;+BAEpCJ,kBAAAA;;;eAAAA;;;6BAJY;AAE5B,MAAMC,mDAA6D;AAE5D,SAASD,eAAeK,IAAY;IACzC,IAAIH,QAAQC,GAAG,CAACG,8BAA8B,EAAE;;IAMhD,iDAAiD;IACjD,IAAIL,SAASO,MAAM,KAAK,GAAG,OAAOH;IAElCA,OAAOA,KAAKI,KAAK,CAACR,SAASO,MAAM;IACjC,IAAI,CAACH,KAAKK,UAAU,CAAC,MAAML,OAAQ,MAAGA;IACtC,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/resolve-href.ts"], "sourcesContent": ["import type { NextRouter, Url } from '../shared/lib/router/router'\n\nimport { searchParamsToUrlQuery } from '../shared/lib/router/utils/querystring'\nimport { formatWithValidation } from '../shared/lib/router/utils/format-url'\nimport { omit } from '../shared/lib/router/utils/omit'\nimport { normalizeRepeatedSlashes } from '../shared/lib/utils'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { isDynamicRoute } from '../shared/lib/router/utils'\nimport { interpolateAs } from '../shared/lib/router/utils/interpolate-as'\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs: true\n): [string, string] | [string]\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: false\n): string\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: boolean\n): [string, string] | [string] | string {\n  // we use a dummy base url for relative urls\n  let base: URL\n  let urlAsString = typeof href === 'string' ? href : formatWithValidation(href)\n\n  // repeated slashes and backslashes in the URL are considered\n  // invalid and will never match a Next.js page/file\n  // https://www.rfc-editor.org/rfc/rfc3986.html#section-3.1\n  const urlProtoMatch = urlAsString.match(/^[a-z][a-z0-9+.-]*:\\/\\//i)\n  const urlAsStringNoProto = urlProtoMatch\n    ? urlAsString.slice(urlProtoMatch[0].length)\n    : urlAsString\n\n  const urlParts = urlAsStringNoProto.split('?', 1)\n\n  if ((urlParts[0] || '').match(/(\\/\\/|\\\\)/)) {\n    console.error(\n      `Invalid href '${urlAsString}' passed to next/router in page: '${router.pathname}'. Repeated forward-slashes (//) or backslashes \\\\ are not valid in the href.`\n    )\n    const normalizedUrl = normalizeRepeatedSlashes(urlAsStringNoProto)\n    urlAsString = (urlProtoMatch ? urlProtoMatch[0] : '') + normalizedUrl\n  }\n\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n\n  try {\n    base = new URL(\n      urlAsString.startsWith('#') ? router.asPath : router.pathname,\n      'http://n'\n    )\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omit(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return resolveAs\n      ? [resolvedHref, interpolatedAs || resolvedHref]\n      : resolvedHref\n  } catch (_) {\n    return resolveAs ? [urlAsString] : urlAsString\n  }\n}\n"], "names": ["resolveHref", "router", "href", "resolveAs", "base", "urlAsString", "formatWithValidation", "urlProtoMatch", "match", "urlAsStringNoProto", "slice", "length", "urlParts", "split", "console", "error", "pathname", "normalizedUrl", "normalizeRepeatedSlashes", "isLocalURL", "URL", "startsWith", "<PERSON><PERSON><PERSON>", "_", "finalUrl", "normalizePathTrailingSlash", "interpolatedAs", "isDynamicRoute", "searchParams", "query", "searchParamsToUrlQuery", "result", "params", "interpolateAs", "hash", "omit", "resolvedHref", "origin"], "mappings": ";;;+BAyBgBA,eAAAA;;;eAAAA;;;6BAvBuB;2BACF;sBAChB;uBACoB;wCACE;4BAChB;wBACI;+BACD;AAgBvB,SAASA,YACdC,MAAkB,EAClBC,IAAS,EACTC,SAAmB;IAEnB,4CAA4C;IAC5C,IAAIC;IACJ,IAAIC,cAAc,OAAOH,SAAS,WAAWA,OAAOI,CAAAA,GAAAA,WAAAA,oBAAoB,EAACJ;IAEzE,6DAA6D;IAC7D,mDAAmD;IACnD,0DAA0D;IAC1D,MAAMK,gBAAgBF,YAAYG,KAAK,CAAC;IACxC,MAAMC,qBAAqBF,gBACvBF,YAAYK,KAAK,CAACH,aAAa,CAAC,EAAE,CAACI,MAAM,IACzCN;IAEJ,MAAMO,WAAWH,mBAAmBI,KAAK,CAAC,KAAK;IAE/C,IAAKD,CAAAA,QAAQ,CAAC,EAAE,IAAI,EAAC,EAAGJ,KAAK,CAAC,cAAc;QAC1CM,QAAQC,KAAK,CACV,mBAAgBV,cAAY,uCAAoCJ,OAAOe,QAAQ,GAAC;QAEnF,MAAMC,gBAAgBC,CAAAA,GAAAA,OAAAA,wBAAwB,EAACT;QAC/CJ,cAAeE,CAAAA,gBAAgBA,aAAa,CAAC,EAAE,GAAG,EAAC,IAAKU;IAC1D;IAEA,2DAA2D;IAC3D,IAAI,CAACE,CAAAA,GAAAA,YAAAA,UAAU,EAACd,cAAc;QAC5B,OAAQF,YAAY;YAACE;SAAY,GAAGA;IACtC;IAEA,IAAI;QACFD,OAAO,IAAIgB,IACTf,YAAYgB,UAAU,CAAC,OAAOpB,OAAOqB,MAAM,GAAGrB,OAAOe,QAAQ,EAC7D;IAEJ,EAAE,OAAOO,GAAG;QACV,kDAAkD;QAClDnB,OAAO,IAAIgB,IAAI,KAAK;IACtB;IAEA,IAAI;QACF,MAAMI,WAAW,IAAIJ,IAAIf,aAAaD;QACtCoB,SAASR,QAAQ,GAAGS,CAAAA,GAAAA,wBAAAA,0BAA0B,EAACD,SAASR,QAAQ;QAChE,IAAIU,iBAAiB;QAErB,IACEC,CAAAA,GAAAA,QAAAA,cAAc,EAACH,SAASR,QAAQ,KAChCQ,SAASI,YAAY,IACrBzB,WACA;YACA,MAAM0B,QAAQC,CAAAA,GAAAA,aAAAA,sBAAsB,EAACN,SAASI,YAAY;YAE1D,MAAM,EAAEG,MAAM,EAAEC,MAAM,EAAE,GAAGC,CAAAA,GAAAA,eAAAA,aAAa,EACtCT,SAASR,QAAQ,EACjBQ,SAASR,QAAQ,EACjBa;YAGF,IAAIE,QAAQ;gBACVL,iBAAiBpB,CAAAA,GAAAA,WAAAA,oBAAoB,EAAC;oBACpCU,UAAUe;oBACVG,MAAMV,SAASU,IAAI;oBACnBL,OAAOM,CAAAA,GAAAA,MAAAA,IAAI,EAACN,OAAOG;gBACrB;YACF;QACF;QAEA,oEAAoE;QACpE,MAAMI,eACJZ,SAASa,MAAM,KAAKjC,KAAKiC,MAAM,GAC3Bb,SAAStB,IAAI,CAACQ,KAAK,CAACc,SAASa,MAAM,CAAC1B,MAAM,IAC1Ca,SAAStB,IAAI;QAEnB,OAAOC,YACH;YAACiC;YAAcV,kBAAkBU;SAAa,GAC9CA;IACN,EAAE,OAAOb,GAAG;QACV,OAAOpB,YAAY;YAACE;SAAY,GAAGA;IACrC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/with-router.tsx"], "sourcesContent": ["import React, { type JSX } from 'react'\nimport type {\n  BaseContext,\n  NextComponentType,\n  NextPageContext,\n} from '../shared/lib/utils'\nimport type { NextRouter } from './router'\nimport { useRouter } from './router'\n\nexport type WithRouterProps = {\n  router: NextRouter\n}\n\nexport type ExcludeRouterProps<P> = Pick<\n  P,\n  Exclude<keyof P, keyof WithRouterProps>\n>\n\nexport default function withRouter<\n  P extends WithRouterProps,\n  C extends BaseContext = NextPageContext,\n>(\n  ComposedComponent: NextComponentType<C, any, P>\n): React.ComponentType<ExcludeRouterProps<P>> {\n  function WithRouterWrapper(props: any): JSX.Element {\n    return <ComposedComponent router={useRouter()} {...props} />\n  }\n\n  WithRouterWrapper.getInitialProps = ComposedComponent.getInitialProps\n  // This is needed to allow checking for custom getInitialProps in _app\n  ;(WithRouterWrapper as any).origGetInitialProps = (\n    ComposedComponent as any\n  ).origGetInitialProps\n  if (process.env.NODE_ENV !== 'production') {\n    const name =\n      ComposedComponent.displayName || ComposedComponent.name || 'Unknown'\n    WithRouterWrapper.displayName = `withRouter(${name})`\n  }\n\n  return WithRouterWrapper\n}\n"], "names": ["with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "router", "useRouter", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": "AAiCMQ,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAf/B,WAAA;;;eAAwBV;;;;;gEAlBQ;wBAON;AAWX,SAASA,WAItBC,iBAA+C;IAE/C,SAASC,kBAAkBC,KAAU;QACnC,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACF,mBAAAA;YAAkBG,QAAQC,CAAAA,GAAAA,QAAAA,SAAS;YAAK,GAAGF,KAAK;;IAC1D;IAEAD,kBAAkBI,eAAe,GAAGL,kBAAkBK,eAAe;IAEnEJ,kBAA0BK,mBAAmB,GAC7CN,kBACAM,mBAAmB;IACrB,wCAA2C;QACzC,MAAMI,OACJV,kBAAkBW,WAAW,IAAIX,kBAAkBU,IAAI,IAAI;QAC7DT,kBAAkBU,WAAW,GAAI,gBAAaD,OAAK;IACrD;IAEA,OAAOT;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/router.ts"], "sourcesContent": ["/* global window */\nimport React from 'react'\nimport Router from '../shared/lib/router/router'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport isError from '../lib/is-error'\n\ntype SingletonRouterBase = {\n  router: Router | null\n  readyCallbacks: Array<() => any>\n  ready(cb: () => any): void\n}\n\nexport { Router }\n\nexport type { NextRouter }\n\nexport type SingletonRouter = SingletonRouterBase & NextRouter\n\nconst singletonRouter: SingletonRouterBase = {\n  router: null, // holds the actual router instance\n  readyCallbacks: [],\n  ready(callback: () => void) {\n    if (this.router) return callback()\n    if (typeof window !== 'undefined') {\n      this.readyCallbacks.push(callback)\n    }\n  },\n}\n\n// Create public properties and methods of the router in the singletonRouter\nconst urlPropertyFields = [\n  'pathname',\n  'route',\n  'query',\n  'asPath',\n  'components',\n  'isFallback',\n  'basePath',\n  'locale',\n  'locales',\n  'defaultLocale',\n  'isReady',\n  'isPreview',\n  'isLocaleDomain',\n  'domainLocales',\n] as const\nconst routerEvents = [\n  'routeChangeStart',\n  'beforeHistoryChange',\n  'routeChangeComplete',\n  'routeChangeError',\n  'hashChangeStart',\n  'hashChangeComplete',\n] as const\nexport type RouterEvent = (typeof routerEvents)[number]\n\nconst coreMethodFields = [\n  'push',\n  'replace',\n  'reload',\n  'back',\n  'prefetch',\n  'beforePopState',\n] as const\n\n// Events is a static property on the router, the router doesn't have to be initialized to use it\nObject.defineProperty(singletonRouter, 'events', {\n  get() {\n    return Router.events\n  },\n})\n\nfunction getRouter(): Router {\n  if (!singletonRouter.router) {\n    const message =\n      'No router instance found.\\n' +\n      'You should only use \"next/router\" on the client side of your app.\\n'\n    throw new Error(message)\n  }\n  return singletonRouter.router\n}\n\nurlPropertyFields.forEach((field) => {\n  // Here we need to use Object.defineProperty because we need to return\n  // the property assigned to the actual router\n  // The value might get changed as we change routes and this is the\n  // proper way to access it\n  Object.defineProperty(singletonRouter, field, {\n    get() {\n      const router = getRouter()\n      return router[field] as string\n    },\n  })\n})\n\ncoreMethodFields.forEach((field) => {\n  // We don't really know the types here, so we add them later instead\n  ;(singletonRouter as any)[field] = (...args: any[]) => {\n    const router = getRouter() as any\n    return router[field](...args)\n  }\n})\n\nrouterEvents.forEach((event) => {\n  singletonRouter.ready(() => {\n    Router.events.on(event, (...args) => {\n      const eventField = `on${event.charAt(0).toUpperCase()}${event.substring(\n        1\n      )}`\n      const _singletonRouter = singletonRouter as any\n      if (_singletonRouter[eventField]) {\n        try {\n          _singletonRouter[eventField](...args)\n        } catch (err) {\n          console.error(`Error when running the Router event: ${eventField}`)\n          console.error(\n            isError(err) ? `${err.message}\\n${err.stack}` : err + ''\n          )\n        }\n      }\n    })\n  })\n})\n\n// Export the singletonRouter and this is the public API.\nexport default singletonRouter as SingletonRouter\n\n// Reexport the withRouter HOC\nexport { default as withRouter } from './with-router'\n\n/**\n * This hook gives access the [router object](https://nextjs.org/docs/pages/api-reference/functions/use-router#router-object)\n * inside the [Pages Router](https://nextjs.org/docs/pages/building-your-application).\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/pages/api-reference/functions/use-router)\n */\nexport function useRouter(): NextRouter {\n  const router = React.useContext(RouterContext)\n  if (!router) {\n    throw new Error(\n      'NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted'\n    )\n  }\n\n  return router\n}\n\n/**\n * Create a router and assign it as the singleton instance.\n * This is used in client side when we are initializing the app.\n * This should **not** be used inside the server.\n * @internal\n */\nexport function createRouter(\n  ...args: ConstructorParameters<typeof Router>\n): Router {\n  singletonRouter.router = new Router(...args)\n  singletonRouter.readyCallbacks.forEach((cb) => cb())\n  singletonRouter.readyCallbacks = []\n\n  return singletonRouter.router\n}\n\n/**\n * This function is used to create the `withRouter` router instance\n * @internal\n */\nexport function makePublicRouterInstance(router: Router): NextRouter {\n  const scopedRouter = router as any\n  const instance = {} as any\n\n  for (const property of urlPropertyFields) {\n    if (typeof scopedRouter[property] === 'object') {\n      instance[property] = Object.assign(\n        Array.isArray(scopedRouter[property]) ? [] : {},\n        scopedRouter[property]\n      ) // makes sure query is not stateful\n      continue\n    }\n\n    instance[property] = scopedRouter[property]\n  }\n\n  // Events is a static property on the router, the router doesn't have to be initialized to use it\n  instance.events = Router.events\n\n  coreMethodFields.forEach((field) => {\n    instance[field] = (...args: any[]) => {\n      return scopedRouter[field](...args)\n    }\n  })\n\n  return instance\n}\n"], "names": ["Router", "createRouter", "makePublicRouterInstance", "useRouter", "with<PERSON><PERSON><PERSON>", "singletonRouter", "router", "readyCallbacks", "ready", "callback", "window", "push", "url<PERSON><PERSON><PERSON><PERSON>ields", "routerEvents", "core<PERSON><PERSON><PERSON><PERSON><PERSON>s", "Object", "defineProperty", "get", "events", "getRouter", "message", "Error", "for<PERSON>ach", "field", "args", "event", "on", "eventField", "char<PERSON>t", "toUpperCase", "substring", "_singletonRouter", "err", "console", "error", "isError", "stack", "React", "useContext", "RouterContext", "cb", "scopedRouter", "instance", "property", "assign", "Array", "isArray"], "mappings": "AAAA,iBAAiB;;;;;;;;;;;;;;;;;;IAaRA,MAAM,EAAA;eAANA,QAAAA,OAAM;;IA6ICC,YAAY,EAAA;eAAZA;;IA7BhB,yDAAyD;IACzD,OAAiD,EAAA;eAAjD;;IA0CgBC,wBAAwB,EAAA;eAAxBA;;IA/BAC,SAAS,EAAA;eAATA;;IARIC,UAAU,EAAA;eAAVA,YAAAA,OAAU;;;;gEAhIZ;iEACC;4CAEW;kEACV;qEA4HkB;AA9GtC,MAAMC,kBAAuC;IAC3CC,QAAQ;IACRC,gBAAgB,EAAE;IAClBC,OAAMC,QAAoB;QACxB,IAAI,IAAI,CAACH,MAAM,EAAE,OAAOG;QACxB,IAAI,OAAOC,WAAW,aAAa;YACjC,IAAI,CAACH,cAAc,CAACI,IAAI,CAACF;QAC3B;IACF;AACF;AAEA,4EAA4E;AAC5E,MAAMG,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAMC,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAGD,MAAMC,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iGAAiG;AACjGC,OAAOC,cAAc,CAACX,iBAAiB,UAAU;IAC/CY;QACE,OAAOjB,QAAAA,OAAM,CAACkB,MAAM;IACtB;AACF;AAEA,SAASC;IACP,IAAI,CAACd,gBAAgBC,MAAM,EAAE;QAC3B,MAAMc,UACJ,gCACA;QACF,MAAM,OAAA,cAAkB,CAAlB,IAAIC,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IACA,OAAOf,gBAAgBC,MAAM;AAC/B;AAEAM,kBAAkBU,OAAO,CAAC,CAACC;IACzB,sEAAsE;IACtE,6CAA6C;IAC7C,kEAAkE;IAClE,0BAA0B;IAC1BR,OAAOC,cAAc,CAACX,iBAAiBkB,OAAO;QAC5CN;YACE,MAAMX,SAASa;YACf,OAAOb,MAAM,CAACiB,MAAM;QACtB;IACF;AACF;AAEAT,iBAAiBQ,OAAO,CAAC,CAACC;IACxB,oEAAoE;;IAClElB,eAAuB,CAACkB,MAAM,GAAG;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACrC,MAAMlB,SAASa;QACf,OAAOb,MAAM,CAACiB,MAAM,IAAIC;IAC1B;AACF;AAEAX,aAAaS,OAAO,CAAC,CAACG;IACpBpB,gBAAgBG,KAAK,CAAC;QACpBR,QAAAA,OAAM,CAACkB,MAAM,CAACQ,EAAE,CAACD,OAAO;6CAAID,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;gBAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;YAC1B,MAAMG,aAAc,OAAIF,MAAMG,MAAM,CAAC,GAAGC,WAAW,KAAKJ,MAAMK,SAAS,CACrE;YAEF,MAAMC,mBAAmB1B;YACzB,IAAI0B,gBAAgB,CAACJ,WAAW,EAAE;gBAChC,IAAI;oBACFI,gBAAgB,CAACJ,WAAW,IAAIH;gBAClC,EAAE,OAAOQ,KAAK;oBACZC,QAAQC,KAAK,CAAE,0CAAuCP;oBACtDM,QAAQC,KAAK,CACXC,CAAAA,GAAAA,SAAAA,OAAO,EAACH,OAAUA,IAAIZ,OAAO,GAAC,OAAIY,IAAII,KAAK,GAAKJ,MAAM;gBAE1D;YACF;QACF;IACF;AACF;MAGA,WAAe3B;AAWR,SAASF;IACd,MAAMG,SAAS+B,OAAAA,OAAK,CAACC,UAAU,CAACC,4BAAAA,aAAa;IAC7C,IAAI,CAACjC,QAAQ;QACX,MAAM,OAAA,cAEL,CAFK,IAAIe,MACR,yFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOf;AACT;AAQO,SAASL;IACd,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGuB,OAAH,IAAA,MAAA,OAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,IAAAA,CAAH,KAAA,GAAA,SAAA,CAAA,KAA6C;;IAE7CnB,gBAAgBC,MAAM,GAAG,IAAIN,QAAAA,OAAM,IAAIwB;IACvCnB,gBAAgBE,cAAc,CAACe,OAAO,CAAC,CAACkB,KAAOA;IAC/CnC,gBAAgBE,cAAc,GAAG,EAAE;IAEnC,OAAOF,gBAAgBC,MAAM;AAC/B;AAMO,SAASJ,yBAAyBI,MAAc;IACrD,MAAMmC,eAAenC;IACrB,MAAMoC,WAAW,CAAC;IAElB,KAAK,MAAMC,YAAY/B,kBAAmB;QACxC,IAAI,OAAO6B,YAAY,CAACE,SAAS,KAAK,UAAU;YAC9CD,QAAQ,CAACC,SAAS,GAAG5B,OAAO6B,MAAM,CAChCC,MAAMC,OAAO,CAACL,YAAY,CAACE,SAAS,IAAI,EAAE,GAAG,CAAC,GAC9CF,YAAY,CAACE,SAAS,EACtB,mCAAmC;;YACrC;QACF;QAEAD,QAAQ,CAACC,SAAS,GAAGF,YAAY,CAACE,SAAS;IAC7C;IAEA,iGAAiG;IACjGD,SAASxB,MAAM,GAAGlB,QAAAA,OAAM,CAACkB,MAAM;IAE/BJ,iBAAiBQ,OAAO,CAAC,CAACC;QACxBmB,QAAQ,CAACnB,MAAM,GAAG;6CAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;gBAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;YACpB,OAAOiB,YAAY,CAAClB,MAAM,IAAIC;QAChC;IACF;IAEA,OAAOkB;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1723, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/route-announcer.tsx"], "sourcesContent": ["import React from 'react'\nimport { useRouter } from './router'\n\nconst nextjsRouteAnnouncerStyles: React.CSSProperties = {\n  border: 0,\n  clip: 'rect(0 0 0 0)',\n  height: '1px',\n  margin: '-1px',\n  overflow: 'hidden',\n  padding: 0,\n  position: 'absolute',\n  top: 0,\n  width: '1px',\n\n  // https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe\n  whiteSpace: 'nowrap',\n  wordWrap: 'normal',\n}\n\nexport const RouteAnnouncer = () => {\n  const { asPath } = useRouter()\n  const [routeAnnouncement, setRouteAnnouncement] = React.useState('')\n\n  // Only announce the path change, but not for the first load because screen\n  // reader will do that automatically.\n  const previouslyLoadedPath = React.useRef(asPath)\n\n  // Every time the path changes, announce the new page’s title following this\n  // priority: first the document title (from head), otherwise the first h1, or\n  // if none of these exist, then the pathname from the URL. This methodology is\n  // inspired by <PERSON><PERSON>’s accessible client routing user testing. More\n  // information can be found here:\n  // https://www.gatsbyjs.com/blog/2019-07-11-user-testing-accessible-client-routing/\n  React.useEffect(\n    () => {\n      // If the path hasn't change, we do nothing.\n      if (previouslyLoadedPath.current === asPath) return\n      previouslyLoadedPath.current = asPath\n\n      if (document.title) {\n        setRouteAnnouncement(document.title)\n      } else {\n        const pageHeader = document.querySelector('h1')\n        const content = pageHeader?.innerText ?? pageHeader?.textContent\n\n        setRouteAnnouncement(content || asPath)\n      }\n    },\n    // TODO: switch to pathname + query object of dynamic route requirements\n    [asPath]\n  )\n\n  return (\n    <p\n      aria-live=\"assertive\" // Make the announcement immediately.\n      id=\"__next-route-announcer__\"\n      role=\"alert\"\n      style={nextjsRouteAnnouncerStyles}\n    >\n      {routeAnnouncement}\n    </p>\n  )\n}\n\nexport default RouteAnnouncer\n"], "names": ["RouteAnnouncer", "nextjsRouteAnnouncerStyles", "border", "clip", "height", "margin", "overflow", "padding", "position", "top", "width", "whiteSpace", "wordWrap", "<PERSON><PERSON><PERSON>", "useRouter", "routeAnnouncement", "setRouteAnnouncement", "React", "useState", "previouslyLoaded<PERSON><PERSON>", "useRef", "useEffect", "current", "document", "title", "pageHeader", "querySelector", "content", "innerText", "textContent", "p", "aria-live", "id", "role", "style"], "mappings": ";;;;;;;;;;;;;;IAmBaA,cAAc,EAAA;eAAdA;;IA6Cb,OAA6B,EAAA;eAA7B;;;;;gEAhEkB;wBACQ;AAE1B,MAAMC,6BAAkD;IACtDC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,KAAK;IACLC,OAAO;IAEP,wFAAwF;IACxFC,YAAY;IACZC,UAAU;AACZ;AAEO,MAAMZ,iBAAiB;IAC5B,MAAM,EAAEa,MAAM,EAAE,GAAGC,CAAAA,GAAAA,QAAAA,SAAS;IAC5B,MAAM,CAACC,mBAAmBC,qBAAqB,GAAGC,OAAAA,OAAK,CAACC,QAAQ,CAAC;IAEjE,2EAA2E;IAC3E,qCAAqC;IACrC,MAAMC,uBAAuBF,OAAAA,OAAK,CAACG,MAAM,CAACP;IAE1C,4EAA4E;IAC5E,6EAA6E;IAC7E,8EAA8E;IAC9E,0EAA0E;IAC1E,iCAAiC;IACjC,mFAAmF;IACnFI,OAAAA,OAAK,CAACI,SAAS;oCACb;YACE,4CAA4C;YAC5C,IAAIF,qBAAqBG,OAAO,KAAKT,QAAQ;YAC7CM,qBAAqBG,OAAO,GAAGT;YAE/B,IAAIU,SAASC,KAAK,EAAE;gBAClBR,qBAAqBO,SAASC,KAAK;YACrC,OAAO;gBACL,MAAMC,aAAaF,SAASG,aAAa,CAAC;oBAC1BD;gBAAhB,MAAME,UAAUF,CAAAA,wBAAAA,cAAAA,OAAAA,KAAAA,IAAAA,WAAYG,SAAS,KAAA,OAArBH,wBAAyBA,cAAAA,OAAAA,KAAAA,IAAAA,WAAYI,WAAW;gBAEhEb,qBAAqBW,WAAWd;YAClC;QACF,GACA,wEAAwE;mCACxE;QAACA;KAAO;IAGV,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACiB,KAAAA;QACCC,aAAU,YAAY,qCAAqC;;QAC3DC,IAAG;QACHC,MAAK;QACLC,OAAOjC;kBAENc;;AAGP;MAEA,WAAef", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/react-client-callbacks/report-global-error.ts"], "sourcesContent": ["export const reportGlobalError =\n  typeof reportError === 'function'\n    ? // In modern browsers, reportError will dispatch an error event,\n      // emulating an uncaught JavaScript error.\n      reportError\n    : (error: unknown) => {\n        // TODO: Dispatch error event\n        globalThis.console.error(error)\n      }\n"], "names": ["reportGlobalError", "reportError", "error", "globalThis", "console"], "mappings": ";;;+BAAaA,qBAAAA;;;eAAAA;;;AAAN,MAAMA,oBACX,OAAOC,gBAAgB,aAEnB,AACAA,cACA,CAACC,2BAFyC;IAGxC,6BAA6B;IAC7BC,WAAWC,OAAO,CAACF,KAAK,CAACA;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/react-client-callbacks/on-recoverable-error.ts"], "sourcesContent": ["// This module can be shared between both pages router and app router\n\nimport type { HydrationOptions } from 'react-dom/client'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport isError from '../../lib/is-error'\nimport { reportGlobalError } from './report-global-error'\n\nconst recoverableErrors = new WeakSet<Error>()\n\nexport function isRecoverableError(error: Error): boolean {\n  return recoverableErrors.has(error)\n}\n\nexport const onRecoverableError: HydrationOptions['onRecoverableError'] = (\n  error,\n  errorInfo\n) => {\n  // x-ref: https://github.com/facebook/react/pull/28736\n  let cause = isError(error) && 'cause' in error ? error.cause : error\n  // Skip certain custom errors which are not expected to be reported on client\n  if (isBailoutToCSRError(cause)) return\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { decorateDevError } =\n      require('../../next-devtools/userspace/app/errors/stitched-error') as typeof import('../../next-devtools/userspace/app/errors/stitched-error')\n    const causeError = decorateDevError(cause, errorInfo)\n    recoverableErrors.add(causeError)\n    cause = causeError\n  }\n\n  reportGlobalError(cause)\n}\n"], "names": ["isRecoverableError", "onRecoverableError", "recoverableErrors", "WeakSet", "error", "has", "errorInfo", "cause", "isError", "isBailoutToCSRError", "process", "env", "NODE_ENV", "decorateDevError", "require", "causeError", "add", "reportGlobalError"], "mappings": "AAAA,qEAAqE;AAsB/DU,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;IAbfZ,kBAAkB,EAAA;eAAlBA;;IAIHC,kBAAkB,EAAA;eAAlBA;;;;8BAVuB;kEAChB;mCACc;AAElC,MAAMC,oBAAoB,IAAIC;AAEvB,SAASH,mBAAmBI,KAAY;IAC7C,OAAOF,kBAAkBG,GAAG,CAACD;AAC/B;AAEO,MAAMH,qBAA6D,CACxEG,OACAE;IAEA,sDAAsD;IACtD,IAAIC,QAAQC,CAAAA,GAAAA,SAAAA,OAAO,EAACJ,UAAU,WAAWA,QAAQA,MAAMG,KAAK,GAAGH;IAC/D,6EAA6E;IAC7E,IAAIK,CAAAA,GAAAA,cAAAA,mBAAmB,EAACF,QAAQ;IAEhC,wCAA2C;QACzC,MAAM,EAAEM,gBAAgB,EAAE,GACxBC,QAAQ;QACV,MAAMC,aAAaF,iBAAiBN,OAAOD;QAC3CJ,kBAAkBc,GAAG,CAACD;QACtBR,QAAQQ;IACV;IAEAE,CAAAA,GAAAA,mBAAAA,iBAAiB,EAACV;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1898, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/tracing/tracer.ts"], "sourcesContent": ["import mitt from '../../shared/lib/mitt'\nimport type { MittEmitter } from '../../shared/lib/mitt'\n\nexport type SpanOptions = {\n  startTime?: number\n  attributes?: Record<string, unknown>\n}\n\nexport type SpanState =\n  | {\n      state: 'inprogress'\n    }\n  | {\n      state: 'ended'\n      endTime: number\n    }\n\ninterface ISpan {\n  name: string\n  startTime: number\n  attributes: Record<string, unknown>\n  state: SpanState\n  end(endTime?: number): void\n}\n\nclass Span implements ISpan {\n  name: string\n  startTime: number\n  onSpanEnd: (span: Span) => void\n  state: SpanState\n  attributes: Record<string, unknown>\n\n  constructor(\n    name: string,\n    options: SpanOptions,\n    onSpanEnd: (span: Span) => void\n  ) {\n    this.name = name\n    this.attributes = options.attributes ?? {}\n    this.startTime = options.startTime ?? Date.now()\n    this.onSpanEnd = onSpanEnd\n    this.state = { state: 'inprogress' }\n  }\n\n  end(endTime?: number) {\n    if (this.state.state === 'ended') {\n      throw new Error('Span has already ended')\n    }\n\n    this.state = {\n      state: 'ended',\n      endTime: endTime ?? Date.now(),\n    }\n\n    this.onSpanEnd(this)\n  }\n}\n\nclass Tracer {\n  _emitter: MittEmitter<string> = mitt()\n\n  private handleSpanEnd = (span: Span) => {\n    this._emitter.emit('spanend', span)\n  }\n\n  startSpan(name: string, options: SpanOptions) {\n    return new Span(name, options, this.handleSpanEnd)\n  }\n\n  onSpanEnd(cb: (span: ISpan) => void): () => void {\n    this._emitter.on('spanend', cb)\n    return () => {\n      this._emitter.off('spanend', cb)\n    }\n  }\n}\n\nexport type { ISpan as Span }\nexport default new Tracer()\n"], "names": ["Span", "end", "endTime", "state", "Error", "Date", "now", "onSpanEnd", "constructor", "name", "options", "attributes", "startTime", "Tracer", "startSpan", "handleSpanEnd", "cb", "_emitter", "on", "off", "mitt", "span", "emit"], "mappings": ";;;+BA8EA,WAAA;;;eAAA;;;;+DA9EiB;AAyBjB,MAAMA;IAmBJC,IAAIC,OAAgB,EAAE;QACpB,IAAI,IAAI,CAACC,KAAK,CAACA,KAAK,KAAK,SAAS;YAChC,MAAM,OAAA,cAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAkC;QAC1C;QAEA,IAAI,CAACD,KAAK,GAAG;YACXA,OAAO;YACPD,SAASA,WAAAA,OAAAA,UAAWG,KAAKC,GAAG;QAC9B;QAEA,IAAI,CAACC,SAAS,CAAC,IAAI;IACrB;IAvBAC,YACEC,IAAY,EACZC,OAAoB,EACpBH,SAA+B,CAC/B;QACA,IAAI,CAACE,IAAI,GAAGA;YACMC;QAAlB,IAAI,CAACC,UAAU,GAAGD,CAAAA,sBAAAA,QAAQC,UAAU,KAAA,OAAlBD,sBAAsB,CAAC;YACxBA;QAAjB,IAAI,CAACE,SAAS,GAAGF,CAAAA,qBAAAA,QAAQE,SAAS,KAAA,OAAjBF,qBAAqBL,KAAKC,GAAG;QAC9C,IAAI,CAACC,SAAS,GAAGA;QACjB,IAAI,CAACJ,KAAK,GAAG;YAAEA,OAAO;QAAa;IACrC;AAcF;AAEA,MAAMU;IAOJC,UAAUL,IAAY,EAAEC,OAAoB,EAAE;QAC5C,OAAO,IAAIV,KAAKS,MAAMC,SAAS,IAAI,CAACK,aAAa;IACnD;IAEAR,UAAUS,EAAyB,EAAc;QAC/C,IAAI,CAACC,QAAQ,CAACC,EAAE,CAAC,WAAWF;QAC5B,OAAO;YACL,IAAI,CAACC,QAAQ,CAACE,GAAG,CAAC,WAAWH;QAC/B;IACF;;aAfAC,QAAAA,GAAgCG,CAAAA,GAAAA,MAAAA,OAAI;aAE5BL,aAAAA,GAAgB,CAACM;YACvB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,WAAWD;QAChC;;AAYF;MAGA,WAAe,IAAIR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "HTTP_ERROR_FALLBACK_ERROR_CODE", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "status"], "mappings": ";;;;;;;;;;;;;;;;;IAAaA,qBAAqB,EAAA;eAArBA;;IAQAC,8BAA8B,EAAA;eAA9BA;;IAuCGC,kCAAkC,EAAA;eAAlCA;;IAPAC,2BAA2B,EAAA;eAA3BA;;IAnBAC,yBAAyB,EAAA;eAAzBA;;;AArBT,MAAMJ,wBAAwB;IACnCK,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB;AAEA,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACX;AAErC,MAAMC,iCAAiC;AAavC,SAASG,0BACdQ,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWb,kCACXO,cAAcS,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASZ,4BACdS,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASb,mCACdiB,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2043, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;+BAAYA,sBAAAA;;;eAAAA;;;AAAL,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN", "RedirectStatusCode"], "mappings": ";;;;;;;;;;;;;;;IAEaA,mBAAmB,EAAA;eAAnBA;;IAEDC,YAAY,EAAA;eAAZA;;IAgBIC,eAAe,EAAA;eAAfA;;;oCApBmB;AAE5B,MAAMF,sBAAsB;AAE5B,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;;AAgBL,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcG,oBAAAA,kBAAkB;AAEpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2127, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isNextRouterError", "error", "isRedirectError", "isHTTPAccessFallbackError"], "mappings": ";;;+BAWgBA,qBAAAA;;;eAAAA;;;oCART;+BAC6C;AAO7C,SAASA,kBACdC,KAAc;IAEd,OAAOC,CAAAA,GAAAA,eAAAA,eAAe,EAACD,UAAUE,CAAAA,GAAAA,oBAAAA,yBAAyB,EAACF;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2154, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/hot-reloader/get-socket-url.ts"], "sourcesContent": ["import { normalizedAssetPrefix } from '../../../shared/lib/normalized-asset-prefix'\n\nfunction getSocketProtocol(assetPrefix: string): string {\n  let protocol = window.location.protocol\n\n  try {\n    // assetPrefix is a url\n    protocol = new URL(assetPrefix).protocol\n  } catch {}\n\n  return protocol === 'http:' ? 'ws:' : 'wss:'\n}\n\nexport function getSocketUrl(assetPrefix: string | undefined): string {\n  const prefix = normalizedAssetPrefix(assetPrefix)\n  const protocol = getSocketProtocol(assetPrefix || '')\n\n  if (URL.canParse(prefix)) {\n    // since normalized asset prefix is ensured to be a URL format,\n    // we can safely replace the protocol\n    return prefix.replace(/^http/, 'ws')\n  }\n\n  const { hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? `:${port}` : ''}${prefix}`\n}\n"], "names": ["getSocketUrl", "getSocketProtocol", "assetPrefix", "protocol", "window", "location", "URL", "prefix", "normalizedAssetPrefix", "canParse", "replace", "hostname", "port"], "mappings": ";;;+BAagBA,gBAAAA;;;eAAAA;;;uCAbsB;AAEtC,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,OAAOC,QAAQ,CAACF,QAAQ;IAEvC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIG,IAAIJ,aAAaC,QAAQ;IAC1C,EAAE,OAAA,GAAM,CAAC;IAET,OAAOA,aAAa,UAAU,QAAQ;AACxC;AAEO,SAASH,aAAaE,WAA+B;IAC1D,MAAMK,SAASC,CAAAA,GAAAA,uBAAAA,qBAAqB,EAACN;IACrC,MAAMC,WAAWF,kBAAkBC,eAAe;IAElD,IAAII,IAAIG,QAAQ,CAACF,SAAS;QACxB,+DAA+D;QAC/D,qCAAqC;QACrC,OAAOA,OAAOG,OAAO,CAAC,SAAS;IACjC;IAEA,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGR,OAAOC,QAAQ;IAC1C,OAAUF,WAAS,OAAIQ,WAAWC,CAAAA,OAAQ,MAAGA,OAAS,EAAC,IAAIL;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2196, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/hot-reloader/pages/websocket.ts"], "sourcesContent": ["import {\n  isTerminalLoggingEnabled,\n  logQueue,\n} from '../../../../next-devtools/userspace/app/forward-logs'\nimport {\n  HMR_ACTIONS_SENT_TO_BROWSER,\n  type HMR_ACTION_TYPES,\n} from '../../../../server/dev/hot-reloader-types'\nimport { getSocketUrl } from '../get-socket-url'\n\nlet source: WebSocket\n\ntype ActionCallback = (action: HMR_ACTION_TYPES) => void\n\nconst eventCallbacks: Array<ActionCallback> = []\n\nexport function addMessageListener(callback: ActionCallback) {\n  eventCallbacks.push(callback)\n}\n\nexport function sendMessage(data: string) {\n  if (!source || source.readyState !== source.OPEN) return\n  return source.send(data)\n}\n\nlet reconnections = 0\nlet reloading = false\nlet serverSessionId: number | null = null\n\nexport function connectHMR(options: { path: string; assetPrefix: string }) {\n  function init() {\n    if (source) source.close()\n\n    function handleOnline() {\n      if (isTerminalLoggingEnabled) {\n        logQueue.onSocketReady(source)\n      }\n      reconnections = 0\n      window.console.log('[HMR] connected')\n    }\n\n    function handleMessage(event: MessageEvent<string>) {\n      // While the page is reloading, don't respond to any more messages.\n      // On reconnect, the server may send an empty list of changes if it was restarted.\n      if (reloading) {\n        return\n      }\n\n      // Coerce into HMR_ACTION_TYPES as that is the format.\n      const msg: HMR_ACTION_TYPES = JSON.parse(event.data)\n\n      if (\n        'action' in msg &&\n        msg.action === HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED\n      ) {\n        if (\n          serverSessionId !== null &&\n          serverSessionId !== msg.data.sessionId\n        ) {\n          // Either the server's session id has changed and it's a new server, or\n          // it's been too long since we disconnected and we should reload the page.\n          // There could be 1) unhandled server errors and/or 2) stale content.\n          // Perform a hard reload of the page.\n          window.location.reload()\n\n          reloading = true\n          return\n        }\n\n        serverSessionId = msg.data.sessionId\n      }\n\n      for (const eventCallback of eventCallbacks) {\n        eventCallback(msg)\n      }\n    }\n\n    let timer: ReturnType<typeof setTimeout>\n    function handleDisconnect() {\n      source.onerror = null\n      source.onclose = null\n      source.close()\n      reconnections++\n      // After 25 reconnects we'll want to reload the page as it indicates the dev server is no longer running.\n      if (reconnections > 25) {\n        reloading = true\n        window.location.reload()\n        return\n      }\n\n      clearTimeout(timer)\n      // Try again after 5 seconds\n      timer = setTimeout(init, reconnections > 5 ? 5000 : 1000)\n    }\n\n    const url = getSocketUrl(options.assetPrefix)\n\n    source = new window.WebSocket(`${url}${options.path}`)\n    source.onopen = handleOnline\n    source.onerror = handleDisconnect\n    source.onclose = handleDisconnect\n    source.onmessage = handleMessage\n  }\n\n  init()\n}\n"], "names": ["addMessageListener", "connectHMR", "sendMessage", "source", "eventCallbacks", "callback", "push", "data", "readyState", "OPEN", "send", "reconnections", "reloading", "serverSessionId", "options", "init", "close", "handleOnline", "isTerminalLoggingEnabled", "logQueue", "onSocketReady", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "TURBOPACK_CONNECTED", "sessionId", "location", "reload", "eventCallback", "timer", "handleDisconnect", "onerror", "onclose", "clearTimeout", "setTimeout", "url", "getSocketUrl", "assetPrefix", "WebSocket", "path", "onopen", "onmessage"], "mappings": ";;;;;;;;;;;;;;;IAgBgBA,kBAAkB,EAAA;eAAlBA;;IAaAC,UAAU,EAAA;eAAVA;;IATAC,WAAW,EAAA;eAAXA;;;6BAjBT;kCAIA;8BACsB;AAE7B,IAAIC;AAIJ,MAAMC,iBAAwC,EAAE;AAEzC,SAASJ,mBAAmBK,QAAwB;IACzDD,eAAeE,IAAI,CAACD;AACtB;AAEO,SAASH,YAAYK,IAAY;IACtC,IAAI,CAACJ,UAAUA,OAAOK,UAAU,KAAKL,OAAOM,IAAI,EAAE;IAClD,OAAON,OAAOO,IAAI,CAACH;AACrB;AAEA,IAAII,gBAAgB;AACpB,IAAIC,YAAY;AAChB,IAAIC,kBAAiC;AAE9B,SAASZ,WAAWa,OAA8C;IACvE,SAASC;QACP,IAAIZ,QAAQA,OAAOa,KAAK;QAExB,SAASC;YACP,IAAIC,aAAAA,wBAAwB,EAAE;gBAC5BC,aAAAA,QAAQ,CAACC,aAAa,CAACjB;YACzB;YACAQ,gBAAgB;YAChBU,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,mEAAmE;YACnE,kFAAkF;YAClF,IAAIb,WAAW;gBACb;YACF;YAEA,sDAAsD;YACtD,MAAMc,MAAwBC,KAAKC,KAAK,CAACH,MAAMlB,IAAI;YAEnD,IACE,YAAYmB,OACZA,IAAIG,MAAM,KAAKC,kBAAAA,2BAA2B,CAACC,mBAAmB,EAC9D;gBACA,IACElB,oBAAoB,QACpBA,oBAAoBa,IAAInB,IAAI,CAACyB,SAAS,EACtC;oBACA,uEAAuE;oBACvE,0EAA0E;oBAC1E,qEAAqE;oBACrE,qCAAqC;oBACrCX,OAAOY,QAAQ,CAACC,MAAM;oBAEtBtB,YAAY;oBACZ;gBACF;gBAEAC,kBAAkBa,IAAInB,IAAI,CAACyB,SAAS;YACtC;YAEA,KAAK,MAAMG,iBAAiB/B,eAAgB;gBAC1C+B,cAAcT;YAChB;QACF;QAEA,IAAIU;QACJ,SAASC;YACPlC,OAAOmC,OAAO,GAAG;YACjBnC,OAAOoC,OAAO,GAAG;YACjBpC,OAAOa,KAAK;YACZL;YACA,yGAAyG;YACzG,IAAIA,gBAAgB,IAAI;gBACtBC,YAAY;gBACZS,OAAOY,QAAQ,CAACC,MAAM;gBACtB;YACF;YAEAM,aAAaJ;YACb,4BAA4B;YAC5BA,QAAQK,WAAW1B,MAAMJ,gBAAgB,IAAI,OAAO;QACtD;QAEA,MAAM+B,MAAMC,CAAAA,GAAAA,cAAAA,YAAY,EAAC7B,QAAQ8B,WAAW;QAE5CzC,SAAS,IAAIkB,OAAOwB,SAAS,CAAE,KAAEH,MAAM5B,QAAQgC,IAAI;QACnD3C,OAAO4C,MAAM,GAAG9B;QAChBd,OAAOmC,OAAO,GAAGD;QACjBlC,OAAOoC,OAAO,GAAGF;QACjBlC,OAAO6C,SAAS,GAAGxB;IACrB;IAEAT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/hot-reloader/shared.ts"], "sourcesContent": ["import type { HMR_ACTION_TYPES } from '../../../server/dev/hot-reloader-types'\n\nexport const REACT_REFRESH_FULL_RELOAD =\n  '[Fast Refresh] performing full reload\\n\\n' +\n  \"Fast Refresh will perform a full reload when you edit a file that's imported by modules outside of the React rendering tree.\\n\" +\n  'You might have a file which exports a React component but also exports a value that is imported by a non-React component file.\\n' +\n  'Consider migrating the non-React component export to a separate file and importing it into both files.\\n\\n' +\n  'It is also possible the parent component of the component you edited is a class component, which disables Fast Refresh.\\n' +\n  'Fast Refresh requires at least one parent function component in your React tree.'\n\nexport const REACT_REFRESH_FULL_RELOAD_FROM_ERROR =\n  '[Fast Refresh] performing full reload because your application had an unrecoverable error'\n\nexport function reportInvalidHmrMessage(\n  message: HMR_ACTION_TYPES | MessageEvent<unknown>,\n  err: unknown\n) {\n  console.warn(\n    '[HMR] Invalid message: ' +\n      JSON.stringify(message) +\n      '\\n' +\n      ((err instanceof Error && err?.stack) || '')\n  )\n}\n"], "names": ["REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "reportInvalidHmrMessage", "message", "err", "console", "warn", "JSON", "stringify", "Error", "stack"], "mappings": ";;;;;;;;;;;;;;;IAEaA,yBAAyB,EAAA;eAAzBA;;IAQAC,oCAAoC,EAAA;eAApCA;;IAGGC,uBAAuB,EAAA;eAAvBA;;;AAXT,MAAMF,4BACX,8CACA,mIACA,qIACA,+GACA,8HACA;AAEK,MAAMC,uCACX;AAEK,SAASC,wBACdC,OAAiD,EACjDC,GAAY;IAEZC,QAAQC,IAAI,CACV,4BACEC,KAAKC,SAAS,CAACL,WACf,OACC,CAACC,eAAeK,SAAAA,CAASL,OAAAA,OAAAA,KAAAA,IAAAA,IAAKM,KAAK,KAAK,EAAC;AAEhD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/runtime-error-handler.ts"], "sourcesContent": ["export const RuntimeErrorHandler = {\n  hadRuntimeError: false,\n}\n"], "names": ["RuntimeError<PERSON>andler", "hadRuntimeError"], "mappings": ";;;+BAAaA,uBAAAA;;;eAAAA;;;AAAN,MAAMA,sBAAsB;IACjCC,iBAAiB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2376, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/report-hmr-latency.ts"], "sourcesContent": ["declare global {\n  interface Window {\n    __NEXT_HMR_LATENCY_CB: ((latencyMs: number) => void) | undefined\n  }\n}\n\n/**\n * Logs information about a completed HMR to the console, the server (via a\n * `client-hmr-latency` event), and to `self.__NEXT_HMR_LATENCY_CB` (a debugging\n * hook).\n *\n * @param hasUpdate Set this to `false` to avoid reporting the HMR event via a\n *   `client-hmr-latency` event or to `self.__NEXT_HMR_LATENCY_CB`. Used by\n *   turbopack when we must report a message to the browser console (because we\n *   already logged a \"rebuilding\" message), but it's not a real HMR, so we\n *   don't want to impact our telemetry.\n */\nexport default function reportHmrLatency(\n  sendMessage: (message: string) => void,\n  updatedModules: ReadonlyArray<string | number>,\n  startMsSinceEpoch: number,\n  endMsSinceEpoch: number,\n  hasUpdate: boolean = true\n) {\n  const latencyMs = endMsSinceEpoch - startMsSinceEpoch\n  console.log(`[Fast Refresh] done in ${latencyMs}ms`)\n  if (!hasUpdate) {\n    return\n  }\n  sendMessage(\n    JSON.stringify({\n      event: 'client-hmr-latency',\n      id: window.__nextDevClientId,\n      startTime: startMsSinceEpoch,\n      endTime: endMsSinceEpoch,\n      page: window.location.pathname,\n      updatedModules,\n      // Whether the page (tab) was hidden at the time the event occurred.\n      // This can impact the accuracy of the event's timing.\n      isPageHidden: document.visibilityState === 'hidden',\n    })\n  )\n  if (self.__NEXT_HMR_LATENCY_CB) {\n    self.__NEXT_HMR_LATENCY_CB(latencyMs)\n  }\n}\n"], "names": ["reportHmrLatency", "sendMessage", "updatedModules", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdate", "latencyMs", "console", "log", "JSON", "stringify", "event", "id", "window", "__nextDevClientId", "startTime", "endTime", "page", "location", "pathname", "isPageHidden", "document", "visibilityState", "self", "__NEXT_HMR_LATENCY_CB"], "mappings": ";;;+BAMA;;;;;;;;;;CAUC,GACD,WAAA;;;eAAwBA;;;AAAT,SAASA,iBACtBC,WAAsC,EACtCC,cAA8C,EAC9CC,iBAAyB,EACzBC,eAAuB,EACvBC,SAAyB;IAAzBA,IAAAA,cAAAA,KAAAA,GAAAA,YAAqB;IAErB,MAAMC,YAAYF,kBAAkBD;IACpCI,QAAQC,GAAG,CAAE,4BAAyBF,YAAU;IAChD,IAAI,CAACD,WAAW;QACd;IACF;IACAJ,YACEQ,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,IAAIC,OAAOC,iBAAiB;QAC5BC,WAAWZ;QACXa,SAASZ;QACTa,MAAMJ,OAAOK,QAAQ,CAACC,QAAQ;QAC9BjB;QACA,oEAAoE;QACpE,sDAAsD;QACtDkB,cAAcC,SAASC,eAAe,KAAK;IAC7C;IAEF,IAAIC,KAAKC,qBAAqB,EAAE;QAC9BD,KAAKC,qBAAqB,CAAClB;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2430, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/hot-reloader/turbopack-hot-reloader-common.ts"], "sourcesContent": ["import type { TurbopackMessageAction } from '../../../server/dev/hot-reloader-types'\nimport type { Update as TurbopackUpdate } from '../../../build/swc/types'\n\ndeclare global {\n  interface Window {\n    __NEXT_HMR_TURBOPACK_REPORT_NOISY_NOOP_EVENTS: boolean | undefined\n  }\n}\n\n// How long to wait before reporting the HMR start, used to suppress irrelevant\n// `BUILDING` events. Does not impact reported latency.\nconst TURBOPACK_HMR_START_DELAY_MS = 100\n\ninterface HmrUpdate {\n  hasUpdates: boolean\n  updatedModules: Set<string>\n  startMsSinceEpoch: number\n  endMsSinceEpoch: number\n}\n\nexport class TurbopackHmr {\n  #updatedModules: Set<string>\n  #startMsSinceEpoch: number | undefined\n  #lastUpdateMsSinceEpoch: number | undefined\n  #deferredReportHmrStartId: ReturnType<typeof setTimeout> | undefined\n\n  constructor() {\n    this.#updatedModules = new Set()\n  }\n\n  // HACK: Turbopack tends to generate a lot of irrelevant \"BUILDING\" actions,\n  // as it reports *any* compilation, including fully no-op/cached compilations\n  // and those unrelated to HMR. Fixing this would require significant\n  // architectural changes.\n  //\n  // Work around this by deferring any \"rebuilding\" message by 100ms. If we get\n  // a BUILT event within that threshold and nothing has changed, just suppress\n  // the message entirely.\n  #runDeferredReportHmrStart() {\n    if (this.#deferredReportHmrStartId != null) {\n      console.log('[Fast Refresh] rebuilding')\n      this.#cancelDeferredReportHmrStart()\n    }\n  }\n\n  #cancelDeferredReportHmrStart() {\n    clearTimeout(this.#deferredReportHmrStartId)\n    this.#deferredReportHmrStartId = undefined\n  }\n\n  onBuilding() {\n    this.#lastUpdateMsSinceEpoch = undefined\n    this.#cancelDeferredReportHmrStart()\n    this.#startMsSinceEpoch = Date.now()\n\n    // report the HMR start after a short delay\n    this.#deferredReportHmrStartId = setTimeout(\n      () => this.#runDeferredReportHmrStart(),\n      // debugging feature: don't defer/suppress noisy no-op HMR update messages\n      self.__NEXT_HMR_TURBOPACK_REPORT_NOISY_NOOP_EVENTS\n        ? 0\n        : TURBOPACK_HMR_START_DELAY_MS\n    )\n  }\n\n  /** Helper for other `onEvent` methods. */\n  #onUpdate() {\n    this.#runDeferredReportHmrStart()\n    this.#lastUpdateMsSinceEpoch = Date.now()\n  }\n\n  onTurbopackMessage(msg: TurbopackMessageAction) {\n    this.#onUpdate()\n    const updatedModules = extractModulesFromTurbopackMessage(msg.data)\n    for (const module of updatedModules) {\n      this.#updatedModules.add(module)\n    }\n  }\n\n  onServerComponentChanges() {\n    this.#onUpdate()\n  }\n\n  onReloadPage() {\n    this.#onUpdate()\n  }\n\n  onPageAddRemove() {\n    this.#onUpdate()\n  }\n\n  /**\n   * @returns `null` if the caller should ignore the update entirely. Returns an\n   *   object with `hasUpdates: false` if the caller should report the end of\n   *   the HMR in the browser console, but the HMR was a no-op.\n   */\n  onBuilt(): HmrUpdate | null {\n    // Check that we got *any* `TurbopackMessageAction`, even if\n    // `updatedModules` is empty (not everything gets recorded there).\n    //\n    // There's also a case where `onBuilt` gets called before `onBuilding`,\n    // which can happen during initial page load. Ignore that too!\n    const hasUpdates =\n      this.#lastUpdateMsSinceEpoch != null && this.#startMsSinceEpoch != null\n    if (!hasUpdates && this.#deferredReportHmrStartId != null) {\n      // suppress the update entirely\n      this.#cancelDeferredReportHmrStart()\n      return null\n    }\n    this.#runDeferredReportHmrStart()\n\n    const result = {\n      hasUpdates,\n      updatedModules: this.#updatedModules,\n      startMsSinceEpoch: this.#startMsSinceEpoch!,\n      endMsSinceEpoch: this.#lastUpdateMsSinceEpoch ?? Date.now(),\n    }\n    this.#updatedModules = new Set()\n    return result\n  }\n}\n\nfunction extractModulesFromTurbopackMessage(\n  data: TurbopackUpdate | TurbopackUpdate[]\n): Set<string> {\n  const updatedModules: Set<string> = new Set()\n\n  const updates = Array.isArray(data) ? data : [data]\n  for (const update of updates) {\n    // TODO this won't capture changes to CSS since they don't result in a \"merged\" update\n    if (\n      update.type !== 'partial' ||\n      update.instruction.type !== 'ChunkListUpdate' ||\n      update.instruction.merged === undefined\n    ) {\n      continue\n    }\n\n    for (const mergedUpdate of update.instruction.merged) {\n      for (const name of Object.keys(mergedUpdate.entries)) {\n        const res = /(.*)\\s+\\[.*/.exec(name)\n        if (res === null) {\n          console.error(\n            '[Turbopack HMR] Expected module to match pattern: ' + name\n          )\n          continue\n        }\n\n        updatedModules.add(res[1])\n      }\n    }\n  }\n\n  return updatedModules\n}\n"], "names": ["TurbopackHmr", "TURBOPACK_HMR_START_DELAY_MS", "onBuilding", "undefined", "Date", "now", "setTimeout", "self", "__NEXT_HMR_TURBOPACK_REPORT_NOISY_NOOP_EVENTS", "onTurbopackMessage", "msg", "updatedModules", "extractModulesFromTurbopackMessage", "data", "module", "add", "onServerComponentChanges", "onReloadPage", "onPageAddRemove", "onBuilt", "hasUpdates", "result", "startMsSinceEpoch", "endMsSinceEpoch", "Set", "constructor", "console", "log", "clearTimeout", "updates", "Array", "isArray", "update", "type", "instruction", "merged", "mergedUpdate", "name", "Object", "keys", "entries", "res", "exec", "error"], "mappings": ";;;+BAoBaA,gBAAAA;;;eAAAA;;;;;AAXb,+EAA+E;AAC/E,uDAAuD;AACvD,MAAMC,+BAA+B;IAUnC,kBAAA,WAAA,GAAA,+BAAA,CAAA,CAAA,oBACA,qBAAA,WAAA,GAAA,+BAAA,CAAA,CAAA,uBACA,0BAAA,WAAA,GAAA,+BAAA,CAAA,CAAA,4BACA,4BAAA,WAAA,GAAA,+BAAA,CAAA,CAAA,8BAMA,AACA,4EAD4E,CACC;AAC7E,oEAAoE;AACpE,yBAAyB;AACzB,EAAE;AACF,6EAA6E;AAC7E,6EAA6E;AAC7E,wBAAwB;AACxB,6BAAA,WAAA,GAAA,+BAAA,CAAA,CAAA,+BAOA,gCAAA,WAAA,GAAA,+BAAA,CAAA,CAAA,kCAoBA,wCAAwC,GACxC,YAAA,WAAA,GAAA,+BAAA,CAAA,CAAA;AA9CK,MAAMD;IA8BXE,aAAa;QACX,gCAAA,CAAA,CAAA,IAAI,EAAC,wBAAA,CAAA,wBAAA,GAA0BC;QAC/B,gCAAA,CAAA,CAAA,IAAI,EAAC,8BAAA,CAAA,8BAAA;QACL,gCAAA,CAAA,CAAA,IAAI,EAAC,mBAAA,CAAA,mBAAA,GAAqBC,KAAKC,GAAG;QAElC,2CAA2C;QAC3C,gCAAA,CAAA,CAAA,IAAI,EAAC,0BAAA,CAAA,0BAAA,GAA4BC,WAC/B,IAAM,gCAAA,CAAA,CAAA,IAAI,EAAC,2BAAA,CAAA,2BAAA,IACX,AACAC,KAAKC,6CAA6C,GAC9C,IACAP,iBAHsE;IAK9E;IAQAQ,mBAAmBC,GAA2B,EAAE;QAC9C,gCAAA,CAAA,CAAA,IAAI,EAAC,UAAA,CAAA,UAAA;QACL,MAAMC,iBAAiBC,mCAAmCF,IAAIG,IAAI;QAClE,KAAK,MAAMC,WAAUH,eAAgB;YACnC,gCAAA,CAAA,CAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,CAAgBI,GAAG,CAACD;QAC3B;IACF;IAEAE,2BAA2B;QACzB,gCAAA,CAAA,CAAA,IAAI,EAAC,UAAA,CAAA,UAAA;IACP;IAEAC,eAAe;QACb,gCAAA,CAAA,CAAA,IAAI,EAAC,UAAA,CAAA,UAAA;IACP;IAEAC,kBAAkB;QAChB,gCAAA,CAAA,CAAA,IAAI,EAAC,UAAA,CAAA,UAAA;IACP;IAEA;;;;GAIC,GACDC,UAA4B;QAC1B,4DAA4D;QAC5D,kEAAkE;QAClE,EAAE;QACF,uEAAuE;QACvE,8DAA8D;QAC9D,MAAMC,aACJ,gCAAA,CAAA,CAAA,IAAI,EAAC,wBAAA,CAAA,wBAAA,IAA2B,QAAQ,gCAAA,CAAA,CAAA,IAAI,EAAC,mBAAA,CAAA,mBAAA,IAAsB;QACrE,IAAI,CAACA,cAAc,gCAAA,CAAA,CAAA,IAAI,EAAC,0BAAA,CAAA,0BAAA,IAA6B,MAAM;YACzD,+BAA+B;YAC/B,gCAAA,CAAA,CAAA,IAAI,EAAC,8BAAA,CAAA,8BAAA;YACL,OAAO;QACT;QACA,gCAAA,CAAA,CAAA,IAAI,EAAC,2BAAA,CAAA,2BAAA;;QAEL,MAAMC,SAAS;YACbD;YACAT,cAAc,EAAE,gCAAA,CAAA,CAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA;YACrBW,iBAAiB,EAAE,gCAAA,CAAA,CAAA,IAAI,EAAC,mBAAA,CAAA,mBAAA;YACxBC,iBAAiB,CAAA,0DAAA,gCAAA,CAAA,CAAA,IAAI,EAAC,wBAAA,CAAA,wBAAA,KAAA,OAAA,0DAA2BnB,KAAKC,GAAG;QAC3D;QACA,gCAAA,CAAA,CAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,GAAkB,IAAImB;QAC3B,OAAOH;IACT;IA7FAI,aAAc;QAYd,OAAA,cAAA,CAAA,IAAA,EAAA,4BAAA;mBAAA;;QAOA,OAAA,cAAA,CAAA,IAAA,EAAA,+BAAA;mBAAA;;QAqBA,OAAA,cAAA,CAAA,IAAA,EAAA,WAAA;mBAAA;;QA7CA,OAAA,cAAA,CAAA,IAAA,EAAA,iBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,oBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,yBAAA;;mBAAA,KAAA;;QACA,OAAA,cAAA,CAAA,IAAA,EAAA,2BAAA;;mBAAA,KAAA;;QAGE,gCAAA,CAAA,CAAA,IAAI,EAAC,gBAAA,CAAA,gBAAA,GAAkB,IAAID;IAC7B;AA4FF;AAlFE,SAAA;IACE,IAAI,gCAAA,CAAA,CAAA,IAAI,EAAC,0BAAA,CAAA,0BAAA,IAA6B,MAAM;QAC1CE,QAAQC,GAAG,CAAC;QACZ,gCAAA,CAAA,CAAA,IAAI,EAAC,8BAAA,CAAA,8BAAA;IACP;AACF;AAEA,SAAA;IACEC,aAAa,gCAAA,CAAA,CAAA,IAAI,EAAC,0BAAA,CAAA,0BAAA;IAClB,gCAAA,CAAA,CAAA,IAAI,EAAC,0BAAA,CAAA,0BAAA,GAA4BzB;AACnC;AAkBA,SAAA;IACE,gCAAA,CAAA,CAAA,IAAI,EAAC,2BAAA,CAAA,2BAAA;IACL,gCAAA,CAAA,CAAA,IAAI,EAAC,wBAAA,CAAA,wBAAA,GAA0BC,KAAKC,GAAG;AACzC;AAqDF,SAASO,mCACPC,IAAyC;IAEzC,MAAMF,iBAA8B,IAAIa;IAExC,MAAMK,UAAUC,MAAMC,OAAO,CAAClB,QAAQA,OAAO;QAACA;KAAK;IACnD,KAAK,MAAMmB,UAAUH,QAAS;QAC5B,sFAAsF;QACtF,IACEG,OAAOC,IAAI,KAAK,aAChBD,OAAOE,WAAW,CAACD,IAAI,KAAK,qBAC5BD,OAAOE,WAAW,CAACC,MAAM,KAAKhC,WAC9B;YACA;QACF;QAEA,KAAK,MAAMiC,gBAAgBJ,OAAOE,WAAW,CAACC,MAAM,CAAE;YACpD,KAAK,MAAME,QAAQC,OAAOC,IAAI,CAACH,aAAaI,OAAO,EAAG;gBACpD,MAAMC,MAAM,cAAcC,IAAI,CAACL;gBAC/B,IAAII,QAAQ,MAAM;oBAChBf,QAAQiB,KAAK,CACX,uDAAuDN;oBAEzD;gBACF;gBAEA1B,eAAeI,GAAG,CAAC0B,GAAG,CAAC,EAAE;YAC3B;QACF;IACF;IAEA,OAAO9B;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2582, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/hot-reloader/pages/hot-reloader-pages.ts"], "sourcesContent": ["// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */\n/**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n/// <reference types=\"webpack/module.d.ts\" />\n\n// This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\n/// <reference types=\"webpack/module.d.ts\" />\n\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { register } from '../../../../next-devtools/userspace/pages/pages-dev-overlay-setup'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { addMessageListener, sendMessage } from './websocket'\nimport formatWebpackMessages from '../../../../shared/lib/format-webpack-messages'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport {\n  REACT_REFRESH_FULL_RELOAD,\n  REACT_REFRESH_FULL_RELOAD_FROM_ERROR,\n  reportInvalidHmrMessage,\n} from '../shared'\nimport { RuntimeErrorHandler } from '../../runtime-error-handler'\nimport reportHmrLatency from '../../report-hmr-latency'\nimport { TurbopackHmr } from '../turbopack-hot-reloader-common'\n\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\n\ndeclare global {\n  interface Window {\n    __nextDevClientId: number\n  }\n}\n\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now())\n\nlet customHmrEventHandler: any\nlet turbopackMessageListeners: ((msg: TurbopackMsgToBrowser) => void)[] = []\nexport default function connect() {\n  register()\n\n  addMessageListener((payload) => {\n    if (!('action' in payload)) {\n      return\n    }\n\n    try {\n      processMessage(payload)\n    } catch (err: unknown) {\n      reportInvalidHmrMessage(payload, err)\n    }\n  })\n\n  return {\n    subscribeToHmrEvent(handler: any) {\n      customHmrEventHandler = handler\n    },\n    onUnrecoverableError() {\n      RuntimeErrorHandler.hadRuntimeError = true\n    },\n    addTurbopackMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n      turbopackMessageListeners.push(cb)\n    },\n    sendTurbopackMessage(msg: string) {\n      sendMessage(msg)\n    },\n    handleUpdateError(err: unknown) {\n      performFullReload(err)\n    },\n  }\n}\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true\nvar mostRecentCompilationHash: string | null = null\nvar hasCompileErrors = false\n\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear()\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors()\n  hasCompileErrors = false\n\n  if (process.env.TURBOPACK) {\n    const hmrUpdate = turbopackHmr!.onBuilt()\n    if (hmrUpdate != null) {\n      reportHmrLatency(\n        sendMessage,\n        [...hmrUpdate.updatedModules],\n        hmrUpdate.startMsSinceEpoch,\n        hmrUpdate.endMsSinceEpoch,\n        hmrUpdate.hasUpdates\n      )\n    }\n    dispatcher.onBuildOk()\n  } else {\n    const isHotUpdate =\n      !isFirstCompilation ||\n      (window.__NEXT_DATA__.page !== '/_error' && isUpdateAvailable())\n\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n      tryApplyUpdatesWebpack()\n    }\n  }\n\n  isFirstCompilation = false\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings: any) {\n  clearOutdatedErrors()\n\n  const isHotUpdate = !isFirstCompilation\n  isFirstCompilation = false\n  hasCompileErrors = false\n\n  function printWarnings() {\n    // Print warnings to the console.\n    const formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: [],\n    })\n\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (let i = 0; i < formatted.warnings?.length; i++) {\n        if (i === 5) {\n          console.warn(\n            'There were more warnings in other files.\\n' +\n              'You can find a complete log in the terminal.'\n          )\n          break\n        }\n        console.warn(stripAnsi(formatted.warnings[i]))\n      }\n    }\n  }\n\n  printWarnings()\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdatesWebpack()\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors: any) {\n  clearOutdatedErrors()\n\n  isFirstCompilation = false\n  hasCompileErrors = true\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: [],\n  })\n\n  // Only show the first error.\n\n  dispatcher.onBuildError(formatted.errors[0])\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (self.__NEXT_HMR_CB) {\n      self.__NEXT_HMR_CB(formatted.errors[0])\n      self.__NEXT_HMR_CB = null\n    }\n  }\n}\n\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\nlet isrManifest: Record<string, boolean> = {}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\nexport function handleStaticIndicator() {\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    const routeInfo = window.next.router.components[window.next.router.pathname]\n    const pageComponent = routeInfo?.Component\n    const appComponent = window.next.router.components['/_app']?.Component\n    const isDynamicPage =\n      Boolean(pageComponent?.getInitialProps) || Boolean(routeInfo?.__N_SSP)\n    const hasAppGetInitialProps =\n      Boolean(appComponent?.getInitialProps) &&\n      appComponent?.getInitialProps !== appComponent?.origGetInitialProps\n\n    const isPageStatic =\n      window.location.pathname in isrManifest ||\n      (!isDynamicPage && !hasAppGetInitialProps)\n\n    dispatcher.onStaticIndicator(isPageStatic)\n  }\n}\n\n/** Handles messages from the server for the Pages Router. */\nfunction processMessage(obj: HMR_ACTION_TYPES) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      isrManifest = obj.data\n      handleStaticIndicator()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      dispatcher.buildingIndicatorShow()\n\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      dispatcher.buildingIndicatorHide()\n\n      if (obj.hash) handleAvailableHash(obj.hash)\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) dispatcher.onVersionInfo(obj.versionInfo)\n      if ('devIndicator' in obj) dispatcher.onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleErrors(errors)\n      }\n\n      // NOTE: Turbopack does not currently send warnings\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleWarnings(warnings)\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: window.__nextDevClientId,\n        })\n      )\n      return handleSuccess()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      if (hasCompileErrors || RuntimeErrorHandler.hadRuntimeError) {\n        window.location.reload()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: obj.data,\n        })\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      dispatcher.onBeforeRefresh()\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: obj.data,\n        })\n      }\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null)\n      }\n      dispatcher.onRefresh()\n      break\n    }\n    default: {\n      if (customHmrEventHandler) {\n        customHmrEventHandler(obj)\n        break\n      }\n      break\n    }\n  }\n}\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: () => void) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: string) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack() {\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in Webpack configuration.\n    console.error('HotModuleReplacementPlugin is not in Webpack configuration.')\n    // window.location.reload();\n    return\n  }\n\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    dispatcher.onBuildOk()\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err)\n      return\n    }\n\n    dispatcher.onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack()\n      return\n    }\n\n    dispatcher.onRefresh()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      dispatcher.onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\nexport function performFullReload(err: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  window.location.reload()\n}\n"], "names": ["connect", "handleStaticIndicator", "performFullReload", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "register", "addMessageListener", "payload", "processMessage", "err", "reportInvalidHmrMessage", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "RuntimeError<PERSON>andler", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "sendMessage", "handleUpdateError", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "console", "clear", "handleSuccess", "process", "env", "TURBOPACK", "hmrUpdate", "turbopackHmr", "onBuilt", "reportHmrLatency", "updatedModules", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "dispatcher", "onBuildOk", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdatesWebpack", "handleWarnings", "warnings", "printWarnings", "formatted", "formatWebpackMessages", "errors", "warn", "i", "length", "stripAnsi", "handleErrors", "onBuildError", "error", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "webpackStartMsSinceEpoch", "TurbopackHmr", "isrManifest", "handleAvailableHash", "hash", "__NEXT_DEV_INDICATOR", "routeInfo", "next", "router", "components", "pathname", "pageComponent", "Component", "appComponent", "isDynamicPage", "Boolean", "getInitialProps", "__N_SSP", "hasAppGetInitialProps", "origGetInitialProps", "isPageStatic", "location", "onStaticIndicator", "obj", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "data", "BUILDING", "buildingIndicatorShow", "onBuilding", "log", "BUILT", "SYNC", "buildingIndicatorHide", "onVersionInfo", "versionInfo", "onDevIndicator", "devIndicator", "hasErrors", "JSON", "stringify", "event", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "reload", "SERVER_ERROR", "errorJSON", "message", "stack", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "onTurbopackMessage", "onBeforeRefresh", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "onRefresh", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "handleApplyUpdates", "REACT_REFRESH_FULL_RELOAD", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n", "undefined"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,6CAA6C;AAE7C,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;AAE3G,6CAA6C;AA6FvCqC,QAAQC,GAAG,CAACC,SAAS;;;;;;;;;;;;;;;;;IArD3B,OAgCC,EAAA;eAhCuBvC;;IAkKRC,qBAAqB,EAAA;eAArBA;;IAoPAC,iBAAiB,EAAA;eAAjBA;;;;8BA5bW;sCACF;oEACH;2BAC0B;gFACd;kCACU;wBASrC;qCAC6B;2EACP;4CACA;AAiB7BC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC7D,SAASX;IACtBY,CAAAA,GAAAA,sBAAAA,QAAQ;IAERC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,CAACC;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAc;YACrBC,CAAAA,GAAAA,QAAAA,uBAAuB,EAACH,SAASE;QACnC;IACF;IAEA,OAAO;QACLE,qBAAoBC,OAAY;YAC9BT,wBAAwBS;QAC1B;QACAC;YACEC,qBAAAA,mBAAmB,CAACC,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEb,0BAA0Bc,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9BC,CAAAA,GAAAA,WAAAA,WAAW,EAACD;QACd;QACAE,mBAAkBb,GAAY;YAC5Bd,kBAAkBc;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIc,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOC,YAAY,eAAe,OAAOA,QAAQC,KAAK,KAAK,YAAY;QACzE,IAAIH,kBAAkB;YACpBE,QAAQC,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPH;IACAD,mBAAmB;IAEnB,wCAA2B;QACzB,MAAMQ,YAAYC,aAAcC,OAAO;QACvC,IAAIF,aAAa,MAAM;YACrBG,CAAAA,GAAAA,kBAAAA,OAAgB,EACdf,WAAAA,WAAW,EACX;mBAAIY,UAAUI,cAAc;aAAC,EAC7BJ,UAAUK,iBAAiB,EAC3BL,UAAUM,eAAe,EACzBN,UAAUO,UAAU;QAExB;QACAC,cAAAA,UAAU,CAACC,SAAS;IACtB,OAAO;;IAWPnB,qBAAqB;AACvB;AAEA,2CAA2C;AAC3C,SAASyB,eAAeC,QAAa;IACnCvB;IAEA,MAAMiB,cAAc,CAACpB;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASyB;QACP,iCAAiC;QACjC,MAAMC,YAAYC,CAAAA,GAAAA,uBAAAA,OAAqB,EAAC;YACtCH,UAAUA;YACVI,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAO1B,YAAY,eAAe,OAAOA,QAAQ2B,IAAI,KAAK,YAAY;gBACpDH;YAApB,IAAK,IAAII,IAAI,GAAGA,IAAAA,CAAAA,CAAIJ,sBAAAA,UAAUF,QAAQ,KAAA,OAAA,KAAA,IAAlBE,oBAAoBK,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACX5B,QAAQ2B,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACA3B,QAAQ2B,IAAI,CAACG,CAAAA,GAAAA,WAAAA,OAAS,EAACN,UAAUF,QAAQ,CAACM,EAAE;YAC9C;QACF;IACF;IAEAL;IAEA,0CAA0C;IAC1C,IAAIP,aAAa;QACfI;IACF;AACF;AAEA,kEAAkE;AAClE,SAASW,aAAaL,MAAW;IAC/B3B;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAI0B,YAAYC,CAAAA,GAAAA,uBAAAA,OAAqB,EAAC;QACpCC,QAAQA;QACRJ,UAAU,EAAE;IACd;IAEA,6BAA6B;IAE7BR,cAAAA,UAAU,CAACkB,YAAY,CAACR,UAAUE,MAAM,CAAC,EAAE;IAE3C,gCAAgC;IAChC,IAAI,OAAO1B,YAAY,eAAe,OAAOA,QAAQiC,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIL,IAAI,GAAGA,IAAIJ,UAAUE,MAAM,CAACG,MAAM,EAAED,IAAK;YAChD5B,QAAQiC,KAAK,CAACH,CAAAA,GAAAA,WAAAA,OAAS,EAACN,UAAUE,MAAM,CAACE,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAIzB,QAAQC,GAAG,CAAC8B,gBAAgB,EAAE;;AAMpC;AAEA,IAAIG,2BAA0C;AAC9C,MAAM9B,eAAoCJ,QAAQC,GAAG,CAACC,SAAS,kBAC3D,IAAIiC,4BAAAA,YAAY,KAChB;AACJ,IAAIC,cAAuC,CAAC;AAE5C,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtC5C,4BAA4B4C;AAC9B;AAEO,SAAS1E;IACd,IAAIoC,QAAQC,GAAG,CAACsC,oBAAoB,IAAE;YAGfzE;QAFrB,MAAM0E,YAAY1E,OAAO2E,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC7E,OAAO2E,IAAI,CAACC,MAAM,CAACE,QAAQ,CAAC;QAC5E,MAAMC,gBAAgBL,aAAAA,OAAAA,KAAAA,IAAAA,UAAWM,SAAS;QAC1C,MAAMC,eAAAA,CAAejF,sCAAAA,OAAO2E,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,QAAQ,KAAA,OAAA,KAAA,IAAtC7E,oCAAwCgF,SAAS;QACtE,MAAME,gBACJC,QAAQJ,iBAAAA,OAAAA,KAAAA,IAAAA,cAAeK,eAAe,KAAKD,QAAQT,aAAAA,OAAAA,KAAAA,IAAAA,UAAWW,OAAO;QACvE,MAAMC,wBACJH,QAAQF,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcG,eAAe,KACrCH,CAAAA,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcG,eAAe,MAAA,CAAKH,gBAAAA,OAAAA,KAAAA,IAAAA,aAAcM,mBAAmB;QAErE,MAAMC,eACJxF,OAAOyF,QAAQ,CAACX,QAAQ,IAAIR,eAC3B,CAACY,iBAAiB,CAACI;QAEtBzC,cAAAA,UAAU,CAAC6C,iBAAiB,CAACF;IAC/B;AACF;AAEA,2DAA2D,GAC3D,SAAS5E,eAAe+E,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,OAAQA,IAAIC,MAAM;QAChB,KAAKC,kBAAAA,2BAA2B,CAACC,YAAY;YAAE;gBAC7CxB,cAAcqB,IAAII,IAAI;gBACtBjG;gBACA;YACF;QACA,KAAK+F,kBAAAA,2BAA2B,CAACG,QAAQ;YAAE;gBACzCnD,cAAAA,UAAU,CAACoD,qBAAqB;gBAEhC,IAAI/D,QAAQC,GAAG,CAACC,SAAS,eAAE;oBACzBE,aAAc4D,UAAU;gBAC1B,OAAO;;gBAIP;YACF;QACA,KAAKL,kBAAAA,2BAA2B,CAACO,KAAK;QACtC,KAAKP,kBAAAA,2BAA2B,CAACQ,IAAI;YAAE;gBACrCxD,cAAAA,UAAU,CAACyD,qBAAqB;gBAEhC,IAAIX,IAAInB,IAAI,EAAED,oBAAoBoB,IAAInB,IAAI;gBAE1C,MAAM,EAAEf,MAAM,EAAEJ,QAAQ,EAAE,GAAGsC;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAK9C,cAAAA,UAAU,CAAC0D,aAAa,CAACZ,IAAIa,WAAW;gBAClE,IAAI,kBAAkBb,KAAK9C,cAAAA,UAAU,CAAC4D,cAAc,CAACd,IAAIe,YAAY;gBAErE,MAAMC,YAAYxB,QAAQ1B,UAAUA,OAAOG,MAAM;gBACjD,IAAI+C,WAAW;oBACblF,CAAAA,GAAAA,WAAAA,WAAW,EACTmF,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,YAAYtD,OAAOG,MAAM;wBACzBoD,UAAUhH,OAAOC,iBAAiB;oBACpC;oBAEF,OAAO6D,aAAaL;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMwD,cAAc9B,QAAQ9B,YAAYA,SAASO,MAAM;gBACvD,IAAIqD,aAAa;oBACfxF,CAAAA,GAAAA,WAAAA,WAAW,EACTmF,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPI,cAAc7D,SAASO,MAAM;wBAC7BoD,UAAUhH,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOmD,eAAeC;gBACxB;gBAEA5B,CAAAA,GAAAA,WAAAA,WAAW,EACTmF,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPE,UAAUhH,OAAOC,iBAAiB;gBACpC;gBAEF,OAAOgC;YACT;QACA,KAAK4D,kBAAAA,2BAA2B,CAACsB,wBAAwB;YAAE;gBACzD7E,gBAAAA,OAAAA,KAAAA,IAAAA,aAAc8E,wBAAwB;gBACtC,IAAIvF,oBAAoBX,qBAAAA,mBAAmB,CAACC,eAAe,EAAE;oBAC3DnB,OAAOyF,QAAQ,CAAC4B,MAAM;gBACxB;gBACA;YACF;QACA,KAAKxB,kBAAAA,2BAA2B,CAACyB,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAG5B;gBACtB,IAAI4B,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE,GAAGb,KAAKc,KAAK,CAACH;oBACtC,MAAMvD,QAAQ,OAAA,cAAkB,CAAlB,IAAI2D,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BxD,MAAMyD,KAAK,GAAGA;oBACd3D,aAAa;wBAACE;qBAAM;gBACtB;gBACA;YACF;QACA,KAAK6B,kBAAAA,2BAA2B,CAAC+B,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAYrH,0BAA2B;oBAChDqH,SAAS;wBACPC,MAAMjC,kBAAAA,2BAA2B,CAAC+B,mBAAmB;wBACrD7B,MAAMJ,IAAII,IAAI;oBAChB;gBACF;gBACA;YACF;QACA,KAAKF,kBAAAA,2BAA2B,CAACkC,iBAAiB;YAAE;gBAClDzF,aAAc0F,kBAAkB,CAACrC;gBACjC9C,cAAAA,UAAU,CAACoF,eAAe;gBAC1B,KAAK,MAAMJ,YAAYrH,0BAA2B;oBAChDqH,SAAS;wBACPC,MAAMjC,kBAAAA,2BAA2B,CAACkC,iBAAiB;wBACnDhC,MAAMJ,IAAII,IAAI;oBAChB;gBACF;gBACA,IAAI7E,qBAAAA,mBAAmB,CAACC,eAAe,EAAE;oBACvCY,QAAQ2B,IAAI,CAACwE,QAAAA,oCAAoC;oBACjDnI,kBAAkB;gBACpB;gBACA8C,cAAAA,UAAU,CAACsF,SAAS;gBACpB;YACF;QACA;YAAS;gBACP,IAAI5H,uBAAuB;oBACzBA,sBAAsBoF;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAASzC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOtB,8BAA8BwG;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAAS1H,QAAQwH,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACI,mBAAmB,CAAC3H;gBAC/B0H;YACF;QACF;QACAJ,OAAOC,GAAG,CAACK,gBAAgB,CAAC5H;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAASmC;IACP,IAAI,CAACmF,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9DxG,QAAQiC,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAACd,uBAAuB,CAACmF,mBAAmB;QAC9CxF,cAAAA,UAAU,CAACC,SAAS;QACpB;IACF;IAEA,SAAS+F,mBACPhI,GAAQ,EACR4B,cAA0C;QAE1C,IAAI5B,OAAOK,qBAAAA,mBAAmB,CAACC,eAAe,IAAIsB,kBAAkB,MAAM;YACxE,IAAI5B,KAAK;gBACPkB,QAAQ2B,IAAI,CAACoF,QAAAA,yBAAyB;YACxC,OAAO,IAAI5H,qBAAAA,mBAAmB,CAACC,eAAe,EAAE;gBAC9CY,QAAQ2B,IAAI,CAACwE,QAAAA,oCAAoC;YACnD;YACAnI,kBAAkBc;YAClB;QACF;QAEAgC,cAAAA,UAAU,CAACC,SAAS;QAEpB,IAAII,qBAAqB;YACvB,+DAA+D;YAC/DC;YACA;QACF;QAEAN,cAAAA,UAAU,CAACsF,SAAS;QACpB3F,CAAAA,GAAAA,kBAAAA,OAAgB,EACdf,WAAAA,WAAW,EACXgB,gBACA2B,0BACA/D,KAAKC,GAAG;QAGV,IAAI4B,QAAQC,GAAG,CAAC8B,gBAAgB,EAAE;;IAQpC;IAEA,2DAA2D;IAC3DqE,OAAOC,GAAG,CACPQ,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACvG;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGI,cAAAA,UAAU,CAACoF,eAAe;QAC1B,2DAA2D;QAC3D,OAAOK,OAAOC,GAAG,CAACU,KAAK;IACzB,GACCD,IAAI,CACH,CAACvG;QACCoG,mBAAmB,MAAMpG;IAC3B,GACA,CAAC5B;QACCgI,mBAAmBhI,KAAK;IAC1B;AAEN;AAEO,SAASd,kBAAkBc,GAAQ;IACxC,MAAMqI,aACJrI,OACC,CAACA,IAAI4G,KAAK,IAAI5G,IAAI4G,KAAK,CAAC0B,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDxI,IAAI2G,OAAO,IACX3G,MAAM,EAAC;IAEXY,CAAAA,GAAAA,WAAAA,WAAW,EACTmF,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPoC;QACA/H,iBAAiB,CAAC,CAACD,qBAAAA,mBAAmB,CAACC,eAAe;QACtDmI,iBAAiBzI,MAAMA,IAAIyI,eAAe,GAAGC;IAC/C;IAGFvJ,OAAOyF,QAAQ,CAAC4B,MAAM;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2993, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/tracing/report-to-socket.ts"], "sourcesContent": ["import { sendMessage } from '../dev/hot-reloader/pages/websocket'\nimport type { Span } from './tracer'\n\nexport default function reportToSocket(span: Span) {\n  if (span.state.state !== 'ended') {\n    throw new Error('Expected span to be ended')\n  }\n\n  sendMessage(\n    JSON.stringify({\n      event: 'span-end',\n      startTime: span.startTime,\n      endTime: span.state.endTime,\n      spanName: span.name,\n      attributes: span.attributes,\n    })\n  )\n}\n"], "names": ["reportToSocket", "span", "state", "Error", "sendMessage", "JSON", "stringify", "event", "startTime", "endTime", "spanName", "name", "attributes"], "mappings": ";;;+BAGA,WAAA;;;eAAwBA;;;2BAHI;AAGb,SAASA,eAAeC,IAAU;IAC/C,IAAIA,KAAKC,KAAK,CAACA,KAAK,KAAK,SAAS;QAChC,MAAM,OAAA,cAAsC,CAAtC,IAAIC,MAAM,8BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqC;IAC7C;IAEAC,CAAAA,GAAAA,WAAAA,WAAW,EACTC,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPC,WAAWP,KAAKO,SAAS;QACzBC,SAASR,KAAKC,KAAK,CAACO,OAAO;QAC3BC,UAAUT,KAAKU,IAAI;QACnBC,YAAYX,KAAKW,UAAU;IAC7B;AAEJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3032, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/index.tsx"], "sourcesContent": ["/* global location */\n// imports polyfill from `@next/polyfill-module` after build.\nimport '../build/polyfills/polyfill-module'\nimport type Router from '../shared/lib/router/router'\nimport type {\n  AppComponent,\n  AppProps,\n  PrivateRouteInfo,\n} from '../shared/lib/router/router'\n\nimport React, { type JSX } from 'react'\nimport ReactDOM from 'react-dom/client'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport mitt from '../shared/lib/mitt'\nimport type { MittEmitter } from '../shared/lib/mitt'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport { disableSmoothScrollDuringRouteTransition } from '../shared/lib/router/utils/disable-smooth-scroll'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  urlQueryToSearchParams,\n  assign,\n} from '../shared/lib/router/utils/querystring'\nimport { setConfig } from '../shared/lib/runtime-config.external'\nimport { getURL, loadGetInitialProps, ST } from '../shared/lib/utils'\nimport type { NextWebVitalsMetric, NEXT_DATA } from '../shared/lib/utils'\nimport { Portal } from './portal'\nimport initHeadManager from './head-manager'\nimport PageLoader from './page-loader'\nimport type { StyleSheetTuple } from './page-loader'\nimport { RouteAnnouncer } from './route-announcer'\nimport { createRouter, makePublicRouterInstance } from './router'\nimport { getProperError } from '../lib/is-error'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { removeBasePath } from './remove-base-path'\nimport { hasBasePath } from './has-base-path'\nimport { AppRouterContext } from '../shared/lib/app-router-context.shared-runtime'\nimport {\n  adaptForAppRouterInstance,\n  adaptForPathParams,\n  adaptForSearchParams,\n  PathnameContextProviderAdapter,\n} from '../shared/lib/router/adapters'\nimport {\n  SearchParamsContext,\n  PathParamsContext,\n} from '../shared/lib/hooks-client-context.shared-runtime'\nimport { onRecoverableError } from './react-client-callbacks/on-recoverable-error'\nimport tracer from './tracing/tracer'\nimport { isNextRouterError } from './components/is-next-router-error'\n\n/// <reference types=\"react-dom/experimental\" />\n\ndeclare global {\n  interface Window {\n    /* test fns */\n    __NEXT_HYDRATED?: boolean\n    __NEXT_HYDRATED_AT?: number\n    __NEXT_HYDRATED_CB?: () => void\n\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n    __NEXT_P: any[]\n  }\n}\ntype RenderRouteInfo = PrivateRouteInfo & {\n  App: AppComponent\n  scroll?: { x: number; y: number } | null\n  isHydratePass?: boolean\n}\ntype RenderErrorProps = Omit<RenderRouteInfo, 'Component' | 'styleSheets'>\ntype RegisterFn = (input: [string, () => void]) => void\n\nexport const version = process.env.__NEXT_VERSION\nexport let router: Router\nexport const emitter: MittEmitter<string> = mitt()\n\nconst looseToArray = <T extends {}>(input: any): T[] => [].slice.call(input)\n\nlet initialData: NEXT_DATA\nlet defaultLocale: string | undefined = undefined\nlet asPath: string\nlet pageLoader: PageLoader\nlet appElement: HTMLElement | null\nlet headManager: {\n  mountedInstances: Set<unknown>\n  updateHead: (head: JSX.Element[]) => void\n  getIsSsr?: () => boolean\n}\nlet initialMatchesMiddleware = false\nlet lastAppProps: AppProps\n\nlet lastRenderReject: (() => void) | null\nlet devClient: any\n\nlet CachedApp: AppComponent, onPerfEntry: (metric: any) => void\nlet CachedComponent: React.ComponentType\n\nclass Container extends React.Component<{\n  children?: React.ReactNode\n  fn: (err: Error, info?: any) => void\n}> {\n  componentDidCatch(componentErr: Error, info: any) {\n    this.props.fn(componentErr, info)\n  }\n\n  componentDidMount() {\n    this.scrollToHash()\n\n    // We need to replace the router state if:\n    // - the page was (auto) exported and has a query string or search (hash)\n    // - it was auto exported and is a dynamic route (to provide params)\n    // - if it is a client-side skeleton (fallback render)\n    // - if middleware matches the current page (may have rewrite params)\n    // - if rewrites in next.config.js match (may have rewrite params)\n    if (\n      router.isSsr &&\n      (initialData.isFallback ||\n        (initialData.nextExport &&\n          (isDynamicRoute(router.pathname) ||\n            location.search ||\n            process.env.__NEXT_HAS_REWRITES ||\n            initialMatchesMiddleware)) ||\n        (initialData.props &&\n          initialData.props.__N_SSG &&\n          (location.search ||\n            process.env.__NEXT_HAS_REWRITES ||\n            initialMatchesMiddleware)))\n    ) {\n      // update query on mount for exported pages\n      router\n        .replace(\n          router.pathname +\n            '?' +\n            String(\n              assign(\n                urlQueryToSearchParams(router.query),\n                new URLSearchParams(location.search)\n              )\n            ),\n          asPath,\n          {\n            // @ts-ignore\n            // WARNING: `_h` is an internal option for handing Next.js\n            // client-side hydration. Your app should _never_ use this property.\n            // It may change at any time without notice.\n            _h: 1,\n            // Fallback pages must trigger the data fetch, so the transition is\n            // not shallow.\n            // Other pages (strictly updating query) happens shallowly, as data\n            // requirements would already be present.\n            shallow: !initialData.isFallback && !initialMatchesMiddleware,\n          }\n        )\n        .catch((err) => {\n          if (!err.cancelled) throw err\n        })\n    }\n  }\n\n  componentDidUpdate() {\n    this.scrollToHash()\n  }\n\n  scrollToHash() {\n    let { hash } = location\n    hash = hash && hash.substring(1)\n    if (!hash) return\n\n    const el: HTMLElement | null = document.getElementById(hash)\n    if (!el) return\n\n    // If we call scrollIntoView() in here without a setTimeout\n    // it won't scroll properly.\n    setTimeout(() => el.scrollIntoView(), 0)\n  }\n\n  render() {\n    if (process.env.NODE_ENV === 'production') {\n      return this.props.children\n    } else {\n      const { PagesDevOverlayBridge } =\n        require('../next-devtools/userspace/pages/pages-dev-overlay-setup') as typeof import('../next-devtools/userspace/pages/pages-dev-overlay-setup')\n      return (\n        <PagesDevOverlayBridge>{this.props.children}</PagesDevOverlayBridge>\n      )\n    }\n  }\n}\n\nexport async function initialize(opts: { devClient?: any } = {}): Promise<{\n  assetPrefix: string\n}> {\n  // This makes sure this specific lines are removed in production\n  if (process.env.NODE_ENV === 'development') {\n    tracer.onSpanEnd(\n      (\n        require('./tracing/report-to-socket') as typeof import('./tracing/report-to-socket')\n      ).default\n    )\n    devClient = opts.devClient\n  }\n\n  initialData = JSON.parse(\n    document.getElementById('__NEXT_DATA__')!.textContent!\n  )\n  window.__NEXT_DATA__ = initialData\n\n  defaultLocale = initialData.defaultLocale\n  const prefix: string = initialData.assetPrefix || ''\n  // With dynamic assetPrefix it's no longer possible to set assetPrefix at the build time\n  // So, this is how we do it in the client side at runtime\n  ;(self as any).__next_set_public_path__(`${prefix}/_next/`) //eslint-disable-line\n\n  // Initialize next/config with the environment configuration\n  setConfig({\n    serverRuntimeConfig: {},\n    publicRuntimeConfig: initialData.runtimeConfig || {},\n  })\n\n  asPath = getURL()\n\n  // make sure not to attempt stripping basePath for 404s\n  if (hasBasePath(asPath)) {\n    asPath = removeBasePath(asPath)\n  }\n\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const { normalizeLocalePath } =\n      require('../shared/lib/i18n/normalize-locale-path') as typeof import('../shared/lib/i18n/normalize-locale-path')\n\n    const { detectDomainLocale } =\n      require('../shared/lib/i18n/detect-domain-locale') as typeof import('../shared/lib/i18n/detect-domain-locale')\n\n    const { parseRelativeUrl } =\n      require('../shared/lib/router/utils/parse-relative-url') as typeof import('../shared/lib/router/utils/parse-relative-url')\n\n    const { formatUrl } =\n      require('../shared/lib/router/utils/format-url') as typeof import('../shared/lib/router/utils/format-url')\n\n    if (initialData.locales) {\n      const parsedAs = parseRelativeUrl(asPath)\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        initialData.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        parsedAs.pathname = localePathResult.pathname\n        asPath = formatUrl(parsedAs)\n      } else {\n        // derive the default locale if it wasn't detected in the asPath\n        // since we don't prerender static pages with all possible default\n        // locales\n        defaultLocale = initialData.locale\n      }\n\n      // attempt detecting default locale based on hostname\n      const detectedDomain = detectDomainLocale(\n        process.env.__NEXT_I18N_DOMAINS as any,\n        window.location.hostname\n      )\n\n      // TODO: investigate if defaultLocale needs to be populated after\n      // hydration to prevent mismatched renders\n      if (detectedDomain) {\n        defaultLocale = detectedDomain.defaultLocale\n      }\n    }\n  }\n\n  if (initialData.scriptLoader) {\n    const { initScriptLoader } =\n      require('./script') as typeof import('./script')\n    initScriptLoader(initialData.scriptLoader)\n  }\n\n  pageLoader = new PageLoader(initialData.buildId, prefix)\n\n  const register: RegisterFn = ([r, f]) =>\n    pageLoader.routeLoader.onEntrypoint(r, f)\n  if (window.__NEXT_P) {\n    // Defer page registration for another tick. This will increase the overall\n    // latency in hydrating the page, but reduce the total blocking time.\n    window.__NEXT_P.map((p) => setTimeout(() => register(p), 0))\n  }\n  window.__NEXT_P = []\n  ;(window.__NEXT_P as any).push = register\n\n  headManager = initHeadManager()\n  headManager.getIsSsr = () => {\n    return router.isSsr\n  }\n\n  appElement = document.getElementById('__next')\n  return { assetPrefix: prefix }\n}\n\nfunction renderApp(App: AppComponent, appProps: AppProps) {\n  return <App {...appProps} />\n}\n\nfunction AppContainer({\n  children,\n}: React.PropsWithChildren<{}>): React.ReactElement {\n  // Create a memoized value for next/navigation router context.\n  const adaptedForAppRouter = React.useMemo(() => {\n    return adaptForAppRouterInstance(router)\n  }, [])\n  return (\n    <Container\n      fn={(error) =>\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        renderError({ App: CachedApp, err: error }).catch((err) =>\n          console.error('Error rendering page: ', err)\n        )\n      }\n    >\n      <AppRouterContext.Provider value={adaptedForAppRouter}>\n        <SearchParamsContext.Provider value={adaptForSearchParams(router)}>\n          <PathnameContextProviderAdapter\n            router={router}\n            isAutoExport={self.__NEXT_DATA__.autoExport ?? false}\n          >\n            <PathParamsContext.Provider value={adaptForPathParams(router)}>\n              <RouterContext.Provider value={makePublicRouterInstance(router)}>\n                <HeadManagerContext.Provider value={headManager}>\n                  <ImageConfigContext.Provider\n                    value={\n                      process.env\n                        .__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n                    }\n                  >\n                    {children}\n                  </ImageConfigContext.Provider>\n                </HeadManagerContext.Provider>\n              </RouterContext.Provider>\n            </PathParamsContext.Provider>\n          </PathnameContextProviderAdapter>\n        </SearchParamsContext.Provider>\n      </AppRouterContext.Provider>\n    </Container>\n  )\n}\n\nconst wrapApp =\n  (App: AppComponent) =>\n  (wrappedAppProps: Record<string, any>): JSX.Element => {\n    const appProps: AppProps = {\n      ...wrappedAppProps,\n      Component: CachedComponent,\n      err: initialData.err,\n      router,\n    }\n    return <AppContainer>{renderApp(App, appProps)}</AppContainer>\n  }\n\n// This method handles all runtime and debug errors.\n// 404 and 500 errors are special kind of errors\n// and they are still handle via the main render method.\nfunction renderError(renderErrorProps: RenderErrorProps): Promise<any> {\n  let { App, err } = renderErrorProps\n\n  // In development runtime errors are caught by our overlay\n  // In production we catch runtime errors using componentDidCatch which will trigger renderError\n  if (process.env.NODE_ENV !== 'production') {\n    // A Next.js rendering runtime error is always unrecoverable\n    // FIXME: let's make this recoverable (error in GIP client-transition)\n    devClient.onUnrecoverableError()\n\n    // We need to render an empty <App> so that the `<ReactDevOverlay>` can\n    // render itself.\n    // TODO: Fix disabled eslint rule\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    return doRender({\n      App: () => null,\n      props: {},\n      Component: () => null,\n      styleSheets: [],\n    })\n  }\n\n  // Make sure we log the error to the console, otherwise users can't track down issues.\n  console.error(err)\n  console.error(\n    `A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred`\n  )\n\n  return pageLoader\n    .loadPage('/_error')\n    .then(({ page: ErrorComponent, styleSheets }) => {\n      return lastAppProps?.Component === ErrorComponent\n        ? import('../pages/_error')\n            .then((errorModule) => {\n              return import('../pages/_app').then((appModule) => {\n                App = appModule.default as any as AppComponent\n                renderErrorProps.App = App\n                return errorModule\n              })\n            })\n            .then((m) => ({\n              ErrorComponent: m.default as React.ComponentType<{}>,\n              styleSheets: [],\n            }))\n        : { ErrorComponent, styleSheets }\n    })\n    .then(({ ErrorComponent, styleSheets }) => {\n      // In production we do a normal render with the `ErrorComponent` as component.\n      // If we've gotten here upon initial render, we can use the props from the server.\n      // Otherwise, we need to call `getInitialProps` on `App` before mounting.\n      const AppTree = wrapApp(App)\n      const appCtx = {\n        Component: ErrorComponent,\n        AppTree,\n        router,\n        ctx: {\n          err,\n          pathname: initialData.page,\n          query: initialData.query,\n          asPath,\n          AppTree,\n        },\n      }\n      return Promise.resolve(\n        renderErrorProps.props?.err\n          ? renderErrorProps.props\n          : loadGetInitialProps(App, appCtx)\n      ).then((initProps) =>\n        // TODO: Fix disabled eslint rule\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        doRender({\n          ...renderErrorProps,\n          err,\n          Component: ErrorComponent,\n          styleSheets,\n          props: initProps,\n        })\n      )\n    })\n}\n\n// Dummy component that we render as a child of Root so that we can\n// toggle the correct styles before the page is rendered.\nfunction Head({ callback }: { callback: () => void }): null {\n  // We use `useLayoutEffect` to guarantee the callback is executed\n  // as soon as React flushes the update.\n  React.useLayoutEffect(() => callback(), [callback])\n  return null\n}\n\nconst performanceMarks = {\n  navigationStart: 'navigationStart',\n  beforeRender: 'beforeRender',\n  afterRender: 'afterRender',\n  afterHydrate: 'afterHydrate',\n  routeChange: 'routeChange',\n} as const\n\nconst performanceMeasures = {\n  hydration: 'Next.js-hydration',\n  beforeHydration: 'Next.js-before-hydration',\n  routeChangeToRender: 'Next.js-route-change-to-render',\n  render: 'Next.js-render',\n} as const\n\nlet reactRoot: any = null\n// On initial render a hydrate should always happen\nlet shouldHydrate: boolean = true\n\nfunction clearMarks(): void {\n  ;[\n    performanceMarks.beforeRender,\n    performanceMarks.afterHydrate,\n    performanceMarks.afterRender,\n    performanceMarks.routeChange,\n  ].forEach((mark) => performance.clearMarks(mark))\n}\n\nfunction markHydrateComplete(): void {\n  if (!ST) return\n\n  performance.mark(performanceMarks.afterHydrate) // mark end of hydration\n\n  const hasBeforeRenderMark = performance.getEntriesByName(\n    performanceMarks.beforeRender,\n    'mark'\n  ).length\n  if (hasBeforeRenderMark) {\n    const beforeHydrationMeasure = performance.measure(\n      performanceMeasures.beforeHydration,\n      performanceMarks.navigationStart,\n      performanceMarks.beforeRender\n    )\n\n    const hydrationMeasure = performance.measure(\n      performanceMeasures.hydration,\n      performanceMarks.beforeRender,\n      performanceMarks.afterHydrate\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      // Old versions of Safari don't return `PerformanceMeasure`s from `performance.measure()`\n      beforeHydrationMeasure &&\n      hydrationMeasure\n    ) {\n      tracer\n        .startSpan('navigation-to-hydration', {\n          startTime: performance.timeOrigin + beforeHydrationMeasure.startTime,\n          attributes: {\n            pathname: location.pathname,\n            query: location.search,\n          },\n        })\n        .end(\n          performance.timeOrigin +\n            hydrationMeasure.startTime +\n            hydrationMeasure.duration\n        )\n    }\n  }\n\n  if (onPerfEntry) {\n    performance\n      .getEntriesByName(performanceMeasures.hydration)\n      .forEach(onPerfEntry)\n  }\n  clearMarks()\n}\n\nfunction markRenderComplete(): void {\n  if (!ST) return\n\n  performance.mark(performanceMarks.afterRender) // mark end of render\n  const navStartEntries: PerformanceEntryList = performance.getEntriesByName(\n    performanceMarks.routeChange,\n    'mark'\n  )\n\n  if (!navStartEntries.length) return\n\n  const hasBeforeRenderMark = performance.getEntriesByName(\n    performanceMarks.beforeRender,\n    'mark'\n  ).length\n\n  if (hasBeforeRenderMark) {\n    performance.measure(\n      performanceMeasures.routeChangeToRender,\n      navStartEntries[0].name,\n      performanceMarks.beforeRender\n    )\n    performance.measure(\n      performanceMeasures.render,\n      performanceMarks.beforeRender,\n      performanceMarks.afterRender\n    )\n    if (onPerfEntry) {\n      performance\n        .getEntriesByName(performanceMeasures.render)\n        .forEach(onPerfEntry)\n      performance\n        .getEntriesByName(performanceMeasures.routeChangeToRender)\n        .forEach(onPerfEntry)\n    }\n  }\n\n  clearMarks()\n  ;[\n    performanceMeasures.routeChangeToRender,\n    performanceMeasures.render,\n  ].forEach((measure) => performance.clearMeasures(measure))\n}\n\nfunction renderReactElement(\n  domEl: HTMLElement,\n  fn: (cb: () => void) => JSX.Element\n): void {\n  // mark start of hydrate/render\n  if (ST) {\n    performance.mark(performanceMarks.beforeRender)\n  }\n\n  const reactEl = fn(shouldHydrate ? markHydrateComplete : markRenderComplete)\n  if (!reactRoot) {\n    // Unlike with createRoot, you don't need a separate root.render() call here\n    reactRoot = ReactDOM.hydrateRoot(domEl, reactEl, {\n      onRecoverableError,\n    })\n    // TODO: Remove shouldHydrate variable when React 18 is stable as it can depend on `reactRoot` existing\n    shouldHydrate = false\n  } else {\n    const startTransition = (React as any).startTransition\n    startTransition(() => {\n      reactRoot.render(reactEl)\n    })\n  }\n}\n\nfunction Root({\n  callbacks,\n  children,\n}: React.PropsWithChildren<{\n  callbacks: Array<() => void>\n}>): React.ReactElement {\n  // We use `useLayoutEffect` to guarantee the callbacks are executed\n  // as soon as React flushes the update\n  React.useLayoutEffect(\n    () => callbacks.forEach((callback) => callback()),\n    [callbacks]\n  )\n\n  if (process.env.__NEXT_TEST_MODE) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      window.__NEXT_HYDRATED = true\n      window.__NEXT_HYDRATED_AT = performance.now()\n\n      if (window.__NEXT_HYDRATED_CB) {\n        window.__NEXT_HYDRATED_CB()\n      }\n    }, [])\n  }\n\n  return children as React.ReactElement\n}\n\nfunction doRender(input: RenderRouteInfo): Promise<any> {\n  let { App, Component, props, err }: RenderRouteInfo = input\n  let styleSheets: StyleSheetTuple[] | undefined =\n    'initial' in input ? undefined : input.styleSheets\n  Component = Component || lastAppProps.Component\n  props = props || lastAppProps.props\n\n  const appProps: AppProps = {\n    ...props,\n    Component,\n    err,\n    router,\n  }\n  // lastAppProps has to be set before ReactDom.render to account for ReactDom throwing an error.\n  lastAppProps = appProps\n\n  let canceled: boolean = false\n  let resolvePromise: () => void\n  const renderPromise = new Promise<void>((resolve, reject) => {\n    if (lastRenderReject) {\n      lastRenderReject()\n    }\n    resolvePromise = () => {\n      lastRenderReject = null\n      resolve()\n    }\n    lastRenderReject = () => {\n      canceled = true\n      lastRenderReject = null\n\n      const error: any = new Error('Cancel rendering route')\n      error.cancelled = true\n      reject(error)\n    }\n  })\n\n  // This function has a return type to ensure it doesn't start returning a\n  // Promise. It should remain synchronous.\n  function onStart(): boolean {\n    if (\n      !styleSheets ||\n      // We use `style-loader` in development, so we don't need to do anything\n      // unless we're in production:\n      process.env.NODE_ENV !== 'production'\n    ) {\n      return false\n    }\n\n    const currentStyleTags: HTMLStyleElement[] = looseToArray<HTMLStyleElement>(\n      document.querySelectorAll('style[data-n-href]')\n    )\n    const currentHrefs: Set<string | null> = new Set(\n      currentStyleTags.map((tag) => tag.getAttribute('data-n-href'))\n    )\n\n    const noscript: Element | null = document.querySelector(\n      'noscript[data-n-css]'\n    )\n    const nonce: string | null | undefined =\n      noscript?.getAttribute('data-n-css')\n\n    styleSheets.forEach(({ href, text }: { href: string; text: any }) => {\n      if (!currentHrefs.has(href)) {\n        const styleTag = document.createElement('style')\n        styleTag.setAttribute('data-n-href', href)\n        styleTag.setAttribute('media', 'x')\n\n        if (nonce) {\n          styleTag.setAttribute('nonce', nonce)\n        }\n\n        document.head.appendChild(styleTag)\n        styleTag.appendChild(document.createTextNode(text))\n      }\n    })\n    return true\n  }\n\n  function onHeadCommit(): void {\n    if (\n      // Turbopack has it's own css injection handling, this code ends up removing the CSS.\n      !process.env.TURBOPACK &&\n      // We use `style-loader` in development, so we don't need to do anything\n      // unless we're in production:\n      process.env.NODE_ENV === 'production' &&\n      // We can skip this during hydration. Running it wont cause any harm, but\n      // we may as well save the CPU cycles:\n      styleSheets &&\n      // Ensure this render was not canceled\n      !canceled\n    ) {\n      const desiredHrefs: Set<string> = new Set(styleSheets.map((s) => s.href))\n      const currentStyleTags: HTMLStyleElement[] =\n        looseToArray<HTMLStyleElement>(\n          document.querySelectorAll('style[data-n-href]')\n        )\n      const currentHrefs: string[] = currentStyleTags.map(\n        (tag) => tag.getAttribute('data-n-href')!\n      )\n\n      // Toggle `<style>` tags on or off depending on if they're needed:\n      for (let idx = 0; idx < currentHrefs.length; ++idx) {\n        if (desiredHrefs.has(currentHrefs[idx])) {\n          currentStyleTags[idx].removeAttribute('media')\n        } else {\n          currentStyleTags[idx].setAttribute('media', 'x')\n        }\n      }\n\n      // Reorder styles into intended order:\n      let referenceNode: Element | null = document.querySelector(\n        'noscript[data-n-css]'\n      )\n      if (\n        // This should be an invariant:\n        referenceNode\n      ) {\n        styleSheets.forEach(({ href }: { href: string }) => {\n          const targetTag: Element | null = document.querySelector(\n            `style[data-n-href=\"${href}\"]`\n          )\n          if (\n            // This should be an invariant:\n            targetTag\n          ) {\n            referenceNode!.parentNode!.insertBefore(\n              targetTag,\n              referenceNode!.nextSibling\n            )\n            referenceNode = targetTag\n          }\n        })\n      }\n\n      // Finally, clean up server rendered stylesheets:\n      looseToArray<HTMLLinkElement>(\n        document.querySelectorAll('link[data-n-p]')\n      ).forEach((el) => {\n        el.parentNode!.removeChild(el)\n      })\n    }\n\n    if (input.scroll) {\n      const { x, y } = input.scroll\n      disableSmoothScrollDuringRouteTransition(() => {\n        window.scrollTo(x, y)\n      })\n    }\n  }\n\n  function onRootCommit(): void {\n    resolvePromise()\n  }\n\n  onStart()\n\n  const elem: JSX.Element = (\n    <>\n      <Head callback={onHeadCommit} />\n      <AppContainer>\n        {renderApp(App, appProps)}\n        <Portal type=\"next-route-announcer\">\n          <RouteAnnouncer />\n        </Portal>\n      </AppContainer>\n    </>\n  )\n\n  // We catch runtime errors using componentDidCatch which will trigger renderError\n  renderReactElement(appElement!, (callback) => (\n    <Root callbacks={[callback, onRootCommit]}>\n      {process.env.__NEXT_STRICT_MODE ? (\n        <React.StrictMode>{elem}</React.StrictMode>\n      ) : (\n        elem\n      )}\n    </Root>\n  ))\n\n  return renderPromise\n}\n\nasync function render(renderingProps: RenderRouteInfo): Promise<void> {\n  // if an error occurs in a server-side page (e.g. in getInitialProps),\n  // skip re-rendering the error page client-side as data-fetching operations\n  // will already have been done on the server and NEXT_DATA contains the correct\n  // data for straight-forward hydration of the error page\n  if (\n    renderingProps.err &&\n    // renderingProps.Component might be undefined if there is a top/module-level error\n    (typeof renderingProps.Component === 'undefined' ||\n      !renderingProps.isHydratePass)\n  ) {\n    await renderError(renderingProps)\n    return\n  }\n\n  try {\n    await doRender(renderingProps)\n  } catch (err) {\n    const renderErr = getProperError(err)\n    // bubble up cancelation errors\n    if ((renderErr as Error & { cancelled?: boolean }).cancelled) {\n      throw renderErr\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      // Ensure this error is displayed in the overlay in development\n      setTimeout(() => {\n        throw renderErr\n      })\n    }\n    await renderError({ ...renderingProps, err: renderErr })\n  }\n}\n\nexport async function hydrate(opts?: { beforeRender?: () => Promise<void> }) {\n  let initialErr = initialData.err\n\n  try {\n    const appEntrypoint = await pageLoader.routeLoader.whenEntrypoint('/_app')\n    if ('error' in appEntrypoint) {\n      throw appEntrypoint.error\n    }\n\n    const { component: app, exports: mod } = appEntrypoint\n    CachedApp = app as AppComponent\n    if (mod && mod.reportWebVitals) {\n      onPerfEntry = ({\n        id,\n        name,\n        startTime,\n        value,\n        duration,\n        entryType,\n        entries,\n        attribution,\n      }: any): void => {\n        // Combines timestamp with random number for unique ID\n        const uniqueID: string = `${Date.now()}-${\n          Math.floor(Math.random() * (9e12 - 1)) + 1e12\n        }`\n        let perfStartEntry: string | undefined\n\n        if (entries && entries.length) {\n          perfStartEntry = entries[0].startTime\n        }\n\n        const webVitals: NextWebVitalsMetric = {\n          id: id || uniqueID,\n          name,\n          startTime: startTime || perfStartEntry,\n          value: value == null ? duration : value,\n          label:\n            entryType === 'mark' || entryType === 'measure'\n              ? 'custom'\n              : 'web-vital',\n        }\n        if (attribution) {\n          webVitals.attribution = attribution\n        }\n        mod.reportWebVitals(webVitals)\n      }\n    }\n\n    const pageEntrypoint =\n      // The dev server fails to serve script assets when there's a hydration\n      // error, so we need to skip waiting for the entrypoint.\n      process.env.NODE_ENV === 'development' && initialData.err\n        ? { error: initialData.err }\n        : await pageLoader.routeLoader.whenEntrypoint(initialData.page)\n    if ('error' in pageEntrypoint) {\n      throw pageEntrypoint.error\n    }\n    CachedComponent = pageEntrypoint.component\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { isValidElementType } =\n        require('next/dist/compiled/react-is') as typeof import('next/dist/compiled/react-is')\n      if (!isValidElementType(CachedComponent)) {\n        throw new Error(\n          `The default export is not a React Component in page: \"${initialData.page}\"`\n        )\n      }\n    }\n  } catch (error) {\n    // This catches errors like throwing in the top level of a module\n    initialErr = getProperError(error)\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    const getServerError = (\n      require('../server/dev/node-stack-frames') as typeof import('../server/dev/node-stack-frames')\n    ).getServerError\n    // Server-side runtime errors need to be re-thrown on the client-side so\n    // that the overlay is rendered.\n    if (initialErr) {\n      if (initialErr === initialData.err) {\n        setTimeout(() => {\n          let error\n          try {\n            // Generate a new error object. We `throw` it because some browsers\n            // will set the `stack` when thrown, and we want to ensure ours is\n            // not overridden when we re-throw it below.\n            throw new Error(initialErr!.message)\n          } catch (e) {\n            error = e as Error\n          }\n\n          error.name = initialErr!.name\n          error.stack = initialErr!.stack\n          const errSource = initialErr.source!\n\n          // In development, error the navigation API usage in runtime,\n          // since it's not allowed to be used in pages router as it doesn't contain error boundary like app router.\n          if (isNextRouterError(initialErr)) {\n            error.message =\n              'Next.js navigation API is not allowed to be used in Pages Router.'\n          }\n\n          throw getServerError(error, errSource)\n        })\n      }\n      // We replaced the server-side error with a client-side error, and should\n      // no longer rewrite the stack trace to a Node error.\n      else {\n        setTimeout(() => {\n          throw initialErr\n        })\n      }\n    }\n  }\n\n  if (window.__NEXT_PRELOADREADY) {\n    await window.__NEXT_PRELOADREADY(initialData.dynamicIds)\n  }\n\n  router = createRouter(initialData.page, initialData.query, asPath, {\n    initialProps: initialData.props,\n    pageLoader,\n    App: CachedApp,\n    Component: CachedComponent,\n    wrapApp,\n    err: initialErr,\n    isFallback: Boolean(initialData.isFallback),\n    subscription: (info, App, scroll) =>\n      render(\n        Object.assign<\n          {},\n          Omit<RenderRouteInfo, 'App' | 'scroll'>,\n          Pick<RenderRouteInfo, 'App' | 'scroll'>\n        >({}, info, {\n          App,\n          scroll,\n        }) as RenderRouteInfo\n      ),\n    locale: initialData.locale,\n    locales: initialData.locales,\n    defaultLocale,\n    domainLocales: initialData.domainLocales,\n    isPreview: initialData.isPreview,\n  })\n\n  initialMatchesMiddleware = await router._initialMatchesMiddlewarePromise\n\n  const renderCtx: RenderRouteInfo = {\n    App: CachedApp,\n    initial: true,\n    Component: CachedComponent,\n    props: initialData.props,\n    err: initialErr,\n    isHydratePass: true,\n  }\n\n  if (opts?.beforeRender) {\n    await opts.beforeRender()\n  }\n\n  render(renderCtx)\n}\n"], "names": ["emitter", "hydrate", "initialize", "router", "version", "process", "env", "__NEXT_VERSION", "mitt", "looseToArray", "input", "slice", "call", "initialData", "defaultLocale", "undefined", "<PERSON><PERSON><PERSON>", "page<PERSON><PERSON>der", "appElement", "headManager", "initialMatchesMiddleware", "lastAppProps", "lastRenderReject", "devClient", "CachedApp", "onPerfEntry", "CachedComponent", "Container", "React", "Component", "componentDidCatch", "componentErr", "info", "props", "fn", "componentDidMount", "scrollToHash", "isSsr", "<PERSON><PERSON><PERSON><PERSON>", "nextExport", "isDynamicRoute", "pathname", "location", "search", "__NEXT_HAS_REWRITES", "__N_SSG", "replace", "String", "assign", "urlQueryToSearchParams", "query", "URLSearchParams", "_h", "shallow", "catch", "err", "cancelled", "componentDidUpdate", "hash", "substring", "el", "document", "getElementById", "setTimeout", "scrollIntoView", "render", "NODE_ENV", "children", "PagesDevOverlayBridge", "require", "opts", "tracer", "onSpanEnd", "default", "JSON", "parse", "textContent", "window", "__NEXT_DATA__", "prefix", "assetPrefix", "self", "__next_set_public_path__", "setConfig", "serverRuntimeConfig", "publicRuntimeConfig", "runtimeConfig", "getURL", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "__NEXT_I18N_SUPPORT", "normalizeLocalePath", "detectDomainLocale", "parseRelativeUrl", "formatUrl", "locales", "parsedAs", "localePathResult", "detectedLocale", "locale", "detectedDomain", "__NEXT_I18N_DOMAINS", "hostname", "<PERSON><PERSON><PERSON><PERSON>", "initScriptLoader", "<PERSON><PERSON><PERSON><PERSON>", "buildId", "register", "r", "f", "routeLoader", "onEntrypoint", "__NEXT_P", "map", "p", "push", "initHeadManager", "getIsSsr", "renderApp", "App", "appProps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adaptedForAppRouter", "useMemo", "adaptForAppRouterInstance", "error", "renderError", "console", "AppRouterContext", "Provider", "value", "SearchParamsContext", "adaptForSearchParams", "PathnameContextProviderAdapter", "isAutoExport", "autoExport", "PathParamsContext", "adaptForPathParams", "RouterContext", "makePublicRouterInstance", "HeadManagerContext", "ImageConfigContext", "__NEXT_IMAGE_OPTS", "wrapApp", "wrappedAppProps", "renderErrorProps", "onUnrecoverableError", "doR<PERSON>", "styleSheets", "loadPage", "then", "page", "ErrorComponent", "errorModule", "appModule", "m", "AppTree", "appCtx", "ctx", "Promise", "resolve", "loadGetInitialProps", "initProps", "Head", "callback", "useLayoutEffect", "performanceMarks", "navigationStart", "beforeRender", "afterRender", "afterHydrate", "routeChange", "performanceMeasures", "hydration", "beforeHydration", "routeChangeToRender", "reactRoot", "shouldHydrate", "clearMarks", "for<PERSON>ach", "mark", "performance", "markHydrateComplete", "ST", "hasBeforeRenderMark", "getEntriesByName", "length", "beforeHydrationMeasure", "measure", "hydrationMeasure", "startSpan", "startTime", "<PERSON><PERSON><PERSON><PERSON>", "attributes", "end", "duration", "markRenderComplete", "navStartEntries", "name", "clearMeasures", "renderReactElement", "domEl", "reactEl", "ReactDOM", "hydrateRoot", "onRecoverableError", "startTransition", "Root", "callbacks", "__NEXT_TEST_MODE", "useEffect", "__NEXT_HYDRATED", "__NEXT_HYDRATED_AT", "now", "__NEXT_HYDRATED_CB", "canceled", "resolvePromise", "renderPromise", "reject", "Error", "onStart", "currentStyleTags", "querySelectorAll", "currentHrefs", "Set", "tag", "getAttribute", "noscript", "querySelector", "nonce", "href", "text", "has", "styleTag", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "createTextNode", "onHeadCommit", "TURBOPACK", "desiredHrefs", "s", "idx", "removeAttribute", "referenceNode", "targetTag", "parentNode", "insertBefore", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "scroll", "x", "y", "disableSmoothScrollDuringRouteTransition", "scrollTo", "onRootCommit", "elem", "Portal", "type", "RouteAnnouncer", "__NEXT_STRICT_MODE", "StrictMode", "renderingProps", "isHydratePass", "renderErr", "getProperError", "initialErr", "appEntrypoint", "whenEntrypoint", "component", "app", "exports", "mod", "reportWebVitals", "id", "entryType", "entries", "attribution", "uniqueID", "Date", "Math", "floor", "random", "perfStartEntry", "webVitals", "label", "pageEntrypoint", "isValidElementType", "getServerError", "message", "e", "stack", "errSource", "source", "isNextRouterError", "__NEXT_PRELOADREADY", "dynamicIds", "createRouter", "initialProps", "Boolean", "subscription", "Object", "domainLocales", "isPreview", "_initialMatchesMiddlewarePromise", "renderCtx", "initial"], "mappings": "AAAA,mBAAmB,GACnB,6DAA6D;AAwHjDK,QAAQC,GAAG,CAACsC,mBAAmB;;;;;;;;;;;;;;;;;;;IA9C9B5C,OAAO,EAAA;eAAPA;;IAiwBSC,OAAO,EAAA;eAAPA;;IA9oBAC,UAAU,EAAA;eAAVA;;IApHXC,MAAM,EAAA;eAANA;;IADEC,OAAO,EAAA;eAAPA;;;;;;gEA/DmB;iEACX;iDACc;+DAClB;4CAEa;qCAC2B;2BAC1B;6BAIxB;uCACmB;uBACsB;wBAEzB;sEACK;qEACL;gCAEQ;wBACwB;yBACxB;iDACI;gCAEJ;6BACH;+CACK;0BAM1B;iDAIA;oCAC4B;iEAChB;mCACe;AAwB3B,MAAMA,UAAUC,QAAQC,GAAG,CAACC,cAAc;AAC1C,IAAIJ;AACJ,MAAMH,UAA+BQ,CAAAA,GAAAA,MAAAA,OAAI;AAEhD,MAAMC,eAAe,CAAeC,QAAoB,EAAE,CAACC,KAAK,CAACC,IAAI,CAACF;AAEtE,IAAIG;AACJ,IAAIC,gBAAoCC;AACxC,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAKJ,IAAIC,2BAA2B;AAC/B,IAAIC;AAEJ,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,WAAyBC;AAC7B,IAAIC;AAEJ,MAAMC,kBAAkBC,OAAAA,OAAK,CAACC,SAAS;IAIrCC,kBAAkBC,YAAmB,EAAEC,IAAS,EAAE;QAChD,IAAI,CAACC,KAAK,CAACC,EAAE,CAACH,cAAcC;IAC9B;IAEAG,oBAAoB;QAClB,IAAI,CAACC,YAAY;QAEjB,0CAA0C;QAC1C,yEAAyE;QACzE,oEAAoE;QACpE,sDAAsD;QACtD,qEAAqE;QACrE,kEAAkE;QAClE,IACEjC,OAAOkC,KAAK,IACXxB,CAAAA,YAAYyB,UAAU,IACpBzB,YAAY0B,UAAU,IACpBC,CAAAA,CAAAA,GAAAA,WAAAA,cAAc,EAACrC,OAAOsC,QAAQ,KAC7BC,SAASC,MAAM,+CAEfvB,wBAAuB,KAC1BP,YAAYoB,KAAK,IAChBpB,YAAYoB,KAAK,CAACY,OAAO,IACxBH,CAAAA,SAASC,MAAM,IACdtC,QAAQC,GAAG,CAACsC,mBAAmB,YAC/BxB,wBAAuB,CAAE,GAC/B;YACA,2CAA2C;YAC3CjB,OACG2C,OAAO,CACN3C,OAAOsC,QAAQ,GACb,MACAM,OACEC,CAAAA,GAAAA,aAAAA,MAAM,EACJC,CAAAA,GAAAA,aAAAA,sBAAsB,EAAC9C,OAAO+C,KAAK,GACnC,IAAIC,gBAAgBT,SAASC,MAAM,KAGzC3B,QACA;gBACE,aAAa;gBACb,0DAA0D;gBAC1D,oEAAoE;gBACpE,4CAA4C;gBAC5CoC,IAAI;gBACJ,mEAAmE;gBACnE,eAAe;gBACf,mEAAmE;gBACnE,yCAAyC;gBACzCC,SAAS,CAACxC,YAAYyB,UAAU,IAAI,CAAClB;YACvC,GAEDkC,KAAK,CAAC,CAACC;gBACN,IAAI,CAACA,IAAIC,SAAS,EAAE,MAAMD;YAC5B;QACJ;IACF;IAEAE,qBAAqB;QACnB,IAAI,CAACrB,YAAY;IACnB;IAEAA,eAAe;QACb,IAAI,EAAEsB,IAAI,EAAE,GAAGhB;QACfgB,OAAOA,QAAQA,KAAKC,SAAS,CAAC;QAC9B,IAAI,CAACD,MAAM;QAEX,MAAME,KAAyBC,SAASC,cAAc,CAACJ;QACvD,IAAI,CAACE,IAAI;QAET,2DAA2D;QAC3D,4BAA4B;QAC5BG,WAAW,IAAMH,GAAGI,cAAc,IAAI;IACxC;IAEAC,SAAS;QACP,IAAI5D,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,cAAc;;aAEpC;YACL,MAAM,EAAEE,qBAAqB,EAAE,GAC7BC,QAAQ;YACV,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACD,uBAAAA;0BAAuB,IAAI,CAACnC,KAAK,CAACkC,QAAQ;;QAE/C;IACF;AACF;AAEO,eAAejE,WAAWoE,IAA8B;IAA9BA,IAAAA,SAAAA,KAAAA,GAAAA,OAA4B,CAAC;IAG5D,gEAAgE;IAChE,IAAIjE,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,WAAe;QAC1CK,QAAAA,OAAM,CAACC,SAAS,CAEZH,QAAQ,6GACRI,OAAO;QAEXlD,YAAY+C,KAAK/C,SAAS;IAC5B;IAEAV,cAAc6D,KAAKC,KAAK,CACtBd,SAASC,cAAc,CAAC,iBAAkBc,WAAW;IAEvDC,OAAOC,aAAa,GAAGjE;IAEvBC,gBAAgBD,YAAYC,aAAa;IACzC,MAAMiE,SAAiBlE,YAAYmE,WAAW,IAAI;IAGhDC,KAAaC,wBAAwB,CAAE,KAAEH,SAAO,WAAU,qBAAqB;;IAEjF,4DAA4D;IAC5DI,CAAAA,GAAAA,uBAAAA,SAAS,EAAC;QACRC,qBAAqB,CAAC;QACtBC,qBAAqBxE,YAAYyE,aAAa,IAAI,CAAC;IACrD;IAEAtE,SAASuE,CAAAA,GAAAA,OAAAA,MAAM;IAEf,uDAAuD;IACvD,IAAIC,CAAAA,GAAAA,aAAAA,WAAW,EAACxE,SAAS;QACvBA,SAASyE,CAAAA,GAAAA,gBAAAA,cAAc,EAACzE;IAC1B;IAEA,IAAIX,QAAQC,GAAG,CAACoF,mBAAmB,EAAE;;IA4CrC,IAAI7E,YAAY0F,YAAY,EAAE;QAC5B,MAAM,EAAEC,gBAAgB,EAAE,GACxBnC,QAAQ;QACVmC,iBAAiB3F,YAAY0F,YAAY;IAC3C;IAEAtF,aAAa,IAAIwF,YAAAA,OAAU,CAAC5F,YAAY6F,OAAO,EAAE3B;IAEjD,MAAM4B,WAAuB,CAAA;YAAC,CAACC,GAAGC,EAAE,GAAA;eAClC5F,WAAW6F,WAAW,CAACC,YAAY,CAACH,GAAGC;;IACzC,IAAIhC,OAAOmC,QAAQ,EAAE;QACnB,2EAA2E;QAC3E,qEAAqE;QACrEnC,OAAOmC,QAAQ,CAACC,GAAG,CAAC,CAACC,IAAMnD,WAAW,IAAM4C,SAASO,IAAI;IAC3D;IACArC,OAAOmC,QAAQ,GAAG,EAAE;IAClBnC,OAAOmC,QAAQ,CAASG,IAAI,GAAGR;IAEjCxF,cAAciG,CAAAA,GAAAA,aAAAA,OAAe;IAC7BjG,YAAYkG,QAAQ,GAAG;QACrB,OAAOlH,OAAOkC,KAAK;IACrB;IAEAnB,aAAa2C,SAASC,cAAc,CAAC;IACrC,OAAO;QAAEkB,aAAaD;IAAO;AAC/B;AAEA,SAASuC,UAAUC,GAAiB,EAAEC,QAAkB;IACtD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACD,KAAAA;QAAK,GAAGC,QAAQ;;AAC1B;AAEA,SAASC,aAAa,KAEQ;IAFR,IAAA,EACpBtD,QAAQ,EACoB,GAFR;IAGpB,8DAA8D;IAC9D,MAAMuD,sBAAsB9F,OAAAA,OAAK,CAAC+F,OAAO;qDAAC;YACxC,OAAOC,CAAAA,GAAAA,UAAAA,yBAAyB,EAACzH;QACnC;oDAAG,EAAE;QAemB8E;IAdxB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACtD,WAAAA;QACCO,IAAI,CAAC2F,QACH,AACA,iCADiC,kCACkC;YACnEC,YAAY;gBAAEP,KAAK/F;gBAAW+B,KAAKsE;YAAM,GAAGvE,KAAK,CAAC,CAACC,MACjDwE,QAAQF,KAAK,CAAC,0BAA0BtE;kBAI5C,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACyE,+BAAAA,gBAAgB,CAACC,QAAQ,EAAA;YAACC,OAAOR;sBAChC,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACS,iCAAAA,mBAAmB,CAACF,QAAQ,EAAA;gBAACC,OAAOE,CAAAA,GAAAA,UAAAA,oBAAoB,EAACjI;0BACxD,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACkI,UAAAA,8BAA8B,EAAA;oBAC7BlI,QAAQA;oBACRmI,cAAcrD,CAAAA,iCAAAA,KAAKH,aAAa,CAACyD,UAAU,KAAA,OAA7BtD,iCAAiC;8BAE/C,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACuD,iCAAAA,iBAAiB,CAACP,QAAQ,EAAA;wBAACC,OAAOO,CAAAA,GAAAA,UAAAA,kBAAkB,EAACtI;kCACpD,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACuI,4BAAAA,aAAa,CAACT,QAAQ,EAAA;4BAACC,OAAOS,CAAAA,GAAAA,QAAAA,wBAAwB,EAACxI;sCACtD,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACyI,iCAAAA,kBAAkB,CAACX,QAAQ,EAAA;gCAACC,OAAO/G;0CAClC,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAAC0H,iCAAAA,kBAAkB,CAACZ,QAAQ,EAAA;oCAC1BC,KAAAA,EACE7H,QAAQC,GAAG,CACRwI,iBAAiB;8CAGrB3E;;;;;;;;;AAUrB;AAEA,MAAM4E,UACJ,CAACxB,MACD,CAACyB;QACC,MAAMxB,WAAqB;YACzB,GAAGwB,eAAe;YAClBnH,WAAWH;YACX6B,KAAK1C,YAAY0C,GAAG;YACpBpD;QACF;QACA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACsH,cAAAA;sBAAcH,UAAUC,KAAKC;;IACvC;AAEF,oDAAoD;AACpD,gDAAgD;AAChD,wDAAwD;AACxD,SAASM,YAAYmB,gBAAkC;IACrD,IAAI,EAAE1B,GAAG,EAAEhE,GAAG,EAAE,GAAG0F;IAEnB,0DAA0D;IAC1D,+FAA+F;IAC/F,IAAI5I,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,WAAc;QACzC,4DAA4D;QAC5D,sEAAsE;QACtE3C,UAAU2H,oBAAoB;QAE9B,uEAAuE;QACvE,iBAAiB;QACjB,iCAAiC;QACjC,mEAAmE;QACnE,OAAOC,SAAS;YACd5B,KAAK,IAAM;YACXtF,OAAO,CAAC;YACRJ,WAAW,IAAM;YACjBuH,aAAa,EAAE;QACjB;IACF;;;AA2DF;AAEA,mEAAmE;AACnE,yDAAyD;AACzD,SAASe,KAAK,KAAsC;IAAtC,IAAA,EAAEC,QAAQ,EAA4B,GAAtC;IACZ,iEAAiE;IACjE,uCAAuC;IACvCxI,OAAAA,OAAK,CAACyI,eAAe;gCAAC,IAAMD;+BAAY;QAACA;KAAS;IAClD,OAAO;AACT;AAEA,MAAME,mBAAmB;IACvBC,iBAAiB;IACjBC,cAAc;IACdC,aAAa;IACbC,cAAc;IACdC,aAAa;AACf;AAEA,MAAMC,sBAAsB;IAC1BC,WAAW;IACXC,iBAAiB;IACjBC,qBAAqB;IACrB9G,QAAQ;AACV;AAEA,IAAI+G,YAAiB;AACrB,mDAAmD;AACnD,IAAIC,gBAAyB;AAE7B,SAASC;;IACN;QACCZ,iBAAiBE,YAAY;QAC7BF,iBAAiBI,YAAY;QAC7BJ,iBAAiBG,WAAW;QAC5BH,iBAAiBK,WAAW;KAC7B,CAACQ,OAAO,CAAC,CAACC,OAASC,YAAYH,UAAU,CAACE;AAC7C;AAEA,SAASE;IACP,IAAI,CAACC,OAAAA,EAAE,EAAE;IAETF,YAAYD,IAAI,CAACd,iBAAiBI,YAAY,EAAE,wBAAwB;;IAExE,MAAMc,sBAAsBH,YAAYI,gBAAgB,CACtDnB,iBAAiBE,YAAY,EAC7B,QACAkB,MAAM;IACR,IAAIF,qBAAqB;QACvB,MAAMG,yBAAyBN,YAAYO,OAAO,CAChDhB,oBAAoBE,eAAe,EACnCR,iBAAiBC,eAAe,EAChCD,iBAAiBE,YAAY;QAG/B,MAAMqB,mBAAmBR,YAAYO,OAAO,CAC1ChB,oBAAoBC,SAAS,EAC7BP,iBAAiBE,YAAY,EAC7BF,iBAAiBI,YAAY;QAG/B,IACErK,QAAQC,GAAG,CAAC4D,QAAQ,gCAAK,iBACzB,yFAAyF;QACzFyH,0BACAE,kBACA;YACAtH,QAAAA,OAAM,CACHuH,SAAS,CAAC,2BAA2B;gBACpCC,WAAWV,YAAYW,UAAU,GAAGL,uBAAuBI,SAAS;gBACpEE,YAAY;oBACVxJ,UAAUC,SAASD,QAAQ;oBAC3BS,OAAOR,SAASC,MAAM;gBACxB;YACF,GACCuJ,GAAG,CACFb,YAAYW,UAAU,GACpBH,iBAAiBE,SAAS,GAC1BF,iBAAiBM,QAAQ;QAEjC;IACF;IAEA,IAAI1K,aAAa;QACf4J,YACGI,gBAAgB,CAACb,oBAAoBC,SAAS,EAC9CM,OAAO,CAAC1J;IACb;IACAyJ;AACF;AAEA,SAASkB;IACP,IAAI,CAACb,OAAAA,EAAE,EAAE;IAETF,YAAYD,IAAI,CAACd,iBAAiBG,WAAW,EAAE,qBAAqB;;IACpE,MAAM4B,kBAAwChB,YAAYI,gBAAgB,CACxEnB,iBAAiBK,WAAW,EAC5B;IAGF,IAAI,CAAC0B,gBAAgBX,MAAM,EAAE;IAE7B,MAAMF,sBAAsBH,YAAYI,gBAAgB,CACtDnB,iBAAiBE,YAAY,EAC7B,QACAkB,MAAM;IAER,IAAIF,qBAAqB;QACvBH,YAAYO,OAAO,CACjBhB,oBAAoBG,mBAAmB,EACvCsB,eAAe,CAAC,EAAE,CAACC,IAAI,EACvBhC,iBAAiBE,YAAY;QAE/Ba,YAAYO,OAAO,CACjBhB,oBAAoB3G,MAAM,EAC1BqG,iBAAiBE,YAAY,EAC7BF,iBAAiBG,WAAW;QAE9B,IAAIhJ,aAAa;YACf4J,YACGI,gBAAgB,CAACb,oBAAoB3G,MAAM,EAC3CkH,OAAO,CAAC1J;YACX4J,YACGI,gBAAgB,CAACb,oBAAoBG,mBAAmB,EACxDI,OAAO,CAAC1J;QACb;IACF;IAEAyJ;IACC;QACCN,oBAAoBG,mBAAmB;QACvCH,oBAAoB3G,MAAM;KAC3B,CAACkH,OAAO,CAAC,CAACS,UAAYP,YAAYkB,aAAa,CAACX;AACnD;AAEA,SAASY,mBACPC,KAAkB,EAClBvK,EAAmC;IAEnC,+BAA+B;IAC/B,IAAIqJ,OAAAA,EAAE,EAAE;QACNF,YAAYD,IAAI,CAACd,iBAAiBE,YAAY;IAChD;IAEA,MAAMkC,UAAUxK,GAAG+I,gBAAgBK,sBAAsBc;IACzD,IAAI,CAACpB,WAAW;QACd,4EAA4E;QAC5EA,YAAY2B,QAAAA,OAAQ,CAACC,WAAW,CAACH,OAAOC,SAAS;YAC/CG,oBAAAA,oBAAAA,kBAAkB;QACpB;QACA,uGAAuG;QACvG5B,gBAAgB;IAClB,OAAO;QACL,MAAM6B,kBAAmBlL,OAAAA,OAAK,CAASkL,eAAe;QACtDA,gBAAgB;YACd9B,UAAU/G,MAAM,CAACyI;QACnB;IACF;AACF;AAEA,SAASK,KAAK,KAKZ;IALY,IAAA,EACZC,SAAS,EACT7I,QAAQ,EAGR,GALY;IAMZ,mEAAmE;IACnE,sCAAsC;IACtCvC,OAAAA,OAAK,CAACyI,eAAe;gCACnB,IAAM2C,UAAU7B,OAAO;wCAAC,CAACf,WAAaA;;+BACtC;QAAC4C;KAAU;IAGb,IAAI3M,QAAQC,GAAG,CAAC2M,gBAAgB,EAAE;;IAYlC,OAAO9I;AACT;AAEA,SAASgF,SAASzI,KAAsB;IACtC,IAAI,EAAE6G,GAAG,EAAE1F,SAAS,EAAEI,KAAK,EAAEsB,GAAG,EAAE,GAAoB7C;IACtD,IAAI0I,cACF,aAAa1I,QAAQK,YAAYL,MAAM0I,WAAW;IACpDvH,YAAYA,aAAaR,aAAaQ,SAAS;IAC/CI,QAAQA,SAASZ,aAAaY,KAAK;IAEnC,MAAMuF,WAAqB;QACzB,GAAGvF,KAAK;QACRJ;QACA0B;QACApD;IACF;IACA,+FAA+F;IAC/FkB,eAAemG;IAEf,IAAI+F,WAAoB;IACxB,IAAIC;IACJ,MAAMC,gBAAgB,IAAI1D,QAAc,CAACC,SAAS0D;QAChD,IAAIpM,kBAAkB;YACpBA;QACF;QACAkM,iBAAiB;YACflM,mBAAmB;YACnB0I;QACF;QACA1I,mBAAmB;YACjBiM,WAAW;YACXjM,mBAAmB;YAEnB,MAAMuG,QAAa,OAAA,cAAmC,CAAnC,IAAI8F,MAAM,2BAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAkC;YACrD9F,MAAMrE,SAAS,GAAG;YAClBkK,OAAO7F;QACT;IACF;IAEA,yEAAyE;IACzE,yCAAyC;IACzC,SAAS+F;QACP,IACE,CAACxE,eACD,oBAGA,oDAHwE;YAIxE,OAAO;QACT;;;QAEA,MAAMyE,mBAAuCpN,aAC3CoD,SAASiK,gBAAgB,CAAC;QAE5B,MAAMC,eAAmC,IAAIC,IAC3CH,iBAAiB5G,GAAG,CAAC,CAACgH,MAAQA,IAAIC,YAAY,CAAC;QAGjD,MAAMC,WAA2BtK,SAASuK,aAAa,CACrD;QAEF,MAAMC,QACJF,4BAAAA,SAAUD,YAAY,CAAC;IAiB3B;IAEA,SAASa;QACP,IACE,qFAAqF;;QA8DvF,IAAIrO,MAAMiP,MAAM,EAAE;YAChB,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGnP,MAAMiP,MAAM;YAC7BG,CAAAA,GAAAA,qBAAAA,wCAAwC,EAAC;gBACvCjL,OAAOkL,QAAQ,CAACH,GAAGC;YACrB;QACF;IACF;IAEA,SAASG;QACPxC;IACF;IAEAI;IAEA,MAAMqC,OAAAA,WAAAA,GACJ,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;0BACE,CAAA,GAAA,YAAA,GAAA,EAAC9F,MAAAA;gBAAKC,UAAU2E;;0BAChB,CAAA,GAAA,YAAA,IAAA,EAACtH,cAAAA;;oBACEH,UAAUC,KAAKC;kCAChB,CAAA,GAAA,YAAA,GAAA,EAAC0I,QAAAA,MAAM,EAAA;wBAACC,MAAK;kCACX,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACC,gBAAAA,cAAc,EAAA,CAAA;;;;;;IAMvB,iFAAiF;IACjF5D,mBAAmBtL,YAAa,CAACkJ,WAAAA,WAAAA,GAC/B,CAAA,GAAA,YAAA,GAAA,EAAC2C,MAAAA;YAAKC,WAAW;gBAAC5C;gBAAU4F;aAAa;sBACtC3P,QAAQC,GAAG,CAAC+P,kBAAkB,QAAA,WAAA,GAC7B,qBAACzO,KAEDqO,SAFM,CAACK,UAAU;;IAOvB,OAAO7C;AACT;AAEA,eAAexJ,OAAOsM,cAA+B;IACnD,sEAAsE;IACtE,2EAA2E;IAC3E,+EAA+E;IAC/E,wDAAwD;IACxD,IACEA,eAAehN,GAAG,IAClB,mFAAmF;IAClF,CAAA,OAAOgN,eAAe1O,SAAS,KAAK,eACnC,CAAC0O,eAAeC,aAAY,GAC9B;QACA,MAAM1I,YAAYyI;QAClB;IACF;IAEA,IAAI;QACF,MAAMpH,SAASoH;IACjB,EAAE,OAAOhN,KAAK;QACZ,MAAMkN,YAAYC,CAAAA,GAAAA,SAAAA,cAAc,EAACnN;QACjC,+BAA+B;QAC/B,IAAKkN,UAA8CjN,SAAS,EAAE;YAC5D,MAAMiN;QACR;QAEA,IAAIpQ,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,WAAe;YAC1C,+DAA+D;YAC/DH,WAAW;gBACT,MAAM0M;YACR;QACF;QACA,MAAM3I,YAAY;YAAE,GAAGyI,cAAc;YAAEhN,KAAKkN;QAAU;IACxD;AACF;AAEO,eAAexQ,QAAQqE,IAA6C;IACzE,IAAIqM,aAAa9P,YAAY0C,GAAG;IAEhC,IAAI;QACF,MAAMqN,gBAAgB,MAAM3P,WAAW6F,WAAW,CAAC+J,cAAc,CAAC;QAClE,IAAI,WAAWD,eAAe;YAC5B,MAAMA,cAAc/I,KAAK;QAC3B;QAEA,MAAM,EAAEiJ,WAAWC,GAAG,EAAEC,SAASC,GAAG,EAAE,GAAGL;QACzCpP,YAAYuP;QACZ,IAAIE,OAAOA,IAAIC,eAAe,EAAE;YAC9BzP,cAAc,CAAA;oBAAC,EACb0P,EAAE,EACF7E,IAAI,EACJP,SAAS,EACT7D,KAAK,EACLiE,QAAQ,EACRiF,SAAS,EACTC,OAAO,EACPC,WAAW,EACP,GAAA;gBACJ,sDAAsD;gBACtD,MAAMC,WAAsBC,KAAKnE,GAAG,KAAG,MACrCoE,CAAAA,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAM,CAAA,OAAO,CAAA,KAAM,IAAG;gBAE9C,IAAIC;gBAEJ,IAAIP,WAAWA,QAAQ3F,MAAM,EAAE;oBAC7BkG,iBAAiBP,OAAO,CAAC,EAAE,CAACtF,SAAS;gBACvC;gBAEA,MAAM8F,YAAiC;oBACrCV,IAAIA,MAAMI;oBACVjF;oBACAP,WAAWA,aAAa6F;oBACxB1J,OAAOA,SAAS,OAAOiE,WAAWjE;oBAClC4J,OACEV,cAAc,UAAUA,cAAc,YAClC,WACA;gBACR;gBACA,IAAIE,aAAa;oBACfO,UAAUP,WAAW,GAAGA;gBAC1B;gBACAL,IAAIC,eAAe,CAACW;YACtB;QACF;QAEA,MAAME,iBACJ,AACA,wDAAwD,eADe;QAEvE1R,QAAQC,GAAG,CAAC4D,QAAQ,gCAAK,iBAAiBrD,YAAY0C,GAAG,GACrD;YAAEsE,OAAOhH,YAAY0C,GAAG;QAAC,IACzB,MAAMtC,WAAW6F,WAAW,CAAC+J,cAAc,CAAChQ,YAAY0I,IAAI;QAClE,IAAI,WAAWwI,gBAAgB;YAC7B,MAAMA,eAAelK,KAAK;QAC5B;QACAnG,kBAAkBqQ,eAAejB,SAAS;QAE1C,IAAIzQ,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAE8N,kBAAkB,EAAE,GAC1B3N,QAAQ;YACV,IAAI,CAAC2N,mBAAmBtQ,kBAAkB;gBACxC,MAAM,OAAA,cAEL,CAFK,IAAIiM,MACP,2DAAwD9M,YAAY0I,IAAI,GAAC,MADtE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF,EAAE,OAAO1B,OAAO;QACd,iEAAiE;QACjE8I,aAAaD,CAAAA,GAAAA,SAAAA,cAAc,EAAC7I;IAC9B;IAEA,IAAIxH,QAAQC,GAAG,CAAC4D,QAAQ,KAAK,WAAe;QAC1C,MAAM+N,iBACJ5N,QAAQ,0GACR4N,cAAc;QAChB,wEAAwE;QACxE,gCAAgC;QAChC,IAAItB,YAAY;YACd,IAAIA,eAAe9P,YAAY0C,GAAG,EAAE;gBAClCQ,WAAW;oBACT,IAAI8D;oBACJ,IAAI;wBACF,mEAAmE;wBACnE,kEAAkE;wBAClE,4CAA4C;wBAC5C,MAAM,OAAA,cAA8B,CAA9B,IAAI8F,MAAMgD,WAAYuB,OAAO,GAA7B,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6B;oBACrC,EAAE,OAAOC,GAAG;wBACVtK,QAAQsK;oBACV;oBAEAtK,MAAMyE,IAAI,GAAGqE,WAAYrE,IAAI;oBAC7BzE,MAAMuK,KAAK,GAAGzB,WAAYyB,KAAK;oBAC/B,MAAMC,YAAY1B,WAAW2B,MAAM;oBAEnC,6DAA6D;oBAC7D,0GAA0G;oBAC1G,IAAIC,CAAAA,GAAAA,mBAAAA,iBAAiB,EAAC5B,aAAa;wBACjC9I,MAAMqK,OAAO,GACX;oBACJ;oBAEA,MAAMD,eAAepK,OAAOwK;gBAC9B;YACF,OAGK;gBACHtO,WAAW;oBACT,MAAM4M;gBACR;YACF;QACF;IACF;IAEA,IAAI9L,OAAO2N,mBAAmB,EAAE;QAC9B,MAAM3N,OAAO2N,mBAAmB,CAAC3R,YAAY4R,UAAU;IACzD;IAEAtS,SAASuS,CAAAA,GAAAA,QAAAA,YAAY,EAAC7R,YAAY0I,IAAI,EAAE1I,YAAYqC,KAAK,EAAElC,QAAQ;QACjE2R,cAAc9R,YAAYoB,KAAK;QAC/BhB;QACAsG,KAAK/F;QACLK,WAAWH;QACXqH;QACAxF,KAAKoN;QACLrO,YAAYsQ,QAAQ/R,YAAYyB,UAAU;QAC1CuQ,cAAc,CAAC7Q,MAAMuF,KAAKoI,SACxB1L,OACE6O,OAAO9P,MAAM,CAIX,CAAC,GAAGhB,MAAM;gBACVuF;gBACAoI;YACF;QAEJxJ,QAAQtF,YAAYsF,MAAM;QAC1BJ,SAASlF,YAAYkF,OAAO;QAC5BjF;QACAiS,eAAelS,YAAYkS,aAAa;QACxCC,WAAWnS,YAAYmS,SAAS;IAClC;IAEA5R,2BAA2B,MAAMjB,OAAO8S,gCAAgC;IAExE,MAAMC,YAA6B;QACjC3L,KAAK/F;QACL2R,SAAS;QACTtR,WAAWH;QACXO,OAAOpB,YAAYoB,KAAK;QACxBsB,KAAKoN;QACLH,eAAe;IACjB;IAEA,IAAIlM,QAAAA,OAAAA,KAAAA,IAAAA,KAAMkG,YAAY,EAAE;QACtB,MAAMlG,KAAKkG,YAAY;IACzB;IAEAvG,OAAOiP;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3671, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/hot-middleware-client.ts"], "sourcesContent": ["import type {\n  NextRouter,\n  PrivateRouteInfo,\n} from '../../shared/lib/router/router'\nimport connect from './hot-reloader/pages/hot-reloader-pages'\nimport { sendMessage } from './hot-reloader/pages/websocket'\n\n// Define a local type for the window.next object\ninterface NextWindow {\n  next?: {\n    router?: NextRouter & {\n      components: { [pathname: string]: PrivateRouteInfo }\n    }\n  }\n  __nextDevClientId?: string\n  location: Location\n}\n\ndeclare const window: NextWindow\n\nlet reloading = false\n\nexport default () => {\n  const devClient = connect()\n\n  devClient.subscribeToHmrEvent((obj: any) => {\n    if (reloading) return\n\n    // Retrieve the router if it's available\n    const router = window.next?.router\n\n    // Determine if we're on an error page or the router is not initialized\n    const isOnErrorPage =\n      !router || router.pathname === '/404' || router.pathname === '/_error'\n\n    switch (obj.action) {\n      case 'reloadPage': {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-reload-page',\n            clientId: window.__nextDevClientId,\n          })\n        )\n        reloading = true\n        return window.location.reload()\n      }\n      case 'removedPage': {\n        const [page] = obj.data\n\n        // Check if the removed page is the current page\n        const isCurrentPage = page === router?.pathname\n\n        // We enter here if the removed page is currently being viewed\n        // or if we happen to be on an error page.\n        if (isCurrentPage || isOnErrorPage) {\n          sendMessage(\n            JSON.stringify({\n              event: 'client-removed-page',\n              clientId: window.__nextDevClientId,\n              page,\n            })\n          )\n          return window.location.reload()\n        }\n        return\n      }\n      case 'addedPage': {\n        const [page] = obj.data\n\n        // Check if the added page is the current page\n        const isCurrentPage = page === router?.pathname\n\n        // Check if the page component is not yet loaded\n        const isPageNotLoaded =\n          typeof router?.components?.[page] === 'undefined'\n\n        // We enter this block if the newly added page is the one currently being viewed\n        // but hasn't been loaded yet, or if we're on an error page.\n        if ((isCurrentPage && isPageNotLoaded) || isOnErrorPage) {\n          sendMessage(\n            JSON.stringify({\n              event: 'client-added-page',\n              clientId: window.__nextDevClientId,\n              page,\n            })\n          )\n          return window.location.reload()\n        }\n        return\n      }\n      case 'serverError':\n      case 'devPagesManifestUpdate':\n      case 'isrManifest':\n      case 'building':\n      case 'finishBuilding': {\n        return\n      }\n      default: {\n        throw new Error('Unexpected action ' + obj.action)\n      }\n    }\n  })\n\n  return devClient\n}\n"], "names": ["reloading", "devClient", "connect", "subscribeToHmrEvent", "obj", "window", "router", "next", "isOnErrorPage", "pathname", "action", "sendMessage", "JSON", "stringify", "event", "clientId", "__nextDevClientId", "location", "reload", "page", "data", "isCurrentPage", "isPageNotLoaded", "components", "Error"], "mappings": ";;;+BAsBA,WAAA;;;eAAA;;;;2EAlBoB;2BACQ;AAe5B,IAAIA,YAAY;MAEhB,WAAe;IACb,MAAMC,YAAYC,CAAAA,GAAAA,kBAAAA,OAAO;IAEzBD,UAAUE,mBAAmB,CAAC,CAACC;YAIdC;QAHf,IAAIL,WAAW;QAEf,wCAAwC;QACxC,MAAMM,SAAAA,CAASD,eAAAA,OAAOE,IAAI,KAAA,OAAA,KAAA,IAAXF,aAAaC,MAAM;QAElC,uEAAuE;QACvE,MAAME,gBACJ,CAACF,UAAUA,OAAOG,QAAQ,KAAK,UAAUH,OAAOG,QAAQ,KAAK;QAE/D,OAAQL,IAAIM,MAAM;YAChB,KAAK;gBAAc;oBACjBC,CAAAA,GAAAA,WAAAA,WAAW,EACTC,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,UAAUV,OAAOW,iBAAiB;oBACpC;oBAEFhB,YAAY;oBACZ,OAAOK,OAAOY,QAAQ,CAACC,MAAM;gBAC/B;YACA,KAAK;gBAAe;oBAClB,MAAM,CAACC,KAAK,GAAGf,IAAIgB,IAAI;oBAEvB,gDAAgD;oBAChD,MAAMC,gBAAgBF,SAAAA,CAASb,UAAAA,OAAAA,KAAAA,IAAAA,OAAQG,QAAQ;oBAE/C,8DAA8D;oBAC9D,0CAA0C;oBAC1C,IAAIY,iBAAiBb,eAAe;wBAClCG,CAAAA,GAAAA,WAAAA,WAAW,EACTC,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUV,OAAOW,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOd,OAAOY,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;gBAAa;wBAQPZ;oBAPT,MAAM,CAACa,KAAK,GAAGf,IAAIgB,IAAI;oBAEvB,8CAA8C;oBAC9C,MAAMC,gBAAgBF,SAAAA,CAASb,UAAAA,OAAAA,KAAAA,IAAAA,OAAQG,QAAQ;oBAE/C,gDAAgD;oBAChD,MAAMa,kBACJ,OAAA,CAAOhB,UAAAA,OAAAA,KAAAA,IAAAA,CAAAA,qBAAAA,OAAQiB,UAAU,KAAA,OAAA,KAAA,IAAlBjB,kBAAoB,CAACa,KAAK,MAAK;oBAExC,gFAAgF;oBAChF,4DAA4D;oBAC5D,IAAKE,iBAAiBC,mBAAoBd,eAAe;wBACvDG,CAAAA,GAAAA,WAAAA,WAAW,EACTC,KAAKC,SAAS,CAAC;4BACbC,OAAO;4BACPC,UAAUV,OAAOW,iBAAiB;4BAClCG;wBACF;wBAEF,OAAOd,OAAOY,QAAQ,CAACC,MAAM;oBAC/B;oBACA;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAkB;oBACrB;gBACF;YACA;gBAAS;oBACP,MAAM,OAAA,cAA4C,CAA5C,IAAIM,MAAM,uBAAuBpB,IAAIM,MAAM,GAA3C,qBAAA;+BAAA;oCAAA;sCAAA;oBAA2C;gBACnD;QACF;IACF;IAEA,OAAOT;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3773, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/on-demand-entries-client.ts"], "sourcesContent": ["import Router from '../router'\nimport { sendMessage } from './hot-reloader/pages/websocket'\n\nexport default async (page?: string) => {\n  // Never send pings when using Turbopack as it's not used.\n  // Pings were originally used to keep track of active routes in on-demand-entries with webpack.\n  if (process.env.TURBOPACK) {\n    return\n  }\n  if (page) {\n    // in AMP the router isn't initialized on the client and\n    // client-transitions don't occur so ping initial page\n    setInterval(() => {\n      sendMessage(JSON.stringify({ event: 'ping', page }))\n    }, 2500)\n  } else {\n    Router.ready(() => {\n      setInterval(() => {\n        // when notFound: true is returned we should use the notFoundPage\n        // as the Router.pathname will point to the 404 page but we want\n        // to ping the source page that returned notFound: true instead\n        const notFoundSrcPage = self.__NEXT_DATA__.notFoundSrcPage\n        const pathname =\n          (Router.pathname === '/404' || Router.pathname === '/_error') &&\n          notFoundSrcPage\n            ? notFoundSrcPage\n            : Router.pathname\n\n        sendMessage(JSON.stringify({ event: 'ping', page: pathname }))\n      }, 2500)\n    })\n  }\n}\n"], "names": ["page", "process", "env", "TURBOPACK", "setInterval", "sendMessage", "JSON", "stringify", "event", "Router", "ready", "notFoundSrcPage", "self", "__NEXT_DATA__", "pathname"], "mappings": "AAMMC,QAAQC,GAAG,CAACC,SAAS;;;;;+BAH3B,WAAA;;;eAAA;;;;iEAHmB;2BACS;MAE5B,WAAe,OAAOH;IACpB,0DAA0D;IAC1D,+FAA+F;IAC/F,wCAA2B;QACzB;IACF;;;AAwBF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3809, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/dev/fouc.ts"], "sourcesContent": ["// This wrapper function is used to safely select the best available function\n// to schedule removal of the no-FOUC styles workaround. requestAnimationFrame\n// is the ideal choice, but when used in iframes, there are no guarantees that\n// the callback will actually be called, which could stall the promise returned\n// from displayContent.\n//\n// See: https://www.vector-logic.com/blog/posts/on-request-animation-frame-and-embedded-iframes\nconst safeCallbackQueue = (callback: () => void) => {\n  if (window.requestAnimationFrame && window.self === window.top) {\n    window.requestAnimationFrame(callback)\n  } else {\n    window.setTimeout(callback)\n  }\n}\n\n// This function is used to remove Next.js' no-FOUC styles workaround for using\n// `style-loader` in development. It must be called before hydration, or else\n// rendering won't have the correct computed values in effects.\nexport function displayContent(): Promise<void> {\n  return new Promise((resolve) => {\n    safeCallbackQueue(function () {\n      for (\n        var x = document.querySelectorAll('[data-next-hide-fouc]'),\n          i = x.length;\n        i--;\n\n      ) {\n        x[i].parentNode!.removeChild(x[i])\n      }\n      resolve()\n    })\n  })\n}\n"], "names": ["displayContent", "safeCallback<PERSON><PERSON>ue", "callback", "window", "requestAnimationFrame", "self", "top", "setTimeout", "Promise", "resolve", "x", "document", "querySelectorAll", "i", "length", "parentNode", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,6EAA6E;AAC7E,8EAA8E;AAC9E,8EAA8E;AAC9E,+EAA+E;AAC/E,uBAAuB;AACvB,EAAE;AACF,+FAA+F;;;;+BAY/EA,kBAAAA;;;eAAAA;;;AAXhB,MAAMC,oBAAoB,CAACC;IACzB,IAAIC,OAAOC,qBAAqB,IAAID,OAAOE,IAAI,KAAKF,OAAOG,GAAG,EAAE;QAC9DH,OAAOC,qBAAqB,CAACF;IAC/B,OAAO;QACLC,OAAOI,UAAU,CAACL;IACpB;AACF;AAKO,SAASF;IACd,OAAO,IAAIQ,QAAQ,CAACC;QAClBR,kBAAkB;YAChB,IACE,IAAIS,IAAIC,SAASC,gBAAgB,CAAC,0BAChCC,IAAIH,EAAEI,MAAM,EACdD,KAEA;gBACAH,CAAC,CAACG,EAAE,CAACE,UAAU,CAAEC,WAAW,CAACN,CAAC,CAACG,EAAE;YACnC;YACAJ;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3855, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/page-bootstrap.ts"], "sourcesContent": ["import '../lib/require-instrumentation-client'\nimport { hydrate, router } from './'\nimport initOnDemandEntries from './dev/on-demand-entries-client'\nimport { displayContent } from './dev/fouc'\nimport {\n  connectHMR,\n  addMessageListener,\n} from './dev/hot-reloader/pages/websocket'\nimport {\n  assign,\n  urlQueryToSearchParams,\n} from '../shared/lib/router/utils/querystring'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../server/dev/hot-reloader-types'\nimport { RuntimeErrorHandler } from './dev/runtime-error-handler'\nimport { REACT_REFRESH_FULL_RELOAD_FROM_ERROR } from './dev/hot-reloader/shared'\nimport { performFullReload } from './dev/hot-reloader/pages/hot-reloader-pages'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\n\nexport function pageBootstrap(assetPrefix: string) {\n  connectHMR({ assetPrefix, path: '/_next/webpack-hmr' })\n\n  return hydrate({ beforeRender: displayContent }).then(() => {\n    initOnDemandEntries()\n\n    let reloading = false\n\n    addMessageListener((payload) => {\n      if (reloading) return\n      if ('action' in payload) {\n        switch (payload.action) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n            const { stack, message } = JSON.parse(payload.errorJSON)\n            const error = new Error(message)\n            error.stack = stack\n            throw error\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE: {\n            reloading = true\n            window.location.reload()\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.DEV_PAGES_MANIFEST_UPDATE: {\n            fetch(\n              `${assetPrefix}/_next/static/development/_devPagesManifest.json`\n            )\n              .then((res) => res.json())\n              .then((manifest) => {\n                window.__DEV_PAGES_MANIFEST = manifest\n              })\n              .catch((err) => {\n                console.log(`Failed to fetch devPagesManifest`, err)\n              })\n            break\n          }\n          default:\n            break\n        }\n      } else if ('event' in payload) {\n        switch (payload.event) {\n          case HMR_ACTIONS_SENT_TO_BROWSER.MIDDLEWARE_CHANGES: {\n            return window.location.reload()\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.CLIENT_CHANGES: {\n            // This is used in `../server/dev/turbopack-utils.ts`.\n            const isOnErrorPage = window.next.router.pathname === '/_error'\n            // On the error page we want to reload the page when a page was changed\n            if (isOnErrorPage) {\n              if (RuntimeErrorHandler.hadRuntimeError) {\n                console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              }\n              reloading = true\n              performFullReload(null)\n            }\n            break\n          }\n          case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ONLY_CHANGES: {\n            if (RuntimeErrorHandler.hadRuntimeError) {\n              console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n              performFullReload(null)\n            }\n\n            const { pages } = payload\n\n            // Make sure to reload when the dev-overlay is showing for an\n            // API route\n            // TODO: Fix `__NEXT_PAGE` type\n            if (pages.includes(router.query.__NEXT_PAGE as string)) {\n              return window.location.reload()\n            }\n\n            if (!router.clc && pages.includes(router.pathname)) {\n              console.log('Refreshing page data due to server-side change')\n              dispatcher.buildingIndicatorShow()\n              const clearIndicator = dispatcher.buildingIndicatorHide\n\n              router\n                .replace(\n                  router.pathname +\n                    '?' +\n                    String(\n                      assign(\n                        urlQueryToSearchParams(router.query),\n                        new URLSearchParams(location.search)\n                      )\n                    ),\n                  router.asPath,\n                  { scroll: false }\n                )\n                .catch(() => {\n                  // trigger hard reload when failing to refresh data\n                  // to show error overlay properly\n                  location.reload()\n                })\n                .finally(clearIndicator)\n            }\n            break\n          }\n          default:\n            break\n        }\n      }\n    })\n  })\n}\n"], "names": ["pageBootstrap", "assetPrefix", "connectHMR", "path", "hydrate", "beforeRender", "displayContent", "then", "initOnDemandEntries", "reloading", "addMessageListener", "payload", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "SERVER_ERROR", "stack", "message", "JSON", "parse", "errorJSON", "error", "Error", "RELOAD_PAGE", "window", "location", "reload", "DEV_PAGES_MANIFEST_UPDATE", "fetch", "res", "json", "manifest", "__DEV_PAGES_MANIFEST", "catch", "err", "console", "log", "event", "MIDDLEWARE_CHANGES", "CLIENT_CHANGES", "isOnErrorPage", "next", "router", "pathname", "RuntimeError<PERSON>andler", "hadRuntimeError", "warn", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "performFullReload", "SERVER_ONLY_CHANGES", "pages", "includes", "query", "__NEXT_PAGE", "clc", "dispatcher", "buildingIndicatorShow", "clearIndicator", "buildingIndicatorHide", "replace", "String", "assign", "urlQueryToSearchParams", "URLSearchParams", "search", "<PERSON><PERSON><PERSON>", "scroll", "finally"], "mappings": ";;;+BAkBgBA,iBAAAA;;;eAAAA;;;;;kBAjBgB;gFACA;sBACD;2BAIxB;6BAIA;kCACqC;qCACR;wBACiB;kCACnB;8BACP;AAEpB,SAASA,cAAcC,WAAmB;IAC/CC,CAAAA,GAAAA,WAAAA,UAAU,EAAC;QAAED;QAAaE,MAAM;IAAqB;IAErD,OAAOC,CAAAA,GAAAA,EAAAA,OAAO,EAAC;QAAEC,cAAcC,MAAAA,cAAc;IAAC,GAAGC,IAAI,CAAC;QACpDC,CAAAA,GAAAA,uBAAAA,OAAmB;QAEnB,IAAIC,YAAY;QAEhBC,CAAAA,GAAAA,WAAAA,kBAAkB,EAAC,CAACC;YAClB,IAAIF,WAAW;YACf,IAAI,YAAYE,SAAS;gBACvB,OAAQA,QAAQC,MAAM;oBACpB,KAAKC,kBAAAA,2BAA2B,CAACC,YAAY;wBAAE;4BAC7C,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAE,GAAGC,KAAKC,KAAK,CAACP,QAAQQ,SAAS;4BACvD,MAAMC,QAAQ,OAAA,cAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;uCAAA;4CAAA;8CAAA;4BAAiB;4BAC/BI,MAAML,KAAK,GAAGA;4BACd,MAAMK;wBACR;oBACA,KAAKP,kBAAAA,2BAA2B,CAACS,WAAW;wBAAE;4BAC5Cb,YAAY;4BACZc,OAAOC,QAAQ,CAACC,MAAM;4BACtB;wBACF;oBACA,KAAKZ,kBAAAA,2BAA2B,CAACa,yBAAyB;wBAAE;4BAC1DC,MACG,KAAE1B,cAAY,oDAEdM,IAAI,CAAC,CAACqB,MAAQA,IAAIC,IAAI,IACtBtB,IAAI,CAAC,CAACuB;gCACLP,OAAOQ,oBAAoB,GAAGD;4BAChC,GACCE,KAAK,CAAC,CAACC;gCACNC,QAAQC,GAAG,CAAE,oCAAmCF;4BAClD;4BACF;wBACF;oBACA;wBACE;gBACJ;YACF,OAAO,IAAI,WAAWtB,SAAS;gBAC7B,OAAQA,QAAQyB,KAAK;oBACnB,KAAKvB,kBAAAA,2BAA2B,CAACwB,kBAAkB;wBAAE;4BACnD,OAAOd,OAAOC,QAAQ,CAACC,MAAM;wBAC/B;oBACA,KAAKZ,kBAAAA,2BAA2B,CAACyB,cAAc;wBAAE;4BAC/C,sDAAsD;4BACtD,MAAMC,gBAAgBhB,OAAOiB,IAAI,CAACC,MAAM,CAACC,QAAQ,KAAK;4BACtD,uEAAuE;4BACvE,IAAIH,eAAe;gCACjB,IAAII,qBAAAA,mBAAmB,CAACC,eAAe,EAAE;oCACvCV,QAAQW,IAAI,CAACC,QAAAA,oCAAoC;gCACnD;gCACArC,YAAY;gCACZsC,CAAAA,GAAAA,kBAAAA,iBAAiB,EAAC;4BACpB;4BACA;wBACF;oBACA,KAAKlC,kBAAAA,2BAA2B,CAACmC,mBAAmB;wBAAE;4BACpD,IAAIL,qBAAAA,mBAAmB,CAACC,eAAe,EAAE;gCACvCV,QAAQW,IAAI,CAACC,QAAAA,oCAAoC;gCACjDC,CAAAA,GAAAA,kBAAAA,iBAAiB,EAAC;4BACpB;4BAEA,MAAM,EAAEE,KAAK,EAAE,GAAGtC;4BAElB,6DAA6D;4BAC7D,YAAY;4BACZ,+BAA+B;4BAC/B,IAAIsC,MAAMC,QAAQ,CAACT,EAAAA,MAAM,CAACU,KAAK,CAACC,WAAW,GAAa;gCACtD,OAAO7B,OAAOC,QAAQ,CAACC,MAAM;4BAC/B;4BAEA,IAAI,CAACgB,EAAAA,MAAM,CAACY,GAAG,IAAIJ,MAAMC,QAAQ,CAACT,EAAAA,MAAM,CAACC,QAAQ,GAAG;gCAClDR,QAAQC,GAAG,CAAC;gCACZmB,cAAAA,UAAU,CAACC,qBAAqB;gCAChC,MAAMC,iBAAiBF,cAAAA,UAAU,CAACG,qBAAqB;gCAEvDhB,EAAAA,MAAM,CACHiB,OAAO,CACNjB,EAAAA,MAAM,CAACC,QAAQ,GACb,MACAiB,OACEC,CAAAA,GAAAA,aAAAA,MAAM,EACJC,CAAAA,GAAAA,aAAAA,sBAAsB,EAACpB,EAAAA,MAAM,CAACU,KAAK,GACnC,IAAIW,gBAAgBtC,SAASuC,MAAM,KAGzCtB,EAAAA,MAAM,CAACuB,MAAM,EACb;oCAAEC,QAAQ;gCAAM,GAEjBjC,KAAK,CAAC;oCACL,mDAAmD;oCACnD,iCAAiC;oCACjCR,SAASC,MAAM;gCACjB,GACCyC,OAAO,CAACV;4BACb;4BACA;wBACF;oBACA;wBACE;gBACJ;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3986, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/node_modules/next/src/client/next-dev-turbopack.ts"], "sourcesContent": ["// TODO: Remove use of `any` type.\nimport { initialize, version, router, emitter } from './'\nimport initHMR from './dev/hot-middleware-client'\n\nimport { pageBootstrap } from './page-bootstrap'\n//@ts-expect-error requires \"moduleResolution\": \"node16\" in tsconfig.json and not .ts extension\nimport { connect } from '@vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts'\nimport type { TurbopackMsgToBrowser } from '../server/dev/hot-reloader-types'\n\nwindow.next = {\n  version: `${version}-turbo`,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n;(self as any).__next_set_public_path__ = () => {}\n;(self as any).__webpack_hash__ = ''\n\n// for the page loader\ndeclare let __turbopack_load__: any\n\nconst devClient = initHMR()\ninitialize({\n  devClient,\n})\n  .then(({ assetPrefix }) => {\n    // for the page loader\n    ;(self as any).__turbopack_load_page_chunks__ = (\n      page: string,\n      chunksData: any\n    ) => {\n      const chunkPromises = chunksData.map(__turbopack_load__)\n\n      Promise.all(chunkPromises).catch((err) =>\n        console.error('failed to load chunks for page ' + page, err)\n      )\n    }\n\n    connect({\n      addMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n        devClient.addTurbopackMessageListener(cb)\n      },\n      sendMessage: devClient.sendTurbopackMessage,\n      onUpdateError: devClient.handleUpdateError,\n    })\n\n    return pageBootstrap(assetPrefix)\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": ["window", "next", "version", "router", "emitter", "self", "__next_set_public_path__", "__webpack_hash__", "devClient", "initHMR", "initialize", "then", "assetPrefix", "__turbopack_load_page_chunks__", "page", "chunksData", "chunkPromises", "map", "__turbopack_load__", "Promise", "all", "catch", "err", "console", "error", "connect", "addMessageListener", "cb", "addTurbopackMessageListener", "sendMessage", "sendTurbopackMessage", "onUpdateError", "handleUpdateError", "pageBootstrap"], "mappings": "AAAA,kCAAkC;;;;;kBACmB;8EACjC;+BAEU;6BAEN;AAGxBA,OAAOC,IAAI,GAAG;IACZC,SAAU,KAAEA,EAAAA,OAAO,GAAC;IACpB,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA,EAAAA,MAAM;IACf;IACAC,SAAAA,EAAAA,OAAO;AACT;AACEC,KAAaC,wBAAwB,GAAG,KAAO;AAC/CD,KAAaE,gBAAgB,GAAG;AAKlC,MAAMC,YAAYC,CAAAA,GAAAA,qBAAAA,OAAO;AACzBC,CAAAA,GAAAA,EAAAA,UAAU,EAAC;IACTF;AACF,GACGG,IAAI,CAAC,CAAA;QAAC,EAAEC,WAAW,EAAE,GAAA;IACpB,sBAAsB;;IACpBP,KAAaQ,8BAA8B,GAAG,CAC9CC,MACAC;QAEA,MAAMC,gBAAgBD,WAAWE,GAAG,CAACC;QAErCC,QAAQC,GAAG,CAACJ,eAAeK,KAAK,CAAC,CAACC,MAChCC,QAAQC,KAAK,CAAC,oCAAoCV,MAAMQ;IAE5D;IAEAG,CAAAA,GAAAA,aAAAA,OAAO,EAAC;QACNC,oBAAmBC,EAAwC;YACzDnB,UAAUoB,2BAA2B,CAACD;QACxC;QACAE,aAAarB,UAAUsB,oBAAoB;QAC3CC,eAAevB,UAAUwB,iBAAiB;IAC5C;IAEA,OAAOC,CAAAA,GAAAA,eAAAA,aAAa,EAACrB;AACvB,GACCS,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC", "ignoreList": [0], "debugId": null}}]}