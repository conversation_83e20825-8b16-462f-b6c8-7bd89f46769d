const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  phone: {
    type: String,
    trim: true
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: {
      type: String,
      default: 'India'
    }
  },
  dateOfBirth: Date,
  profileImage: {
    type: String,
    default: ''
  },
  role: {
    type: String,
    enum: ['user', 'volunteer', 'admin', 'shelter-staff'],
    default: 'user'
  },
  preferences: {
    petTypes: [{
      type: String,
      enum: ['dog', 'cat', 'bird', 'rabbit', 'other']
    }],
    size: [{
      type: String,
      enum: ['small', 'medium', 'large', 'extra-large']
    }],
    age: {
      min: Number,
      max: Number
    },
    location: {
      maxDistance: {
        type: Number,
        default: 50 // km
      },
      city: String,
      state: String
    }
  },
  adoptionHistory: [{
    pet: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Pet'
    },
    adoptionDate: Date,
    status: {
      type: String,
      enum: ['active', 'returned', 'completed']
    }
  }],
  savedPets: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pet'
  }],
  volunteerInfo: {
    skills: [String],
    availability: {
      days: [{
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
      }],
      timeSlots: [{
        start: String,
        end: String
      }]
    },
    experience: String,
    backgroundCheck: {
      completed: {
        type: Boolean,
        default: false
      },
      date: Date,
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected']
      }
    }
  },
  notifications: {
    email: {
      newPets: {
        type: Boolean,
        default: true
      },
      applicationUpdates: {
        type: Boolean,
        default: true
      },
      events: {
        type: Boolean,
        default: true
      }
    },
    sms: {
      urgent: {
        type: Boolean,
        default: false
      }
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date,
  lastLogin: Date
}, {
  timestamps: true
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Get full name
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ role: 1 });
userSchema.index({ 'address.city': 1, 'address.state': 1 });

module.exports = mongoose.model('User', userSchema);
