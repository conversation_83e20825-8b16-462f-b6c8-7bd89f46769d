import { NextRequest } from 'next/server';
import { GET, POST } from '../pets/route';
import { db } from '@/lib/database';

// Mock the database
jest.mock('@/lib/database', () => ({
  db: {
    getPets: jest.fn(),
    createPet: jest.fn(),
  },
}));

const mockDb = db as jest.Mocked<typeof db>;

describe('/api/pets', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/pets', () => {
    it('returns pets with default filters', async () => {
      const mockPets = [
        {
          id: '1',
          name: 'Buddy',
          type: 'dog',
          breed: 'Golden Retriever',
          status: 'available',
        },
      ];

      mockDb.getPets.mockResolvedValue({
        pets: mockPets,
        total: 1,
      });

      const request = new NextRequest('http://localhost:3000/api/pets');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(mockPets);
      expect(data.pagination).toEqual({
        total: 1,
        limit: 20,
        offset: 0,
        hasMore: false,
      });
    });

    it('applies query filters correctly', async () => {
      const mockPets = [
        {
          id: '1',
          name: 'Buddy',
          type: 'dog',
          breed: 'Golden Retriever',
          status: 'available',
        },
      ];

      mockDb.getPets.mockResolvedValue({
        pets: mockPets,
        total: 1,
      });

      const request = new NextRequest(
        'http://localhost:3000/api/pets?type=dog&size=large&limit=10&offset=5'
      );
      const response = await GET(request);

      expect(mockDb.getPets).toHaveBeenCalledWith({
        type: 'dog',
        size: 'large',
        age: undefined,
        location: undefined,
        status: 'available',
        limit: 10,
        offset: 5,
      });
    });

    it('handles database errors', async () => {
      mockDb.getPets.mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/pets');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to fetch pets');
    });
  });

  describe('POST /api/pets', () => {
    it('creates a new pet with valid data', async () => {
      const newPet = {
        id: '1',
        name: 'Buddy',
        type: 'dog',
        breed: 'Golden Retriever',
        age: { value: 2, unit: 'years' },
        gender: 'male',
        size: 'large',
        description: 'Friendly dog',
        status: 'available',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockDb.createPet.mockResolvedValue(newPet as any);

      const requestBody = {
        name: 'Buddy',
        type: 'dog',
        breed: 'Golden Retriever',
        age: { value: 2, unit: 'years' },
        gender: 'male',
        size: 'large',
        description: 'Friendly dog',
      };

      const request = new NextRequest('http://localhost:3000/api/pets', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(newPet);
      expect(data.message).toBe('Pet created successfully');
    });

    it('validates required fields', async () => {
      const requestBody = {
        name: 'Buddy',
        // Missing required fields
      };

      const request = new NextRequest('http://localhost:3000/api/pets', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Missing required field');
    });

    it('handles database errors during creation', async () => {
      mockDb.createPet.mockRejectedValue(new Error('Database error'));

      const requestBody = {
        name: 'Buddy',
        type: 'dog',
        breed: 'Golden Retriever',
        age: { value: 2, unit: 'years' },
        gender: 'male',
        size: 'large',
        description: 'Friendly dog',
      };

      const request = new NextRequest('http://localhost:3000/api/pets', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create pet');
    });

    it('applies default values for optional fields', async () => {
      const newPet = {
        id: '1',
        name: 'Buddy',
        type: 'dog',
        breed: 'Golden Retriever',
        age: { value: 2, unit: 'years' },
        gender: 'male',
        size: 'large',
        description: 'Friendly dog',
        status: 'available',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      mockDb.createPet.mockResolvedValue(newPet as any);

      const requestBody = {
        name: 'Buddy',
        type: 'dog',
        breed: 'Golden Retriever',
        age: { value: 2, unit: 'years' },
        gender: 'male',
        size: 'large',
        description: 'Friendly dog',
        // Optional fields not provided
      };

      const request = new NextRequest('http://localhost:3000/api/pets', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      expect(mockDb.createPet).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Buddy',
          type: 'dog',
          breed: 'Golden Retriever',
          color: 'Unknown',
          status: 'available',
          adoptionFee: 0,
          specialNeeds: false,
          views: 0,
          likes: 0,
          featured: false,
        })
      );
    });
  });
});
