'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant = 'primary', 
    size = 'md', 
    isLoading = false,
    leftIcon,
    rightIcon,
    children, 
    disabled,
    ...props 
  }, ref) => {
    const baseStyles = `
      inline-flex items-center justify-center gap-2 rounded-lg font-medium 
      transition-all duration-200 focus-ring disabled:opacity-50 disabled:cursor-not-allowed
      active:scale-95 hover:shadow-md
    `;

    const variants = {
      primary: `
        bg-gradient-to-r from-orange-500 to-orange-600 text-white 
        hover:from-orange-600 hover:to-orange-700 shadow-warm
      `,
      secondary: `
        bg-gradient-to-r from-green-500 to-green-600 text-white 
        hover:from-green-600 hover:to-green-700
      `,
      outline: `
        border-2 border-orange-500 text-orange-600 bg-transparent 
        hover:bg-orange-50 hover:border-orange-600
      `,
      ghost: `
        text-neutral-700 bg-transparent hover:bg-neutral-100
      `,
      danger: `
        bg-gradient-to-r from-red-500 to-red-600 text-white 
        hover:from-red-600 hover:to-red-700
      `
    };

    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
      xl: 'px-8 py-4 text-xl'
    };

    return (
      <button
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          className
        )}
        ref={ref}
        disabled={disabled || isLoading}
        {...props}
      >
        {isLoading ? (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
        ) : leftIcon}
        {children}
        {!isLoading && rightIcon}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button };
