'use client';

import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  Heart, 
  Users, 
  DollarSign, 
  Shield, 
  Home,
  HelpCircle,
  MessageCircle,
  Phone
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Input,
  Navigation,
  Footer
} from '@/components';

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'adoption' | 'volunteer' | 'donation' | 'general' | 'medical' | 'process';
  popular: boolean;
}

interface Category {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  count: number;
}

const categories: Category[] = [
  {
    id: 'adoption',
    name: 'Adoption Process',
    description: 'Questions about adopting pets',
    icon: <Heart className="w-5 h-5" />,
    color: 'bg-red-100 text-red-600',
    count: 12
  },
  {
    id: 'volunteer',
    name: 'Volunteering',
    description: 'Information about volunteer opportunities',
    icon: <Users className="w-5 h-5" />,
    color: 'bg-blue-100 text-blue-600',
    count: 8
  },
  {
    id: 'donation',
    name: 'Donations',
    description: 'Questions about donations and sponsorship',
    icon: <DollarSign className="w-5 h-5" />,
    color: 'bg-green-100 text-green-600',
    count: 6
  },
  {
    id: 'medical',
    name: 'Pet Health',
    description: 'Medical care and health questions',
    icon: <Shield className="w-5 h-5" />,
    color: 'bg-purple-100 text-purple-600',
    count: 10
  },
  {
    id: 'process',
    name: 'Procedures',
    description: 'How our processes work',
    icon: <Home className="w-5 h-5" />,
    color: 'bg-orange-100 text-orange-600',
    count: 7
  },
  {
    id: 'general',
    name: 'General',
    description: 'Other common questions',
    icon: <HelpCircle className="w-5 h-5" />,
    color: 'bg-neutral-100 text-neutral-600',
    count: 9
  }
];

const faqs: FAQ[] = [
  {
    id: '1',
    question: 'How long does the adoption process take?',
    answer: 'The adoption process typically takes 3-7 days from application to taking your pet home. This includes application review, meet-and-greet sessions, home visit (if required), and final paperwork. Emergency adoptions for senior pets or special needs animals may be expedited.',
    category: 'adoption',
    popular: true
  },
  {
    id: '2',
    question: 'What is the adoption fee and what does it cover?',
    answer: 'Adoption fees vary by animal type and age, typically ranging from ₹2,000-₹6,000. The fee covers spaying/neutering, vaccinations, microchipping, health check, and basic training. This fee helps us continue rescuing and caring for more animals.',
    category: 'adoption',
    popular: true
  },
  {
    id: '3',
    question: 'Can I volunteer if I have no experience with animals?',
    answer: 'Absolutely! We welcome volunteers of all experience levels. We provide comprehensive training for all volunteer roles, from basic animal care to specialized tasks. Many of our best volunteers started with no prior experience.',
    category: 'volunteer',
    popular: true
  },
  {
    id: '4',
    question: 'Do you accept donations of pet supplies?',
    answer: 'Yes! We gratefully accept donations of pet food, toys, blankets, leashes, carriers, and cleaning supplies. Please check our current needs list on our website or call before bringing large donations. All items should be new or gently used.',
    category: 'donation',
    popular: false
  },
  {
    id: '5',
    question: 'Are all pets spayed/neutered before adoption?',
    answer: 'Yes, all pets are spayed or neutered before adoption unless they are too young for the procedure. In rare cases where a pet is adopted before the procedure, adopters sign an agreement to complete it within 30 days at our partner clinics.',
    category: 'medical',
    popular: true
  },
  {
    id: '6',
    question: 'What if my adopted pet doesn\'t get along with my other pets?',
    answer: 'We offer a 30-day adjustment period where you can return the pet if there are serious compatibility issues. We also provide free behavioral consultation during this period to help with the transition. Our goal is successful, permanent placements.',
    category: 'process',
    popular: false
  },
  {
    id: '7',
    question: 'Do you have age restrictions for adopters?',
    answer: 'Adopters must be at least 21 years old and able to provide proof of stable housing and income. We don\'t have an upper age limit, but we do consider the adopter\'s ability to care for the pet throughout its lifetime.',
    category: 'adoption',
    popular: false
  },
  {
    id: '8',
    question: 'How often do you need volunteers?',
    answer: 'We need volunteers year-round! Most volunteer positions require a commitment of 4-6 hours per week for at least 6 months. We also have opportunities for one-time event volunteers and seasonal helpers during busy periods.',
    category: 'volunteer',
    popular: false
  },
  {
    id: '9',
    question: 'Are donations tax-deductible?',
    answer: 'Yes! Animal Heaven is a registered 501(c)(3) non-profit organization. All donations are tax-deductible under Section 80G. We provide official receipts for all donations, and annual tax summaries for regular donors.',
    category: 'donation',
    popular: true
  },
  {
    id: '10',
    question: 'What vaccinations do pets receive?',
    answer: 'All pets receive age-appropriate core vaccinations including rabies, DHPP for dogs, and FVRCP for cats. Additional vaccines may be given based on the pet\'s health history and risk factors. Complete vaccination records are provided at adoption.',
    category: 'medical',
    popular: false
  },
  {
    id: '11',
    question: 'Can I meet a pet before applying for adoption?',
    answer: 'Yes! We encourage meet-and-greet sessions before adoption. You can visit during our open hours or schedule a private appointment. For families with children or other pets, we require a meet-and-greet as part of the adoption process.',
    category: 'process',
    popular: true
  },
  {
    id: '12',
    question: 'What happens if I can no longer care for my adopted pet?',
    answer: 'We have a lifetime return policy. If you can no longer care for your adopted pet for any reason, we will take them back. We ask that you contact us first to see if we can help resolve any issues before returning the pet.',
    category: 'general',
    popular: false
  }
];

const FAQPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const [filteredFAQs, setFilteredFAQs] = useState(faqs);

  React.useEffect(() => {
    let filtered = faqs.filter(faq => {
      const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
      const matchesSearch = searchQuery === '' || 
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
      
      return matchesCategory && matchesSearch;
    });

    // Sort by popular first, then alphabetically
    filtered.sort((a, b) => {
      if (a.popular && !b.popular) return -1;
      if (!a.popular && b.popular) return 1;
      return a.question.localeCompare(b.question);
    });

    setFilteredFAQs(filtered);
  }, [selectedCategory, searchQuery]);

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const popularFAQs = faqs.filter(faq => faq.popular);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Find answers to common questions about pet adoption, volunteering, donations, and our services. 
            Can't find what you're looking for? Contact us directly!
          </p>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-sm text-neutral-600">FAQ Articles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">95%</div>
              <div className="text-sm text-neutral-600">Questions Answered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">6</div>
              <div className="text-sm text-neutral-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">24/7</div>
              <div className="text-sm text-neutral-600">Available</div>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="mb-12">
          <Card variant="warm" className="max-w-2xl mx-auto">
            <CardContent className="p-6">
              <Input
                placeholder="Search frequently asked questions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
                variant="default"
                className="text-lg"
              />
            </CardContent>
          </Card>
        </div>

        {/* Popular FAQs */}
        {searchQuery === '' && selectedCategory === 'all' && (
          <section className="mb-16">
            <div className="text-center mb-8">
              <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                Most Popular Questions
              </h2>
              <p className="text-lg text-neutral-600">
                The questions we get asked most often
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {popularFAQs.map((faq) => (
                <Card 
                  key={faq.id} 
                  variant="elevated" 
                  className="cursor-pointer hover:scale-105 transition-all duration-300"
                  onClick={() => toggleFAQ(faq.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="font-semibold text-lg pr-4">{faq.question}</h3>
                      {expandedFAQ === faq.id ? (
                        <ChevronUp className="w-5 h-5 text-neutral-500 flex-shrink-0" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-neutral-500 flex-shrink-0" />
                      )}
                    </div>
                    
                    {expandedFAQ === faq.id && (
                      <div className="text-neutral-600 leading-relaxed">
                        {faq.answer}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Categories */}
        <section className="mb-12">
          <div className="text-center mb-8">
            <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
              Browse by Category
            </h2>
            <p className="text-lg text-neutral-600">
              Find questions organized by topic
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            {categories.map((category) => (
              <Card 
                key={category.id}
                variant={selectedCategory === category.id ? 'warm' : 'outlined'}
                className="cursor-pointer transition-all duration-300 hover:scale-105"
                onClick={() => setSelectedCategory(category.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${category.color}`}>
                      {category.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold">{category.name}</h3>
                      <p className="text-neutral-600 text-sm">{category.description}</p>
                    </div>
                    <div className="text-sm text-neutral-500">
                      {category.count}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button 
              variant={selectedCategory === 'all' ? 'primary' : 'outline'}
              onClick={() => setSelectedCategory('all')}
            >
              View All Questions
            </Button>
          </div>
        </section>

        {/* FAQ List */}
        <section className="mb-16">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="font-primary text-2xl font-bold text-neutral-900 mb-2">
                {selectedCategory === 'all' ? 'All Questions' : categories.find(c => c.id === selectedCategory)?.name}
              </h2>
              <p className="text-neutral-600">
                {filteredFAQs.length} questions found
                {searchQuery && ` for "${searchQuery}"`}
              </p>
            </div>
          </div>

          {filteredFAQs.length > 0 ? (
            <div className="space-y-4">
              {filteredFAQs.map((faq) => (
                <Card
                  key={faq.id}
                  variant="outlined"
                  className="cursor-pointer hover:shadow-lg transition-all duration-300"
                  onClick={() => toggleFAQ(faq.id)}
                >
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1 pr-4">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">{faq.question}</h3>
                          {faq.popular && (
                            <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full">
                              Popular
                            </span>
                          )}
                        </div>

                        {expandedFAQ === faq.id && (
                          <div className="text-neutral-600 leading-relaxed mt-4 pt-4 border-t border-neutral-200">
                            {faq.answer}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          categories.find(c => c.id === faq.category)?.color || 'bg-neutral-100 text-neutral-600'
                        }`}>
                          {categories.find(c => c.id === faq.category)?.name}
                        </span>
                        {expandedFAQ === faq.id ? (
                          <ChevronUp className="w-5 h-5 text-neutral-500" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-neutral-500" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card variant="outlined" className="text-center py-12">
              <CardContent>
                <HelpCircle className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="font-semibold text-neutral-700 mb-2">No questions found</h3>
                <p className="text-neutral-500 mb-4">
                  Try adjusting your search terms or browse a different category.
                </p>
                <Button variant="outline" onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                }}>
                  Clear Search
                </Button>
              </CardContent>
            </Card>
          )}
        </section>

        {/* Still Have Questions */}
        <section className="mb-16">
          <Card variant="warm" className="max-w-4xl mx-auto">
            <CardContent className="p-8 text-center">
              <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                Still Have Questions?
              </h2>
              <p className="text-lg text-neutral-600 mb-6">
                Can't find the answer you're looking for? Our friendly team is here to help!
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Live Chat</h3>
                  <p className="text-neutral-600 text-sm mb-3">
                    Chat with our support team in real-time
                  </p>
                  <Button variant="outline" size="sm">
                    Start Chat
                  </Button>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Phone className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Call Us</h3>
                  <p className="text-neutral-600 text-sm mb-3">
                    Speak directly with our team
                  </p>
                  <Button variant="outline" size="sm">
                    +91 98765 43210
                  </Button>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageCircle className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Contact Form</h3>
                  <p className="text-neutral-600 text-sm mb-3">
                    Send us a detailed message
                  </p>
                  <Button variant="outline" size="sm">
                    Contact Us
                  </Button>
                </div>
              </div>

              <div className="text-center">
                <Button variant="primary" size="lg">
                  Get Help Now
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default FAQPage;
