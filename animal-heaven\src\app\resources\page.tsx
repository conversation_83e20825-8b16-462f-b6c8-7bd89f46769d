'use client';

import React, { useState } from 'react';
import { 
  BookOpen, 
  Download, 
  Play, 
  Heart, 
  Stethoscope, 
  Home, 
  Utensils, 
  Scissors,
  Shield,
  Users,
  Calendar,
  Search,
  Filter,
  Star,
  Clock,
  Eye
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge, 
  Input,
  Navigation,
  Footer
} from '@/components';

interface Resource {
  id: string;
  title: string;
  description: string;
  category: 'health' | 'nutrition' | 'training' | 'grooming' | 'safety' | 'general';
  type: 'article' | 'video' | 'guide' | 'checklist';
  readTime: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  featured: boolean;
  downloadable: boolean;
  views: number;
  rating: number;
  lastUpdated: string;
}

interface Category {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  color: string;
  count: number;
}

const categories: Category[] = [
  {
    id: 'health',
    name: 'Health & Medical',
    icon: <Stethoscope className="w-6 h-6" />,
    description: 'Medical care, vaccinations, and health monitoring',
    color: 'text-red-600 bg-red-100',
    count: 24
  },
  {
    id: 'nutrition',
    name: 'Nutrition & Diet',
    icon: <Utensils className="w-6 h-6" />,
    description: 'Feeding guidelines, diet plans, and nutrition tips',
    color: 'text-green-600 bg-green-100',
    count: 18
  },
  {
    id: 'training',
    name: 'Training & Behavior',
    icon: <Users className="w-6 h-6" />,
    description: 'Training techniques, behavior modification, and socialization',
    color: 'text-blue-600 bg-blue-100',
    count: 32
  },
  {
    id: 'grooming',
    name: 'Grooming & Care',
    icon: <Scissors className="w-6 h-6" />,
    description: 'Grooming techniques, hygiene, and daily care routines',
    color: 'text-purple-600 bg-purple-100',
    count: 15
  },
  {
    id: 'safety',
    name: 'Safety & Emergency',
    icon: <Shield className="w-6 h-6" />,
    description: 'Pet-proofing, emergency care, and safety protocols',
    color: 'text-orange-600 bg-orange-100',
    count: 12
  },
  {
    id: 'general',
    name: 'General Care',
    icon: <Home className="w-6 h-6" />,
    description: 'Basic pet care, housing, and daily routines',
    color: 'text-neutral-600 bg-neutral-100',
    count: 21
  }
];

const sampleResources: Resource[] = [
  {
    id: '1',
    title: 'Complete Guide to Puppy Vaccination Schedule',
    description: 'Essential vaccination timeline and tips for keeping your puppy healthy during their first year.',
    category: 'health',
    type: 'guide',
    readTime: 8,
    difficulty: 'beginner',
    featured: true,
    downloadable: true,
    views: 2456,
    rating: 4.8,
    lastUpdated: '2024-01-15'
  },
  {
    id: '2',
    title: 'How to Train Your Dog to Walk on a Leash',
    description: 'Step-by-step video tutorial for teaching proper leash walking techniques.',
    category: 'training',
    type: 'video',
    readTime: 12,
    difficulty: 'beginner',
    featured: true,
    downloadable: false,
    views: 3421,
    rating: 4.9,
    lastUpdated: '2024-01-20'
  },
  {
    id: '3',
    title: 'Cat Nutrition: Age-Specific Feeding Guidelines',
    description: 'Comprehensive nutrition guide covering feeding requirements from kitten to senior cat.',
    category: 'nutrition',
    type: 'article',
    readTime: 6,
    difficulty: 'intermediate',
    featured: false,
    downloadable: true,
    views: 1876,
    rating: 4.7,
    lastUpdated: '2024-01-10'
  },
  {
    id: '4',
    title: 'Emergency First Aid for Pets Checklist',
    description: 'Quick reference checklist for handling common pet emergencies until veterinary care is available.',
    category: 'safety',
    type: 'checklist',
    readTime: 3,
    difficulty: 'beginner',
    featured: true,
    downloadable: true,
    views: 4123,
    rating: 4.9,
    lastUpdated: '2024-01-25'
  },
  {
    id: '5',
    title: 'Professional Grooming Techniques at Home',
    description: 'Learn professional grooming techniques you can safely perform at home.',
    category: 'grooming',
    type: 'video',
    readTime: 15,
    difficulty: 'intermediate',
    featured: false,
    downloadable: false,
    views: 1234,
    rating: 4.6,
    lastUpdated: '2024-01-12'
  },
  {
    id: '6',
    title: 'Creating a Pet-Safe Home Environment',
    description: 'Complete guide to pet-proofing your home and creating a safe environment for your new pet.',
    category: 'general',
    type: 'guide',
    readTime: 10,
    difficulty: 'beginner',
    featured: false,
    downloadable: true,
    views: 2987,
    rating: 4.8,
    lastUpdated: '2024-01-18'
  }
];

const ResourcesPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredResources, setFilteredResources] = useState(sampleResources);

  React.useEffect(() => {
    let filtered = sampleResources.filter(resource => {
      const matchesCategory = selectedCategory === 'all' || resource.category === selectedCategory;
      const matchesType = selectedType === 'all' || resource.type === selectedType;
      const matchesSearch = searchQuery === '' || 
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      return matchesCategory && matchesType && matchesSearch;
    });

    setFilteredResources(filtered);
  }, [selectedCategory, selectedType, searchQuery]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Play className="w-4 h-4" />;
      case 'guide': return <BookOpen className="w-4 h-4" />;
      case 'checklist': return <Download className="w-4 h-4" />;
      default: return <BookOpen className="w-4 h-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-yellow-600 bg-yellow-100';
      case 'advanced': return 'text-red-600 bg-red-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            Pet Care Resources
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Everything you need to know about caring for your pet. From basic care guides to advanced training techniques, 
            our comprehensive resources help you provide the best care for your furry family members.
          </p>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">120+</div>
              <div className="text-sm text-neutral-600">Expert Articles</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">45+</div>
              <div className="text-sm text-neutral-600">Video Tutorials</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">30+</div>
              <div className="text-sm text-neutral-600">Downloadable Guides</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">50K+</div>
              <div className="text-sm text-neutral-600">Monthly Readers</div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-12">
          <Card variant="warm">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search resources..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    leftIcon={<Search className="w-4 h-4" />}
                    variant="default"
                  />
                </div>
                <div className="flex gap-4">
                  <select
                    value={selectedType}
                    onChange={(e) => setSelectedType(e.target.value)}
                    className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="article">Articles</option>
                    <option value="video">Videos</option>
                    <option value="guide">Guides</option>
                    <option value="checklist">Checklists</option>
                  </select>
                  <Button variant="outline" leftIcon={<Filter className="w-4 h-4" />}>
                    Filter
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Categories */}
        <section className="mb-12">
          <div className="text-center mb-8">
            <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
              Browse by Category
            </h2>
            <p className="text-lg text-neutral-600">
              Find resources organized by topic to quickly locate what you need
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {categories.map((category) => (
              <Card 
                key={category.id}
                variant={selectedCategory === category.id ? 'warm' : 'elevated'}
                className="cursor-pointer transition-all duration-300 hover:scale-105"
                onClick={() => setSelectedCategory(category.id)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-4 mb-3">
                    <div className={`p-3 rounded-lg ${category.color}`}>
                      {category.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{category.name}</h3>
                      <Badge variant="outline" size="sm">
                        {category.count} resources
                      </Badge>
                    </div>
                  </div>
                  <p className="text-neutral-600 text-sm">{category.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center">
            <Button 
              variant={selectedCategory === 'all' ? 'primary' : 'outline'}
              onClick={() => setSelectedCategory('all')}
            >
              View All Categories
            </Button>
          </div>
        </section>

        {/* Featured Resources */}
        <section className="mb-12">
          <div className="text-center mb-8">
            <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
              Featured Resources
            </h2>
            <p className="text-lg text-neutral-600">
              Our most popular and highly-rated pet care resources
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredResources.filter(resource => resource.featured).map((resource) => (
              <Card key={resource.id} variant="elevated" className="group hover:scale-105 transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(resource.type)}
                      <Badge variant="primary" size="sm" className="capitalize">
                        {resource.type}
                      </Badge>
                    </div>
                    {resource.downloadable && (
                      <Download className="w-4 h-4 text-green-600" />
                    )}
                  </div>

                  <h3 className="font-semibold text-lg mb-2 group-hover:text-blue-600 transition-colors">
                    {resource.title}
                  </h3>

                  <p className="text-neutral-600 text-sm mb-4 leading-relaxed">
                    {resource.description}
                  </p>

                  <div className="flex items-center gap-4 mb-4 text-xs text-neutral-500">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{resource.readTime} min read</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      <span>{resource.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span>{resource.rating}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge
                      variant="outline"
                      size="sm"
                      className={`capitalize ${getDifficultyColor(resource.difficulty)}`}
                    >
                      {resource.difficulty}
                    </Badge>
                    <Button variant="outline" size="sm">
                      {resource.type === 'video' ? 'Watch' : 'Read'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* All Resources */}
        <section className="mb-12">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-2">
                All Resources
              </h2>
              <p className="text-neutral-600">
                {filteredResources.length} resources found
                {selectedCategory !== 'all' && ` in ${categories.find(c => c.id === selectedCategory)?.name}`}
              </p>
            </div>
            <div className="flex gap-2">
              <select className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                <option>Sort by Popularity</option>
                <option>Sort by Date</option>
                <option>Sort by Rating</option>
                <option>Sort by Title</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredResources.map((resource) => (
              <Card key={resource.id} variant="outlined" className="group hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(resource.type)}
                      <Badge variant="outline" size="sm" className="capitalize">
                        {resource.type}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      {resource.downloadable && (
                        <Download className="w-4 h-4 text-green-600" />
                      )}
                      {resource.featured && (
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                  </div>

                  <h3 className="font-semibold text-lg mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
                    {resource.title}
                  </h3>

                  <p className="text-neutral-600 text-sm mb-4 leading-relaxed line-clamp-3">
                    {resource.description}
                  </p>

                  <div className="flex items-center gap-4 mb-4 text-xs text-neutral-500">
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{resource.readTime} min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      <span>{resource.views.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-500" />
                      <span>{resource.rating}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Badge
                      variant="outline"
                      size="sm"
                      className={`capitalize ${getDifficultyColor(resource.difficulty)}`}
                    >
                      {resource.difficulty}
                    </Badge>
                    <Button variant="outline" size="sm">
                      {resource.type === 'video' ? 'Watch' : 'Read'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredResources.length === 0 && (
            <Card variant="outlined" className="text-center py-12">
              <CardContent>
                <BookOpen className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="font-semibold text-neutral-700 mb-2">No resources found</h3>
                <p className="text-neutral-500 mb-4">
                  Try adjusting your search terms or filters to find more resources.
                </p>
                <Button variant="outline" onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('all');
                  setSelectedType('all');
                }}>
                  Clear Filters
                </Button>
              </CardContent>
            </Card>
          )}
        </section>

        {/* Newsletter Signup */}
        <section className="mb-12">
          <Card variant="warm" className="max-w-4xl mx-auto">
            <CardContent className="p-8 text-center">
              <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                Stay Updated with New Resources
              </h2>
              <p className="text-lg text-neutral-600 mb-6">
                Get the latest pet care tips, guides, and resources delivered to your inbox weekly.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1"
                />
                <Button variant="primary">
                  Subscribe
                </Button>
              </div>
              <p className="text-sm text-neutral-500 mt-4">
                Join 10,000+ pet parents who trust our expert advice
              </p>
            </CardContent>
          </Card>
        </section>

        {/* Expert Contributors */}
        <section className="mb-12">
          <div className="text-center mb-8">
            <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
              Expert Contributors
            </h2>
            <p className="text-lg text-neutral-600">
              Our resources are created by certified veterinarians and animal behavior specialists
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card variant="elevated">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Stethoscope className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Dr. Sarah Johnson</h3>
                <p className="text-neutral-600 text-sm mb-3">Veterinarian, 15+ years experience</p>
                <p className="text-neutral-500 text-xs">
                  Specializes in small animal medicine and preventive care
                </p>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Mark Thompson</h3>
                <p className="text-neutral-600 text-sm mb-3">Certified Animal Behaviorist</p>
                <p className="text-neutral-500 text-xs">
                  Expert in dog training and behavioral modification
                </p>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardContent className="p-6 text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Heart className="w-8 h-8 text-orange-600" />
                </div>
                <h3 className="font-semibold text-lg mb-2">Lisa Chen</h3>
                <p className="text-neutral-600 text-sm mb-3">Pet Nutrition Specialist</p>
                <p className="text-neutral-500 text-xs">
                  Certified in animal nutrition and dietary planning
                </p>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default ResourcesPage;
