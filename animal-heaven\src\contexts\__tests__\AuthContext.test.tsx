import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '../AuthContext';

// Test component that uses the auth context
const TestComponent = () => {
  const { user, isAuthenticated, login, logout, register } = useAuth();

  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      {user && (
        <div data-testid="user-info">
          {user.firstName} {user.lastName} - {user.email}
        </div>
      )}
      <button
        data-testid="login-btn"
        onClick={() => login('<EMAIL>', 'password')}
      >
        Login
      </button>
      <button data-testid="logout-btn" onClick={logout}>
        Logout
      </button>
      <button
        data-testid="register-btn"
        onClick={() =>
          register({
            email: '<EMAIL>',
            password: 'password',
            firstName: 'New',
            lastName: 'User',
          })
        }
      >
        Register
      </button>
    </div>
  );
};

const renderWithAuthProvider = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  );
};

describe('AuthContext', () => {
  beforeEach(() => {
    localStorage.clear();
    jest.clearAllMocks();
  });

  it('provides initial unauthenticated state', () => {
    renderWithAuthProvider();
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    expect(screen.queryByTestId('user-info')).not.toBeInTheDocument();
  });

  it('handles successful login', async () => {
    renderWithAuthProvider();
    
    fireEvent.click(screen.getByTestId('login-btn'));
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });
    
    expect(screen.getByTestId('user-info')).toHaveTextContent('John Doe - <EMAIL>');
  });

  it('handles logout', async () => {
    renderWithAuthProvider();
    
    // First login
    fireEvent.click(screen.getByTestId('login-btn'));
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });
    
    // Then logout
    fireEvent.click(screen.getByTestId('logout-btn'));
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    expect(screen.queryByTestId('user-info')).not.toBeInTheDocument();
  });

  it('handles successful registration', async () => {
    renderWithAuthProvider();
    
    fireEvent.click(screen.getByTestId('register-btn'));
    
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });
    
    expect(screen.getByTestId('user-info')).toHaveTextContent('New User - <EMAIL>');
  });

  it('persists authentication state in localStorage', async () => {
    renderWithAuthProvider();
    
    fireEvent.click(screen.getByTestId('login-btn'));
    
    await waitFor(() => {
      expect(localStorage.setItem).toHaveBeenCalledWith('auth_token', expect.any(String));
      expect(localStorage.setItem).toHaveBeenCalledWith('user', expect.any(String));
    });
  });

  it('clears localStorage on logout', async () => {
    renderWithAuthProvider();
    
    // Login first
    fireEvent.click(screen.getByTestId('login-btn'));
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });
    
    // Then logout
    fireEvent.click(screen.getByTestId('logout-btn'));
    
    expect(localStorage.removeItem).toHaveBeenCalledWith('auth_token');
    expect(localStorage.removeItem).toHaveBeenCalledWith('user');
  });

  it('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');
    
    consoleSpy.mockRestore();
  });

  it('restores authentication state from localStorage on mount', () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Stored',
      lastName: 'User',
      role: 'user',
      preferences: { petTypes: [], notifications: true, newsletter: false },
      verificationStatus: 'verified',
      createdAt: '2024-01-01T00:00:00Z',
      lastLogin: '2024-01-01T00:00:00Z',
    };

    localStorage.getItem = jest.fn()
      .mockReturnValueOnce('mock_token')
      .mockReturnValueOnce(JSON.stringify(mockUser));

    renderWithAuthProvider();
    
    expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    expect(screen.getByTestId('user-info')).toHaveTextContent('Stored User - <EMAIL>');
  });
});
