{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ninterface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  address?: string;\n  role: 'user' | 'volunteer' | 'staff' | 'admin';\n  profileImage?: string;\n  preferences: {\n    petTypes: string[];\n    notifications: boolean;\n    newsletter: boolean;\n  };\n  createdAt: string;\n  lastLogin: string;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  register: (userData: RegisterData) => Promise<void>;\n  logout: () => void;\n  updateProfile: (userData: Partial<User>) => Promise<void>;\n  resetPassword: (email: string) => Promise<void>;\n}\n\ninterface RegisterData {\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  address?: string;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Simulate API calls - in real app, these would be actual API calls\n  const login = async (email: string, password: string): Promise<void> => {\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock user data\n      const mockUser: User = {\n        id: '1',\n        email,\n        firstName: 'John',\n        lastName: 'Doe',\n        phone: '+91 98765 43210',\n        address: '123 Pet Street, Animal City',\n        role: 'user',\n        profileImage: '/images/users/john-doe.jpg',\n        preferences: {\n          petTypes: ['dog', 'cat'],\n          notifications: true,\n          newsletter: true\n        },\n        createdAt: '2024-01-01T00:00:00Z',\n        lastLogin: new Date().toISOString()\n      };\n      \n      setUser(mockUser);\n      localStorage.setItem('auth_token', 'mock_token_123');\n      localStorage.setItem('user', JSON.stringify(mockUser));\n    } catch (error) {\n      throw new Error('Invalid credentials');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (userData: RegisterData): Promise<void> => {\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      const newUser: User = {\n        id: Date.now().toString(),\n        email: userData.email,\n        firstName: userData.firstName,\n        lastName: userData.lastName,\n        phone: userData.phone,\n        address: userData.address,\n        role: 'user',\n        preferences: {\n          petTypes: [],\n          notifications: true,\n          newsletter: false\n        },\n        createdAt: new Date().toISOString(),\n        lastLogin: new Date().toISOString()\n      };\n      \n      setUser(newUser);\n      localStorage.setItem('auth_token', 'mock_token_123');\n      localStorage.setItem('user', JSON.stringify(newUser));\n    } catch (error) {\n      throw new Error('Registration failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('user');\n  };\n\n  const updateProfile = async (userData: Partial<User>): Promise<void> => {\n    if (!user) throw new Error('No user logged in');\n    \n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const updatedUser = { ...user, ...userData };\n      setUser(updatedUser);\n      localStorage.setItem('user', JSON.stringify(updatedUser));\n    } catch (error) {\n      throw new Error('Profile update failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const resetPassword = async (email: string): Promise<void> => {\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    // In real app, this would send a password reset email\n  };\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const token = localStorage.getItem('auth_token');\n    const savedUser = localStorage.getItem('user');\n    \n    if (token && savedUser) {\n      try {\n        const parsedUser = JSON.parse(savedUser);\n        setUser(parsedUser);\n      } catch (error) {\n        localStorage.removeItem('auth_token');\n        localStorage.removeItem('user');\n      }\n    }\n    \n    setIsLoading(false);\n  }, []);\n\n  const value: AuthContextType = {\n    user,\n    isLoading,\n    isAuthenticated: !!user,\n    login,\n    register,\n    logout,\n    updateProfile,\n    resetPassword\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AA0CA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oEAAoE;IACpE,MAAM,QAAQ,OAAO,OAAe;QAClC,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,iBAAiB;YACjB,MAAM,WAAiB;gBACrB,IAAI;gBACJ;gBACA,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,cAAc;gBACd,aAAa;oBACX,UAAU;wBAAC;wBAAO;qBAAM;oBACxB,eAAe;oBACf,YAAY;gBACd;gBACA,WAAW;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,QAAQ;YACR,aAAa,OAAO,CAAC,cAAc;YACnC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,OAAO,SAAS,KAAK;gBACrB,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,SAAS,SAAS,OAAO;gBACzB,MAAM;gBACN,aAAa;oBACX,UAAU,EAAE;oBACZ,eAAe;oBACf,YAAY;gBACd;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,QAAQ;YACR,aAAa,OAAO,CAAC,cAAc;YACnC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,QAAQ;QACR,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;IAC1B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;QAE3B,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,GAAG,QAAQ;YAAC;YAC3C,QAAQ;YACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,EAAE,OAAO,OAAO;YACd,MAAM,IAAI,MAAM;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IACjD,sDAAsD;IACxD;IAEA,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,MAAM,YAAY,aAAa,OAAO,CAAC;QAEvC,IAAI,SAAS,WAAW;YACtB,IAAI;gBACF,MAAM,aAAa,KAAK,KAAK,CAAC;gBAC9B,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,QAAyB;QAC7B;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/contexts/ThemeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Theme = 'light' | 'dark' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  actualTheme: 'light' | 'dark';\n  setTheme: (theme: Theme) => void;\n  toggleTheme: () => void;\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [theme, setThemeState] = useState<Theme>('system');\n  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light');\n\n  // Get system preference\n  const getSystemTheme = (): 'light' | 'dark' => {\n    if (typeof window !== 'undefined') {\n      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n    }\n    return 'light';\n  };\n\n  // Update actual theme based on theme setting\n  const updateActualTheme = (newTheme: Theme) => {\n    let resolvedTheme: 'light' | 'dark';\n    \n    if (newTheme === 'system') {\n      resolvedTheme = getSystemTheme();\n    } else {\n      resolvedTheme = newTheme;\n    }\n    \n    setActualTheme(resolvedTheme);\n    \n    // Update document class and localStorage\n    if (typeof window !== 'undefined') {\n      const root = document.documentElement;\n      root.classList.remove('light', 'dark');\n      root.classList.add(resolvedTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Set theme\n  const setTheme = (newTheme: Theme) => {\n    setThemeState(newTheme);\n    updateActualTheme(newTheme);\n  };\n\n  // Toggle between light and dark (ignoring system)\n  const toggleTheme = () => {\n    const newTheme = actualTheme === 'light' ? 'dark' : 'light';\n    setTheme(newTheme);\n  };\n\n  // Initialize theme on mount\n  useEffect(() => {\n    // Get saved theme from localStorage\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    const initialTheme = savedTheme || 'system';\n    \n    setThemeState(initialTheme);\n    updateActualTheme(initialTheme);\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleSystemThemeChange = () => {\n      if (theme === 'system') {\n        updateActualTheme('system');\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleSystemThemeChange);\n    return () => mediaQuery.removeEventListener('change', handleSystemThemeChange);\n  }, []);\n\n  // Update actual theme when theme changes\n  useEffect(() => {\n    updateActualTheme(theme);\n  }, [theme]);\n\n  const value: ThemeContextType = {\n    theme,\n    actualTheme,\n    setTheme,\n    toggleTheme\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport default ThemeContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAaA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE;IACjF,MAAM,CAAC,OAAO,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEjE,wBAAwB;IACxB,MAAM,iBAAiB;QACrB;;QAGA,OAAO;IACT;IAEA,6CAA6C;IAC7C,MAAM,oBAAoB,CAAC;QACzB,IAAI;QAEJ,IAAI,aAAa,UAAU;YACzB,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;QAEA,eAAe;QAEf,yCAAyC;QACzC;;IAMF;IAEA,YAAY;IACZ,MAAM,WAAW,CAAC;QAChB,cAAc;QACd,kBAAkB;IACpB;IAEA,kDAAkD;IAClD,MAAM,cAAc;QAClB,MAAM,WAAW,gBAAgB,UAAU,SAAS;QACpD,SAAS;IACX;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,MAAM,eAAe,cAAc;QAEnC,cAAc;QACd,kBAAkB;QAElB,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,0BAA0B;YAC9B,IAAI,UAAU,UAAU;gBACtB,kBAAkB;YACpB;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG,EAAE;IAEL,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG;QAAC;KAAM;IAEV,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect } from 'react';\n\ntype Language = 'en' | 'hi' | 'es' | 'fr';\n\ninterface LanguageContextType {\n  language: Language;\n  setLanguage: (language: Language) => void;\n  t: (key: string) => string;\n  isRTL: boolean;\n}\n\n// Simple translations object - in real app, this would be loaded from files\nconst translations: Record<Language, Record<string, string>> = {\n  en: {\n    'nav.home': 'Home',\n    'nav.adopt': 'Adopt',\n    'nav.volunteer': 'Volunteer',\n    'nav.donate': 'Donate',\n    'nav.resources': 'Resources',\n    'nav.gallery': 'Gallery',\n    'nav.about': 'About',\n    'nav.contact': 'Contact',\n    'nav.login': 'Login',\n    'nav.signup': 'Sign Up',\n    'nav.logout': 'Logout',\n    'nav.dashboard': 'Dashboard',\n    'hero.title': 'Find Your Perfect Companion',\n    'hero.subtitle': 'Connect with loving pets in need of forever homes',\n    'hero.cta': 'Start Your Search',\n    'common.loading': 'Loading...',\n    'common.error': 'Something went wrong',\n    'common.save': 'Save',\n    'common.cancel': 'Cancel',\n    'common.submit': 'Submit',\n    'common.search': 'Search',\n    'common.filter': 'Filter',\n    'footer.tagline': 'Every pet deserves a loving home'\n  },\n  hi: {\n    'nav.home': 'होम',\n    'nav.adopt': 'गोद लें',\n    'nav.volunteer': 'स्वयंसेवक',\n    'nav.donate': 'दान करें',\n    'nav.resources': 'संसाधन',\n    'nav.gallery': 'गैलरी',\n    'nav.about': 'हमारे बारे में',\n    'nav.contact': 'संपर्क',\n    'nav.login': 'लॉगिन',\n    'nav.signup': 'साइन अप',\n    'nav.logout': 'लॉगआउट',\n    'nav.dashboard': 'डैशबोर्ड',\n    'hero.title': 'अपना परफेक्ट साथी खोजें',\n    'hero.subtitle': 'हमेशा के लिए घर की तलाश में प्यारे पालतू जानवरों से जुड़ें',\n    'hero.cta': 'अपनी खोज शुरू करें',\n    'common.loading': 'लोड हो रहा है...',\n    'common.error': 'कुछ गलत हुआ',\n    'common.save': 'सेव करें',\n    'common.cancel': 'रद्द करें',\n    'common.submit': 'जमा करें',\n    'common.search': 'खोजें',\n    'common.filter': 'फिल्टर',\n    'footer.tagline': 'हर पालतू जानवर एक प्यार भरे घर का हकदार है'\n  },\n  es: {\n    'nav.home': 'Inicio',\n    'nav.adopt': 'Adoptar',\n    'nav.volunteer': 'Voluntario',\n    'nav.donate': 'Donar',\n    'nav.resources': 'Recursos',\n    'nav.gallery': 'Galería',\n    'nav.about': 'Acerca de',\n    'nav.contact': 'Contacto',\n    'nav.login': 'Iniciar sesión',\n    'nav.signup': 'Registrarse',\n    'nav.logout': 'Cerrar sesión',\n    'nav.dashboard': 'Panel',\n    'hero.title': 'Encuentra tu Compañero Perfecto',\n    'hero.subtitle': 'Conecta con mascotas amorosas que necesitan hogares para siempre',\n    'hero.cta': 'Comienza tu Búsqueda',\n    'common.loading': 'Cargando...',\n    'common.error': 'Algo salió mal',\n    'common.save': 'Guardar',\n    'common.cancel': 'Cancelar',\n    'common.submit': 'Enviar',\n    'common.search': 'Buscar',\n    'common.filter': 'Filtrar',\n    'footer.tagline': 'Cada mascota merece un hogar lleno de amor'\n  },\n  fr: {\n    'nav.home': 'Accueil',\n    'nav.adopt': 'Adopter',\n    'nav.volunteer': 'Bénévole',\n    'nav.donate': 'Faire un don',\n    'nav.resources': 'Ressources',\n    'nav.gallery': 'Galerie',\n    'nav.about': 'À propos',\n    'nav.contact': 'Contact',\n    'nav.login': 'Connexion',\n    'nav.signup': 'S\\'inscrire',\n    'nav.logout': 'Déconnexion',\n    'nav.dashboard': 'Tableau de bord',\n    'hero.title': 'Trouvez votre Compagnon Parfait',\n    'hero.subtitle': 'Connectez-vous avec des animaux aimants qui cherchent des foyers pour toujours',\n    'hero.cta': 'Commencez votre Recherche',\n    'common.loading': 'Chargement...',\n    'common.error': 'Quelque chose s\\'est mal passé',\n    'common.save': 'Sauvegarder',\n    'common.cancel': 'Annuler',\n    'common.submit': 'Soumettre',\n    'common.search': 'Rechercher',\n    'common.filter': 'Filtrer',\n    'footer.tagline': 'Chaque animal mérite un foyer aimant'\n  }\n};\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined);\n\nexport const useLanguage = () => {\n  const context = useContext(LanguageContext);\n  if (context === undefined) {\n    throw new Error('useLanguage must be used within a LanguageProvider');\n  }\n  return context;\n};\n\nexport const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [language, setLanguageState] = useState<Language>('en');\n\n  // Translation function\n  const t = (key: string): string => {\n    return translations[language][key] || key;\n  };\n\n  // Check if language is RTL\n  const isRTL = language === 'ar' || language === 'he'; // Add RTL languages as needed\n\n  // Set language and update document\n  const setLanguage = (newLanguage: Language) => {\n    setLanguageState(newLanguage);\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('language', newLanguage);\n      document.documentElement.lang = newLanguage;\n      document.documentElement.dir = isRTL ? 'rtl' : 'ltr';\n    }\n  };\n\n  // Initialize language on mount\n  useEffect(() => {\n    const savedLanguage = localStorage.getItem('language') as Language;\n    const browserLanguage = navigator.language.split('-')[0] as Language;\n    \n    // Use saved language, then browser language, then default to English\n    const initialLanguage = savedLanguage || \n      (Object.keys(translations).includes(browserLanguage) ? browserLanguage : 'en');\n    \n    setLanguage(initialLanguage);\n  }, []);\n\n  const value: LanguageContextType = {\n    language,\n    setLanguage,\n    t,\n    isRTL\n  };\n\n  return (\n    <LanguageContext.Provider value={value}>\n      {children}\n    </LanguageContext.Provider>\n  );\n};\n\nexport default LanguageContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAaA,4EAA4E;AAC5E,MAAM,eAAyD;IAC7D,IAAI;QACF,YAAY;QACZ,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,YAAY;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;IACpB;IACA,IAAI;QACF,YAAY;QACZ,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,YAAY;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;IACpB;IACA,IAAI;QACF,YAAY;QACZ,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,YAAY;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;IACpB;IACA,IAAI;QACF,YAAY;QACZ,aAAa;QACb,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,eAAe;QACf,aAAa;QACb,eAAe;QACf,aAAa;QACb,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,cAAc;QACd,iBAAiB;QACjB,YAAY;QACZ,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;IACpB;AACF;AAEA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,MAAM,cAAc;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,mBAA4D,CAAC,EAAE,QAAQ,EAAE;IACpF,MAAM,CAAC,UAAU,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAExD,uBAAuB;IACvB,MAAM,IAAI,CAAC;QACT,OAAO,YAAY,CAAC,SAAS,CAAC,IAAI,IAAI;IACxC;IAEA,2BAA2B;IAC3B,MAAM,QAAQ,aAAa,QAAQ,aAAa,MAAM,8BAA8B;IAEpF,mCAAmC;IACnC,MAAM,cAAc,CAAC;QACnB,iBAAiB;QAEjB;;IAKF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,MAAM,kBAAkB,UAAU,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAExD,qEAAqE;QACrE,MAAM,kBAAkB,iBACtB,CAAC,OAAO,IAAI,CAAC,cAAc,QAAQ,CAAC,mBAAmB,kBAAkB,IAAI;QAE/E,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,QAA6B;QACjC;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\nexport function formatAge(value: number, unit: 'months' | 'years'): string {\n  if (unit === 'months') {\n    if (value < 12) {\n      return `${value} ${value === 1 ? 'month' : 'months'} old`;\n    } else {\n      const years = Math.floor(value / 12);\n      const months = value % 12;\n      if (months === 0) {\n        return `${years} ${years === 1 ? 'year' : 'years'} old`;\n      }\n      return `${years}y ${months}m old`;\n    }\n  }\n  return `${value} ${value === 1 ? 'year' : 'years'} old`;\n}\n\nexport function capitalizeFirst(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-');\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,UAAU,KAAa,EAAE,IAAwB;IAC/D,IAAI,SAAS,UAAU;QACrB,IAAI,QAAQ,IAAI;YACd,OAAO,GAAG,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,SAAS,IAAI,CAAC;QAC3D,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ;YACjC,MAAM,SAAS,QAAQ;YACvB,IAAI,WAAW,GAAG;gBAChB,OAAO,GAAG,MAAM,CAAC,EAAE,UAAU,IAAI,SAAS,QAAQ,IAAI,CAAC;YACzD;YACA,OAAO,GAAG,MAAM,EAAE,EAAE,OAAO,KAAK,CAAC;QACnC;IACF;IACA,OAAO,GAAG,MAAM,CAAC,EAAE,UAAU,IAAI,SAAS,QAAQ,IAAI,CAAC;AACzD;AAEO,SAAS,gBAAgB,GAAW;IACzC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/STUDY/Projects/Animal%20heaven/animal-heaven/src/contexts/NotificationContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ntype NotificationType = 'success' | 'error' | 'warning' | 'info';\n\ninterface Notification {\n  id: string;\n  type: NotificationType;\n  title: string;\n  message?: string;\n  duration?: number;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n}\n\ninterface NotificationContextType {\n  notifications: Notification[];\n  addNotification: (notification: Omit<Notification, 'id'>) => void;\n  removeNotification: (id: string) => void;\n  clearAll: () => void;\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined);\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext);\n  if (context === undefined) {\n    throw new Error('useNotifications must be used within a NotificationProvider');\n  }\n  return context;\n};\n\nconst NotificationItem: React.FC<{\n  notification: Notification;\n  onRemove: (id: string) => void;\n}> = ({ notification, onRemove }) => {\n  const { id, type, title, message, action } = notification;\n\n  const icons = {\n    success: CheckCircle,\n    error: AlertCircle,\n    warning: AlertTriangle,\n    info: Info\n  };\n\n  const colors = {\n    success: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200',\n    error: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',\n    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200',\n    info: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200'\n  };\n\n  const iconColors = {\n    success: 'text-green-600 dark:text-green-400',\n    error: 'text-red-600 dark:text-red-400',\n    warning: 'text-yellow-600 dark:text-yellow-400',\n    info: 'text-blue-600 dark:text-blue-400'\n  };\n\n  const Icon = icons[type];\n\n  return (\n    <div className={cn(\n      'flex items-start gap-3 p-4 border rounded-lg shadow-sm transition-all duration-300',\n      'animate-in slide-in-from-right-full',\n      colors[type]\n    )}>\n      <Icon className={cn('w-5 h-5 flex-shrink-0 mt-0.5', iconColors[type])} />\n      \n      <div className=\"flex-1 min-w-0\">\n        <h4 className=\"font-medium text-sm\">{title}</h4>\n        {message && (\n          <p className=\"text-sm opacity-90 mt-1\">{message}</p>\n        )}\n        {action && (\n          <button\n            onClick={action.onClick}\n            className=\"text-sm font-medium underline mt-2 hover:no-underline\"\n          >\n            {action.label}\n          </button>\n        )}\n      </div>\n\n      <button\n        onClick={() => onRemove(id)}\n        className=\"flex-shrink-0 p-1 rounded hover:bg-black/10 dark:hover:bg-white/10 transition-colors\"\n        aria-label=\"Dismiss notification\"\n      >\n        <X className=\"w-4 h-4\" />\n      </button>\n    </div>\n  );\n};\n\nexport const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n\n  const addNotification = useCallback((notification: Omit<Notification, 'id'>) => {\n    const id = Date.now().toString();\n    const newNotification: Notification = {\n      ...notification,\n      id,\n      duration: notification.duration ?? 5000\n    };\n\n    setNotifications(prev => [...prev, newNotification]);\n\n    // Auto-remove after duration\n    if (newNotification.duration > 0) {\n      setTimeout(() => {\n        removeNotification(id);\n      }, newNotification.duration);\n    }\n  }, []);\n\n  const removeNotification = useCallback((id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  }, []);\n\n  const clearAll = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  const value: NotificationContextType = {\n    notifications,\n    addNotification,\n    removeNotification,\n    clearAll\n  };\n\n  return (\n    <NotificationContext.Provider value={value}>\n      {children}\n      \n      {/* Notification Container */}\n      {notifications.length > 0 && (\n        <div className=\"fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full\">\n          {notifications.map((notification) => (\n            <NotificationItem\n              key={notification.id}\n              notification={notification}\n              onRemove={removeNotification}\n            />\n          ))}\n        </div>\n      )}\n    </NotificationContext.Provider>\n  );\n};\n\n// Convenience hooks for different notification types\nexport const useNotificationHelpers = () => {\n  const { addNotification } = useNotifications();\n\n  return {\n    success: (title: string, message?: string, options?: Partial<Notification>) =>\n      addNotification({ type: 'success', title, message, ...options }),\n    \n    error: (title: string, message?: string, options?: Partial<Notification>) =>\n      addNotification({ type: 'error', title, message, ...options }),\n    \n    warning: (title: string, message?: string, options?: Partial<Notification>) =>\n      addNotification({ type: 'warning', title, message, ...options }),\n    \n    info: (title: string, message?: string, options?: Partial<Notification>) =>\n      addNotification({ type: 'info', title, message, ...options })\n  };\n};\n\nexport default NotificationContext;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AA2BA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,mBAAmB;IAC9B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA,MAAM,mBAGD,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE;IAC9B,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IAE7C,MAAM,QAAQ;QACZ,SAAS,2NAAA,CAAA,cAAW;QACpB,OAAO,oNAAA,CAAA,cAAW;QAClB,SAAS,wNAAA,CAAA,gBAAa;QACtB,MAAM,kMAAA,CAAA,OAAI;IACZ;IAEA,MAAM,SAAS;QACb,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,aAAa;QACjB,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;IACR;IAEA,MAAM,OAAO,KAAK,CAAC,KAAK;IAExB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,sFACA,uCACA,MAAM,CAAC,KAAK;;0BAEZ,8OAAC;gBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC,UAAU,CAAC,KAAK;;;;;;0BAEpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuB;;;;;;oBACpC,yBACC,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;oBAEzC,wBACC,8OAAC;wBACC,SAAS,OAAO,OAAO;wBACvB,WAAU;kCAET,OAAO,KAAK;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,SAAS,IAAM,SAAS;gBACxB,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;AAEO,MAAM,uBAAgE,CAAC,EAAE,QAAQ,EAAE;IACxF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,MAAM,kBAAgC;YACpC,GAAG,YAAY;YACf;YACA,UAAU,aAAa,QAAQ,IAAI;QACrC;QAEA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;QAEnD,6BAA6B;QAC7B,IAAI,gBAAgB,QAAQ,GAAG,GAAG;YAChC,WAAW;gBACT,mBAAmB;YACrB,GAAG,gBAAgB,QAAQ;QAC7B;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,iBAAiB,EAAE;IACrB,GAAG,EAAE;IAEL,MAAM,QAAiC;QACrC;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,oBAAoB,QAAQ;QAAC,OAAO;;YAClC;YAGA,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;wBAEC,cAAc;wBACd,UAAU;uBAFL,aAAa,EAAE;;;;;;;;;;;;;;;;AASlC;AAGO,MAAM,yBAAyB;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG;IAE5B,OAAO;QACL,SAAS,CAAC,OAAe,SAAkB,UACzC,gBAAgB;gBAAE,MAAM;gBAAW;gBAAO;gBAAS,GAAG,OAAO;YAAC;QAEhE,OAAO,CAAC,OAAe,SAAkB,UACvC,gBAAgB;gBAAE,MAAM;gBAAS;gBAAO;gBAAS,GAAG,OAAO;YAAC;QAE9D,SAAS,CAAC,OAAe,SAAkB,UACzC,gBAAgB;gBAAE,MAAM;gBAAW;gBAAO;gBAAS,GAAG,OAAO;YAAC;QAEhE,MAAM,CAAC,OAAe,SAAkB,UACtC,gBAAgB;gBAAE,MAAM;gBAAQ;gBAAO;gBAAS,GAAG,OAAO;YAAC;IAC/D;AACF;uCAEe", "debugId": null}}]}