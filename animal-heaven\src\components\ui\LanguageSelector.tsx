'use client';

import React, { useState } from 'react';
import { Globe, ChevronDown } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import { Button } from './Button';
import { cn } from '@/lib/utils';

interface LanguageSelectorProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const languages = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳' },
  { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' }
] as const;

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ 
  variant = 'dropdown', 
  size = 'md',
  className 
}) => {
  const { language, setLanguage } = useLanguage();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const currentLanguage = languages.find(lang => lang.code === language) || languages[0];

  if (variant === 'button') {
    return (
      <Button
        variant="ghost"
        size={size}
        className={cn('flex items-center gap-2', className)}
        aria-label="Change language"
      >
        <Globe className="w-4 h-4" />
        <span className="hidden sm:inline">{currentLanguage.nativeName}</span>
      </Button>
    );
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size={size}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className={cn('flex items-center gap-2', className)}
        aria-label="Language selector"
        aria-expanded={isDropdownOpen}
      >
        <Globe className="w-4 h-4" />
        <span className="hidden sm:inline">{currentLanguage.nativeName}</span>
        <ChevronDown className={cn(
          'w-3 h-3 transition-transform duration-200',
          isDropdownOpen && 'rotate-180'
        )} />
      </Button>

      {isDropdownOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsDropdownOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 z-20 min-w-[160px] bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg py-1">
            {languages.map((lang) => {
              const isSelected = language === lang.code;
              
              return (
                <button
                  key={lang.code}
                  onClick={() => {
                    setLanguage(lang.code as any);
                    setIsDropdownOpen(false);
                  }}
                  className={cn(
                    'w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors',
                    'hover:bg-neutral-100 dark:hover:bg-neutral-700',
                    isSelected && 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'
                  )}
                >
                  <span className="text-lg">{lang.flag}</span>
                  <div className="flex-1 text-left">
                    <div className="font-medium">{lang.nativeName}</div>
                    <div className="text-xs text-neutral-500 dark:text-neutral-400">
                      {lang.name}
                    </div>
                  </div>
                  {isSelected && (
                    <div className="w-2 h-2 bg-orange-500 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};

export { LanguageSelector };
