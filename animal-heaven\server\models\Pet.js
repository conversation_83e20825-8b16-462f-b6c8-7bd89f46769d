const mongoose = require('mongoose');

const petSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['dog', 'cat', 'bird', 'rabbit', 'other']
  },
  breed: {
    type: String,
    required: true,
    trim: true
  },
  age: {
    value: {
      type: Number,
      required: true
    },
    unit: {
      type: String,
      enum: ['months', 'years'],
      default: 'years'
    }
  },
  gender: {
    type: String,
    required: true,
    enum: ['male', 'female']
  },
  size: {
    type: String,
    required: true,
    enum: ['small', 'medium', 'large', 'extra-large']
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  personality: [{
    type: String,
    enum: ['friendly', 'energetic', 'calm', 'playful', 'gentle', 'protective', 'independent', 'social', 'quiet', 'active']
  }],
  healthInfo: {
    vaccinated: {
      type: Boolean,
      default: false
    },
    spayedNeutered: {
      type: Boolean,
      default: false
    },
    medicalConditions: [{
      condition: String,
      description: String,
      treatment: String
    }],
    lastVetVisit: Date
  },
  images: [{
    url: String,
    alt: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  videos: [{
    url: String,
    title: String,
    description: String
  }],
  status: {
    type: String,
    enum: ['available', 'pending', 'adopted', 'fostered', 'medical-hold'],
    default: 'available'
  },
  location: {
    shelter: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    state: {
      type: String,
      required: true
    }
  },
  adoptionFee: {
    type: Number,
    default: 0
  },
  specialNeeds: {
    type: Boolean,
    default: false
  },
  goodWith: {
    children: {
      type: Boolean,
      default: false
    },
    dogs: {
      type: Boolean,
      default: false
    },
    cats: {
      type: Boolean,
      default: false
    }
  },
  arrivalDate: {
    type: Date,
    default: Date.now
  },
  story: {
    type: String,
    trim: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for better query performance
petSchema.index({ type: 1, status: 1 });
petSchema.index({ location: 1 });
petSchema.index({ featured: 1, status: 1 });
petSchema.index({ createdAt: -1 });

module.exports = mongoose.model('Pet', petSchema);
