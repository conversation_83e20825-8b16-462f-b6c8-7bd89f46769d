'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Heart, Search } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { SearchBar } from '@/components/ui/SearchBar';
import { cn } from '@/lib/utils';

interface HeroSlide {
  id: string;
  image: string;
  title: string;
  subtitle: string;
  description: string;
  cta: {
    text: string;
    href: string;
  };
}

interface HeroProps {
  slides?: HeroSlide[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  onSearch?: (query: string, filters: any) => void;
}

const defaultSlides: HeroSlide[] = [
  {
    id: '1',
    image: '/images/hero/hero-1.jpg',
    title: 'Find Your Perfect Companion',
    subtitle: 'Thousands of loving pets are waiting for their forever homes',
    description: 'Every adoption saves a life and creates an unbreakable bond filled with unconditional love, joy, and countless precious memories.',
    cta: {
      text: 'Start Your Search',
      href: '/adopt'
    }
  },
  {
    id: '2',
    image: '/images/hero/hero-2.jpg',
    title: 'Give Love, Get Love',
    subtitle: 'Open your heart and home to a pet in need',
    description: 'Experience the incredible joy of pet parenthood while giving a deserving animal the loving family they\'ve been dreaming of.',
    cta: {
      text: 'Browse Pets',
      href: '/adopt'
    }
  },
  {
    id: '3',
    image: '/images/hero/hero-3.jpg',
    title: 'Make a Difference Today',
    subtitle: 'Volunteer, donate, or adopt - every action counts',
    description: 'Join our community of animal lovers and help us create a world where every pet has a loving home and a bright future.',
    cta: {
      text: 'Get Involved',
      href: '/volunteer'
    }
  }
];

const Hero: React.FC<HeroProps> = ({
  slides = defaultSlides,
  autoPlay = true,
  autoPlayInterval = 5000,
  onSearch
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, autoPlayInterval, slides.length]);

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const goToPrevious = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToNext = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const currentSlideData = slides[currentSlide];

  return (
    <section className="relative h-screen min-h-[600px] overflow-hidden">
      {/* Background Slides */}
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={cn(
              'absolute inset-0 transition-opacity duration-1000',
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            )}
          >
            {/* Placeholder for hero images - using gradient backgrounds for now */}
            <div className="w-full h-full bg-gradient-to-br from-orange-400 via-amber-500 to-orange-600 relative">
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/30" />
              
              {/* Decorative Elements */}
              <div className="absolute top-20 left-10 text-6xl opacity-20 animate-float">
                🐕
              </div>
              <div className="absolute top-40 right-20 text-4xl opacity-20 animate-bounce-gentle">
                🐱
              </div>
              <div className="absolute bottom-32 left-20 text-5xl opacity-20 animate-float">
                🐾
              </div>
              <div className="absolute bottom-20 right-10 text-3xl opacity-20 animate-bounce-gentle">
                ❤️
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 h-full flex items-center">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="text-white space-y-6">
              <div className="space-y-4">
                <h1 className="font-primary text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
                  {currentSlideData.title}
                </h1>
                <p className="text-xl sm:text-2xl font-medium text-orange-100">
                  {currentSlideData.subtitle}
                </p>
                <p className="text-lg text-neutral-200 leading-relaxed max-w-lg">
                  {currentSlideData.description}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  variant="primary"
                  className="bg-white text-orange-600 hover:bg-orange-50 shadow-xl"
                  leftIcon={<Search className="w-5 h-5" />}
                >
                  {currentSlideData.cta.text}
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-white text-white hover:bg-white hover:text-orange-600"
                  leftIcon={<Heart className="w-5 h-5" />}
                >
                  Learn More
                </Button>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">2,500+</div>
                  <div className="text-sm text-orange-200">Pets Adopted</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">150+</div>
                  <div className="text-sm text-orange-200">Active Volunteers</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white">50+</div>
                  <div className="text-sm text-orange-200">Partner Shelters</div>
                </div>
              </div>
            </div>

            {/* Search Section */}
            <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-2xl">
              <div className="mb-6">
                <h2 className="font-primary text-2xl font-semibold text-neutral-900 mb-2">
                  Find Your Perfect Match
                </h2>
                <p className="text-neutral-600">
                  Search through hundreds of loving pets waiting for their forever homes.
                </p>
              </div>

              <SearchBar
                placeholder="Search by name, breed, or type..."
                onSearch={onSearch}
                showFilters={false}
              />

              {/* Quick Filters */}
              <div className="mt-6">
                <p className="text-sm font-medium text-neutral-700 mb-3">Popular searches:</p>
                <div className="flex flex-wrap gap-2">
                  {['Dogs', 'Cats', 'Puppies', 'Kittens', 'Small Dogs', 'Senior Pets'].map((filter) => (
                    <Button
                      key={filter}
                      variant="outline"
                      size="sm"
                      className="text-xs"
                    >
                      {filter}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex items-center space-x-4">
          {/* Previous Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={goToPrevious}
            className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30"
          >
            <ChevronLeft className="w-5 h-5" />
          </Button>

          {/* Slide Indicators */}
          <div className="flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  'w-3 h-3 rounded-full transition-all duration-300',
                  index === currentSlide
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                )}
              />
            ))}
          </div>

          {/* Next Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={goToNext}
            className="bg-white/20 backdrop-blur-sm text-white hover:bg-white/30"
          >
            <ChevronRight className="w-5 h-5" />
          </Button>
        </div>
      </div>

      {/* Play/Pause Button */}
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsPlaying(!isPlaying)}
        className="absolute top-4 right-4 z-20 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30"
      >
        {isPlaying ? '⏸️' : '▶️'}
      </Button>
    </section>
  );
};

export { Hero };
export type { HeroSlide, HeroProps };
