@import "tailwindcss";

/* Animal Heaven Design System - Warm & Pet-Friendly Theme */
:root {
  /* Color scheme */
  color-scheme: light;
  /* Primary Colors - Warm Earth Tones */
  --primary-50: #fef7ed;
  --primary-100: #fdedd3;
  --primary-200: #fbd7a5;
  --primary-300: #f8bc6d;
  --primary-400: #f59e42;
  --primary-500: #f97316; /* Main orange */
  --primary-600: #ea580c;
  --primary-700: #c2410c;
  --primary-800: #9a3412;
  --primary-900: #7c2d12;

  /* Secondary Colors - Soft Greens */
  --secondary-50: #f0fdf4;
  --secondary-100: #dcfce7;
  --secondary-200: #bbf7d0;
  --secondary-300: #86efac;
  --secondary-400: #4ade80;
  --secondary-500: #22c55e; /* Main green */
  --secondary-600: #16a34a;
  --secondary-700: #15803d;
  --secondary-800: #166534;
  --secondary-900: #14532d;

  /* Accent Colors - Warm Browns */
  --accent-50: #fdf8f6;
  --accent-100: #f2e8e5;
  --accent-200: #eaddd7;
  --accent-300: #e0cfc6;
  --accent-400: #d2bab0;
  --accent-500: #bfa094;
  --accent-600: #a18072;
  --accent-700: #977669;
  --accent-800: #846358;
  --accent-900: #43302b;

  /* Neutral Colors */
  --neutral-50: #fafaf9;
  --neutral-100: #f5f5f4;
  --neutral-200: #e7e5e4;
  --neutral-300: #d6d3d1;
  --neutral-400: #a8a29e;
  --neutral-500: #78716c;
  --neutral-600: #57534e;
  --neutral-700: #44403c;
  --neutral-800: #292524;
  --neutral-900: #1c1917;

  /* Semantic Colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Background & Text */
  --background: #fefefe;
  --surface: #ffffff;
  --surface-secondary: #fafaf9;
  --text-primary: #1c1917;
  --text-secondary: #57534e;
  --text-muted: #78716c;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Typography */
  --font-family-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-secondary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
}

/* Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #1c1917;
    --surface: #292524;
    --surface-secondary: #44403c;
    --text-primary: #fafaf9;
    --text-secondary: #d6d3d1;
    --text-muted: #a8a29e;
  }
}

/* Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--text-primary);
  font-family: var(--font-family-secondary);
  line-height: var(--line-height-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

/* Form elements reset */
button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  margin: 0;
}

button {
  background: none;
  border: none;
  cursor: pointer;
}

input,
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Remove default focus outline and add custom one */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
}

button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Typography Classes */
.font-primary {
  font-family: var(--font-family-primary);
}

.font-secondary {
  font-family: var(--font-family-secondary);
}

/* Heading Styles */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
}

h2 {
  font-size: var(--font-size-3xl);
  font-weight: 600;
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: 600;
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: 500;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* Fallback for browsers that don't support background-clip: text */
  color: var(--primary-500);
}

.bg-gradient-warm {
  background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.shadow-warm {
  box-shadow: 0 10px 25px -5px rgba(251, 146, 60, 0.1), 0 4px 6px -4px rgba(251, 146, 60, 0.1);
}

/* Animation Classes */
.animate-float {
  -webkit-animation: float 3s ease-in-out infinite;
  animation: float 3s ease-in-out infinite;
}

@-webkit-keyframes float {
  0%, 100% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
}

@keyframes float {
  0%, 100% {
    -webkit-transform: translateY(0px);
    transform: translateY(0px);
  }
  50% {
    -webkit-transform: translateY(-10px);
    transform: translateY(-10px);
  }
}

.animate-bounce-gentle {
  -webkit-animation: bounce-gentle 2s infinite;
  animation: bounce-gentle 2s infinite;
}

@-webkit-keyframes bounce-gentle {
  0%, 100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  50% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
  }
}

/* Focus Styles */
.focus-ring {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-ring:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: 9999px; /* fallback for --radius-full */
  border-radius: var(--radius-full, 9999px);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* Dark Mode Styles */
.dark {
  color-scheme: dark;

  /* Override neutral colors for dark mode */
  --neutral-50: #0c0a09;
  --neutral-100: #1c1917;
  --neutral-200: #292524;
  --neutral-300: #44403c;
  --neutral-400: #57534e;
  --neutral-500: #78716c;
  --neutral-600: #a8a29e;
  --neutral-700: #d6d3d1;
  --neutral-800: #e7e5e4;
  --neutral-900: #f5f5f4;
  --neutral-950: #fafaf9;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: var(--neutral-200);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

/* Dark mode transitions are handled in the base * selector above */

/* Ensure proper contrast in dark mode */
.dark .bg-white {
  background-color: var(--neutral-100);
}

.dark .text-neutral-900 {
  color: var(--neutral-100);
}

.dark .text-neutral-800 {
  color: var(--neutral-200);
}

.dark .text-neutral-700 {
  color: var(--neutral-300);
}

.dark .text-neutral-600 {
  color: var(--neutral-400);
}

.dark .text-neutral-500 {
  color: var(--neutral-500);
}

.dark .border-neutral-200 {
  border-color: var(--neutral-300);
}

.dark .border-neutral-300 {
  border-color: var(--neutral-400);
}

/* Dark mode gradients */
.dark .bg-gradient-to-br.from-orange-50 {
  background: linear-gradient(to bottom right, var(--neutral-100), var(--neutral-200));
}

.dark .bg-gradient-to-br.from-blue-50 {
  background: linear-gradient(to bottom right, var(--neutral-100), var(--neutral-200));
}

.dark .bg-gradient-to-br.from-green-50 {
  background: linear-gradient(to bottom right, var(--neutral-100), var(--neutral-200));
}

.dark .bg-gradient-to-br.from-purple-50 {
  background: linear-gradient(to bottom right, var(--neutral-100), var(--neutral-200));
}

/* Additional utility classes for better compatibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Focus visible for better accessibility */
.focus-visible:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-float,
  .animate-bounce-gentle {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary-500: #000000;
    --neutral-300: #666666;
    --neutral-600: #333333;
  }

  .dark {
    --primary-500: #ffffff;
    --neutral-300: #cccccc;
    --neutral-600: #999999;
  }
}
