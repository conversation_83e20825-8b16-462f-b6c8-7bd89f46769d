import React from 'react';
import { render, screen } from '@testing-library/react';
import { Card, CardHeader, CardTitle, CardContent } from '../Card';

describe('Card Components', () => {
  describe('Card', () => {
    it('renders correctly with default props', () => {
      render(<Card data-testid="card">Card content</Card>);
      const card = screen.getByTestId('card');
      
      expect(card).toBeInTheDocument();
      expect(card).toHaveClass('bg-white', 'border', 'rounded-lg');
    });

    it('renders with different variants', () => {
      const { rerender } = render(<Card variant="warm" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('bg-gradient-to-br', 'from-orange-50');

      rerender(<Card variant="elevated" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('shadow-lg');

      rerender(<Card variant="outlined" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('border-2');
    });

    it('applies custom className', () => {
      render(<Card className="custom-class" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('custom-class');
    });

    it('handles different padding options', () => {
      const { rerender } = render(<Card padding="none" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('p-0');

      rerender(<Card padding="sm" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('p-4');

      rerender(<Card padding="lg" data-testid="card">Content</Card>);
      expect(screen.getByTestId('card')).toHaveClass('p-8');
    });
  });

  describe('CardHeader', () => {
    it('renders correctly', () => {
      render(<CardHeader data-testid="header">Header content</CardHeader>);
      const header = screen.getByTestId('header');
      
      expect(header).toBeInTheDocument();
      expect(header).toHaveClass('flex', 'flex-col', 'space-y-1.5', 'p-6');
    });

    it('applies custom className', () => {
      render(<CardHeader className="custom-header" data-testid="header">Content</CardHeader>);
      expect(screen.getByTestId('header')).toHaveClass('custom-header');
    });
  });

  describe('CardTitle', () => {
    it('renders correctly', () => {
      render(<CardTitle data-testid="title">Card Title</CardTitle>);
      const title = screen.getByTestId('title');
      
      expect(title).toBeInTheDocument();
      expect(title).toHaveClass('text-2xl', 'font-semibold');
      expect(title).toHaveTextContent('Card Title');
    });

    it('applies custom className', () => {
      render(<CardTitle className="custom-title" data-testid="title">Title</CardTitle>);
      expect(screen.getByTestId('title')).toHaveClass('custom-title');
    });
  });

  describe('CardContent', () => {
    it('renders correctly', () => {
      render(<CardContent data-testid="content">Card content</CardContent>);
      const content = screen.getByTestId('content');
      
      expect(content).toBeInTheDocument();
      expect(content).toHaveClass('p-6', 'pt-0');
      expect(content).toHaveTextContent('Card content');
    });

    it('applies custom className', () => {
      render(<CardContent className="custom-content" data-testid="content">Content</CardContent>);
      expect(screen.getByTestId('content')).toHaveClass('custom-content');
    });
  });

  describe('Card composition', () => {
    it('renders complete card structure', () => {
      render(
        <Card data-testid="card">
          <CardHeader data-testid="header">
            <CardTitle data-testid="title">Test Card</CardTitle>
          </CardHeader>
          <CardContent data-testid="content">
            This is the card content.
          </CardContent>
        </Card>
      );

      expect(screen.getByTestId('card')).toBeInTheDocument();
      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('title')).toHaveTextContent('Test Card');
      expect(screen.getByTestId('content')).toHaveTextContent('This is the card content.');
    });
  });
});
