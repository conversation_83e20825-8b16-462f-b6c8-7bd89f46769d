'use client';

import React, { useState, useEffect } from 'react';
import { Filter, Grid, List, SlidersHorizontal, X } from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  Badge, 
  SearchBar, 
  PetCard,
  Navigation,
  Footer
} from '@/components';
import { Pet } from '@/components/pets/PetCard';

// Extended sample pets data
const samplePets: Pet[] = [
  {
    id: '1',
    name: '<PERSON>',
    type: 'dog',
    breed: 'Golden Retriever',
    age: { value: 3, unit: 'years' },
    gender: 'male',
    size: 'large',
    color: 'Golden',
    description: '<PERSON> is a friendly and energetic Golden Retriever who loves playing fetch and swimming. He\'s great with kids and other dogs.',
    personality: ['friendly', 'energetic', 'playful', 'gentle'],
    images: [{ url: '/images/pets/buddy.jpg', alt: 'Buddy the Golden Retriever', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Happy Paws Shelter', city: 'Mumbai', state: 'Maharashtra' },
    adoptionFee: 5000,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: false },
    views: 245,
    likes: 89,
    featured: true
  },
  {
    id: '2',
    name: '<PERSON>',
    type: 'cat',
    breed: 'Persian',
    age: { value: 2, unit: 'years' },
    gender: 'female',
    size: 'medium',
    color: 'White',
    description: 'Luna is a calm and affectionate Persian cat who enjoys quiet moments and gentle pets.',
    personality: ['calm', 'gentle', 'independent'],
    images: [{ url: '/images/pets/luna.jpg', alt: 'Luna the Persian Cat', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Feline Friends', city: 'Delhi', state: 'Delhi' },
    adoptionFee: 3000,
    specialNeeds: false,
    goodWith: { children: true, dogs: false, cats: true },
    views: 156,
    likes: 67,
    featured: false
  },
  {
    id: '3',
    name: 'Charlie',
    type: 'dog',
    breed: 'Labrador Mix',
    age: { value: 8, unit: 'months' },
    gender: 'male',
    size: 'medium',
    color: 'Brown',
    description: 'Charlie is a playful puppy who loves to learn new tricks and play with toys.',
    personality: ['playful', 'energetic', 'social', 'friendly'],
    images: [{ url: '/images/pets/charlie.jpg', alt: 'Charlie the Labrador Mix', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Rescue Haven', city: 'Bangalore', state: 'Karnataka' },
    adoptionFee: 4000,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: true },
    views: 189,
    likes: 92,
    featured: true
  },
  {
    id: '4',
    name: 'Whiskers',
    type: 'cat',
    breed: 'Domestic Shorthair',
    age: { value: 5, unit: 'years' },
    gender: 'female',
    size: 'small',
    color: 'Tabby',
    description: 'Whiskers is a senior cat who loves quiet companionship and sunny windowsills.',
    personality: ['calm', 'independent', 'gentle'],
    images: [{ url: '/images/pets/whiskers.jpg', alt: 'Whiskers the Tabby Cat', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Senior Pet Sanctuary', city: 'Chennai', state: 'Tamil Nadu' },
    adoptionFee: 2000,
    specialNeeds: true,
    goodWith: { children: false, dogs: false, cats: true },
    views: 78,
    likes: 34,
    featured: false
  },
  {
    id: '5',
    name: 'Rocky',
    type: 'dog',
    breed: 'German Shepherd',
    age: { value: 4, unit: 'years' },
    gender: 'male',
    size: 'large',
    color: 'Black & Tan',
    description: 'Rocky is a loyal and intelligent German Shepherd looking for an experienced owner.',
    personality: ['protective', 'intelligent', 'loyal', 'active'],
    images: [{ url: '/images/pets/rocky.jpg', alt: 'Rocky the German Shepherd', isPrimary: true }],
    status: 'pending',
    location: { shelter: 'Guardian Angels', city: 'Pune', state: 'Maharashtra' },
    adoptionFee: 6000,
    specialNeeds: false,
    goodWith: { children: true, dogs: false, cats: false },
    views: 312,
    likes: 145,
    featured: true
  },
  {
    id: '6',
    name: 'Mittens',
    type: 'cat',
    breed: 'Maine Coon',
    age: { value: 1, unit: 'years' },
    gender: 'female',
    size: 'large',
    color: 'Orange',
    description: 'Mittens is a young Maine Coon with a big personality and even bigger heart.',
    personality: ['playful', 'social', 'friendly', 'energetic'],
    images: [{ url: '/images/pets/mittens.jpg', alt: 'Mittens the Maine Coon', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Purrfect Homes', city: 'Hyderabad', state: 'Telangana' },
    adoptionFee: 3500,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: true },
    views: 203,
    likes: 87,
    featured: false
  }
];

interface Filters {
  type: string;
  breed: string;
  size: string;
  age: string;
  gender: string;
  location: string;
  specialNeeds: boolean;
  goodWithChildren: boolean;
  goodWithDogs: boolean;
  goodWithCats: boolean;
  status: string;
}

const AdoptPage: React.FC = () => {
  const [pets, setPets] = useState<Pet[]>(samplePets);
  const [filteredPets, setFilteredPets] = useState<Pet[]>(samplePets);
  const [filters, setFilters] = useState<Filters>({
    type: '',
    breed: '',
    size: '',
    age: '',
    gender: '',
    location: '',
    specialNeeds: false,
    goodWithChildren: false,
    goodWithDogs: false,
    goodWithCats: false,
    status: 'available'
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('newest');
  const [showFilters, setShowFilters] = useState(false);

  // Filter pets based on current filters and search query
  useEffect(() => {
    let filtered = pets.filter(pet => {
      // Search query filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          pet.name.toLowerCase().includes(query) ||
          pet.breed.toLowerCase().includes(query) ||
          pet.description.toLowerCase().includes(query) ||
          pet.personality.some(trait => trait.toLowerCase().includes(query));
        
        if (!matchesSearch) return false;
      }

      // Type filter
      if (filters.type && pet.type !== filters.type) return false;
      
      // Size filter
      if (filters.size && pet.size !== filters.size) return false;
      
      // Gender filter
      if (filters.gender && pet.gender !== filters.gender) return false;
      
      // Status filter
      if (filters.status && pet.status !== filters.status) return false;
      
      // Location filter
      if (filters.location) {
        const locationQuery = filters.location.toLowerCase();
        const matchesLocation = 
          pet.location.city.toLowerCase().includes(locationQuery) ||
          pet.location.state.toLowerCase().includes(locationQuery);
        if (!matchesLocation) return false;
      }
      
      // Special needs filter
      if (filters.specialNeeds && !pet.specialNeeds) return false;
      
      // Good with filters
      if (filters.goodWithChildren && !pet.goodWith.children) return false;
      if (filters.goodWithDogs && !pet.goodWith.dogs) return false;
      if (filters.goodWithCats && !pet.goodWith.cats) return false;
      
      // Age filter
      if (filters.age) {
        const [minAge, maxAge] = filters.age.split('-').map(Number);
        const petAgeInYears = pet.age.unit === 'months' ? pet.age.value / 12 : pet.age.value;
        if (petAgeInYears < minAge || petAgeInYears > maxAge) return false;
      }

      return true;
    });

    // Sort filtered pets
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => b.views - a.views); // Using views as proxy for newest
        break;
      case 'oldest':
        filtered.sort((a, b) => a.views - b.views);
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'age-young':
        filtered.sort((a, b) => {
          const ageA = a.age.unit === 'months' ? a.age.value / 12 : a.age.value;
          const ageB = b.age.unit === 'months' ? b.age.value / 12 : b.age.value;
          return ageA - ageB;
        });
        break;
      case 'age-old':
        filtered.sort((a, b) => {
          const ageA = a.age.unit === 'months' ? a.age.value / 12 : a.age.value;
          const ageB = b.age.unit === 'months' ? b.age.value / 12 : b.age.value;
          return ageB - ageA;
        });
        break;
      case 'popular':
        filtered.sort((a, b) => b.likes - a.likes);
        break;
    }

    setFilteredPets(filtered);
  }, [pets, filters, searchQuery, sortBy]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFilterChange = (key: keyof Filters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      type: '',
      breed: '',
      size: '',
      age: '',
      gender: '',
      location: '',
      specialNeeds: false,
      goodWithChildren: false,
      goodWithDogs: false,
      goodWithCats: false,
      status: 'available'
    });
    setSearchQuery('');
  };

  const activeFiltersCount = Object.values(filters).filter(value => 
    typeof value === 'boolean' ? value : value !== ''
  ).length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
            Find Your Perfect Companion
          </h1>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Browse through our available pets and find the one that will complete your family. 
            Each pet is unique and ready to bring love into your home.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <SearchBar
            placeholder="Search by name, breed, or personality..."
            onSearch={handleSearch}
            showFilters={false}
          />
        </div>

        {/* Filter Controls */}
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Sidebar Filters */}
          <div className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <Card variant="warm" className="sticky top-4">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="font-primary text-lg font-semibold">Filters</h3>
                  <div className="flex items-center gap-2">
                    {activeFiltersCount > 0 && (
                      <Badge variant="primary" size="sm">
                        {activeFiltersCount}
                      </Badge>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-xs"
                    >
                      Clear All
                    </Button>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Pet Type */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Pet Type
                    </label>
                    <select
                      value={filters.type}
                      onChange={(e) => handleFilterChange('type', e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    >
                      <option value="">All Types</option>
                      <option value="dog">Dogs</option>
                      <option value="cat">Cats</option>
                      <option value="bird">Birds</option>
                      <option value="rabbit">Rabbits</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  {/* Size */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Size
                    </label>
                    <select
                      value={filters.size}
                      onChange={(e) => handleFilterChange('size', e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    >
                      <option value="">All Sizes</option>
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                      <option value="large">Large</option>
                      <option value="extra-large">Extra Large</option>
                    </select>
                  </div>

                  {/* Age */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Age
                    </label>
                    <select
                      value={filters.age}
                      onChange={(e) => handleFilterChange('age', e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    >
                      <option value="">All Ages</option>
                      <option value="0-1">Puppy/Kitten (0-1 year)</option>
                      <option value="1-3">Young (1-3 years)</option>
                      <option value="3-7">Adult (3-7 years)</option>
                      <option value="7-15">Senior (7+ years)</option>
                    </select>
                  </div>

                  {/* Gender */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Gender
                    </label>
                    <select
                      value={filters.gender}
                      onChange={(e) => handleFilterChange('gender', e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    >
                      <option value="">All Genders</option>
                      <option value="male">Male</option>
                      <option value="female">Female</option>
                    </select>
                  </div>

                  {/* Location */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={filters.location}
                      onChange={(e) => handleFilterChange('location', e.target.value)}
                      placeholder="City or State"
                      className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>

                  {/* Special Characteristics */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-3">
                      Special Characteristics
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.specialNeeds}
                          onChange={(e) => handleFilterChange('specialNeeds', e.target.checked)}
                          className="rounded border-neutral-300 text-orange-600 focus:ring-orange-500"
                        />
                        <span className="ml-2 text-sm text-neutral-700">Special Needs</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.goodWithChildren}
                          onChange={(e) => handleFilterChange('goodWithChildren', e.target.checked)}
                          className="rounded border-neutral-300 text-orange-600 focus:ring-orange-500"
                        />
                        <span className="ml-2 text-sm text-neutral-700">Good with Children</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.goodWithDogs}
                          onChange={(e) => handleFilterChange('goodWithDogs', e.target.checked)}
                          className="rounded border-neutral-300 text-orange-600 focus:ring-orange-500"
                        />
                        <span className="ml-2 text-sm text-neutral-700">Good with Dogs</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.goodWithCats}
                          onChange={(e) => handleFilterChange('goodWithCats', e.target.checked)}
                          className="rounded border-neutral-300 text-orange-600 focus:ring-orange-500"
                        />
                        <span className="ml-2 text-sm text-neutral-700">Good with Cats</span>
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div>
                <h2 className="text-xl font-semibold text-neutral-900">
                  {filteredPets.length} pets found
                </h2>
                <p className="text-sm text-neutral-600">
                  {searchQuery && `Showing results for "${searchQuery}"`}
                </p>
              </div>

              <div className="flex items-center gap-4">
                {/* Mobile Filter Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden"
                  leftIcon={<SlidersHorizontal className="w-4 h-4" />}
                >
                  Filters
                  {activeFiltersCount > 0 && (
                    <Badge variant="primary" size="sm" className="ml-1">
                      {activeFiltersCount}
                    </Badge>
                  )}
                </Button>

                {/* Sort */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="name">Name A-Z</option>
                  <option value="age-young">Youngest First</option>
                  <option value="age-old">Oldest First</option>
                  <option value="popular">Most Popular</option>
                </select>

                {/* View Mode */}
                <div className="flex border border-neutral-300 rounded-lg overflow-hidden">
                  <Button
                    variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-none border-0"
                  >
                    <Grid className="w-4 h-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-none border-0"
                  >
                    <List className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Pet Grid/List */}
            {filteredPets.length > 0 ? (
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                  : 'space-y-4'
              }>
                {filteredPets.map((pet) => (
                  <PetCard
                    key={pet.id}
                    pet={pet}
                    onLike={(id) => console.log('Liked pet:', id)}
                    onSave={(id) => console.log('Saved pet:', id)}
                    onViewDetails={(id) => console.log('View details:', id)}
                    isLiked={false}
                    isSaved={false}
                    className={viewMode === 'list' ? 'max-w-none' : ''}
                  />
                ))}
              </div>
            ) : (
              <Card variant="outlined" className="text-center py-12">
                <CardContent>
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="font-semibold text-neutral-700 mb-2">No pets found</h3>
                  <p className="text-neutral-500 mb-4">
                    Try adjusting your filters or search terms to find more pets.
                  </p>
                  <Button variant="outline" onClick={clearFilters}>
                    Clear Filters
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdoptPage;
