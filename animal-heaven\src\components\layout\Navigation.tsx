'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Heart,
  User,
  Menu,
  X,
  Home,
  Search,
  Users,
  Gift,
  BookOpen,
  Camera,
  Info,
  Phone,
  LogIn,
  UserPlus,
  LogOut,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { ThemeToggle } from '@/components/ui/ThemeToggle';
import { LanguageSelector } from '@/components/ui/LanguageSelector';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';

interface NavigationProps {
  isAuthenticated?: boolean;
  userRole?: 'user' | 'volunteer' | 'admin' | 'shelter-staff';
  savedPetsCount?: number;
}

const Navigation: React.FC<NavigationProps> = ({
  isAuthenticated: propIsAuthenticated,
  userRole = 'user',
  savedPetsCount: propSavedPetsCount
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const { user, logout, isAuthenticated: authIsAuthenticated } = useAuth();

  // Use auth context values if available, otherwise fall back to props
  const isAuthenticated = authIsAuthenticated || propIsAuthenticated || false;
  const savedPetsCount = propSavedPetsCount || 0;

  const navigationItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/adopt', label: 'Adopt', icon: Search },
    { href: '/volunteer', label: 'Volunteer', icon: Users },
    { href: '/donate', label: 'Donate', icon: Gift },
    { href: '/resources', label: 'Resources', icon: BookOpen },
    { href: '/gallery', label: 'Gallery', icon: Camera },
    { href: '/about', label: 'About', icon: Info },
    { href: '/contact', label: 'Contact', icon: Phone },
  ];

  const isActivePath = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className="bg-white/95 backdrop-blur-sm border-b border-orange-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 group">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-amber-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
              <span className="text-white font-bold text-lg">🐾</span>
            </div>
            <div>
              <h1 className="font-primary text-xl font-bold text-gradient">
                Animal Heaven
              </h1>
              <p className="text-xs text-neutral-600 -mt-1">Find Your Perfect Companion</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = isActivePath(item.href);
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                    isActive
                      ? 'bg-orange-100 text-orange-700 shadow-sm'
                      : 'text-neutral-700 hover:bg-orange-50 hover:text-orange-600'
                  )}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-3">
            {isAuthenticated ? (
              <>
                {/* Saved Pets */}
                <Link href="/dashboard">
                  <Button variant="ghost" size="sm" className="relative">
                    <Heart className="w-4 h-4" />
                    {savedPetsCount > 0 && (
                      <Badge
                        variant="primary"
                        size="sm"
                        className="absolute -top-1 -right-1 min-w-[1.25rem] h-5 flex items-center justify-center text-xs"
                      >
                        {savedPetsCount > 99 ? '99+' : savedPetsCount}
                      </Badge>
                    )}
                  </Button>
                </Link>

                {/* User Menu */}
                <Link href="/dashboard">
                  <Button variant="outline" size="sm">
                    <User className="w-4 h-4" />
                    <span className="hidden sm:inline ml-1">
                      {user ? user.firstName : 'Dashboard'}
                    </span>
                  </Button>
                </Link>

                {/* Language Selector */}
                <LanguageSelector variant="dropdown" size="sm" />

                {/* Theme Toggle */}
                <ThemeToggle variant="dropdown" size="sm" />

                {/* Logout */}
                <Button variant="ghost" size="sm" onClick={logout}>
                  <LogOut className="w-4 h-4" />
                  <span className="hidden sm:inline ml-1">Logout</span>
                </Button>

                {/* Admin Panel (if admin) */}
                {(user?.role === 'admin' || user?.role === 'staff') && (
                  <Link href="/admin">
                    <Button variant="secondary" size="sm">
                      Admin
                    </Button>
                  </Link>
                )}
              </>
            ) : (
              <>
                {/* Language Selector */}
                <LanguageSelector variant="dropdown" size="sm" />

                {/* Theme Toggle */}
                <ThemeToggle variant="dropdown" size="sm" />

                <Link href="/login">
                  <Button variant="ghost" size="sm">
                    <LogIn className="w-4 h-4" />
                    <span className="hidden sm:inline ml-1">Login</span>
                  </Button>
                </Link>
                <Link href="/register">
                  <Button variant="primary" size="sm">
                    <UserPlus className="w-4 h-4" />
                    <span className="hidden sm:inline ml-1">Sign Up</span>
                  </Button>
                </Link>
              </>
            )}

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-orange-100 py-4">
            <div className="space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = isActivePath(item.href);
                
                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      'flex items-center space-x-3 px-4 py-3 rounded-lg text-base font-medium transition-all duration-200',
                      isActive
                        ? 'bg-orange-100 text-orange-700'
                        : 'text-neutral-700 hover:bg-orange-50 hover:text-orange-600'
                    )}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.label}</span>
                  </Link>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export { Navigation };
