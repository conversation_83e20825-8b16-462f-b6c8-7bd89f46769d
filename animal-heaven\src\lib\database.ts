// Database connection and models
// In a real application, this would connect to MongoDB using mongoose or similar

export interface Pet {
  id: string;
  name: string;
  type: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  breed: string;
  age: {
    value: number;
    unit: 'months' | 'years';
  };
  gender: 'male' | 'female';
  size: 'small' | 'medium' | 'large';
  color: string;
  description: string;
  personality: string[];
  images: {
    url: string;
    alt: string;
    isPrimary: boolean;
  }[];
  status: 'available' | 'adopted' | 'pending' | 'medical' | 'hold';
  location: {
    shelter: string;
    city: string;
    state: string;
  };
  adoptionFee: number;
  specialNeeds: boolean;
  specialNeedsDescription?: string;
  goodWith: {
    children: boolean;
    dogs: boolean;
    cats: boolean;
  };
  medicalHistory: {
    vaccinated: boolean;
    spayedNeutered: boolean;
    microchipped: boolean;
    healthIssues?: string[];
  };
  intakeDate: string;
  views: number;
  likes: number;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  address?: string;
  role: 'user' | 'volunteer' | 'staff' | 'admin';
  profileImage?: string;
  preferences: {
    petTypes: string[];
    notifications: boolean;
    newsletter: boolean;
  };
  verificationStatus: 'pending' | 'verified' | 'rejected';
  createdAt: string;
  lastLogin: string;
}

export interface Application {
  id: string;
  userId: string;
  petId: string;
  status: 'pending' | 'approved' | 'rejected' | 'interview' | 'home-visit';
  priority: 'high' | 'medium' | 'low';
  submittedDate: string;
  lastUpdate: string;
  applicationData: {
    livingArrangement: string;
    experience: string;
    otherPets: boolean;
    children: boolean;
    workSchedule: string;
    references: {
      name: string;
      relationship: string;
      phone: string;
    }[];
  };
  notes: string[];
  nextStep?: string;
  assignedStaff?: string;
}

export interface Volunteer {
  id: string;
  userId: string;
  skills: string[];
  availability: {
    days: string[];
    timeSlots: string[];
  };
  interests: string[];
  experience: string;
  status: 'active' | 'inactive' | 'pending';
  hoursLogged: number;
  startDate: string;
  backgroundCheckStatus: 'pending' | 'approved' | 'rejected';
}

export interface Donation {
  id: string;
  userId?: string;
  amount: number;
  type: 'one-time' | 'monthly' | 'sponsorship';
  purpose: 'general' | 'medical' | 'food' | 'shelter' | 'specific-pet';
  petId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionId?: string;
  donorInfo: {
    name: string;
    email: string;
    anonymous: boolean;
  };
  date: string;
}

// Mock database - in real app, this would be MongoDB collections
class MockDatabase {
  private pets: Pet[] = [];
  private users: User[] = [];
  private applications: Application[] = [];
  private volunteers: Volunteer[] = [];
  private donations: Donation[] = [];

  // Pet operations
  async getPets(filters?: {
    type?: string;
    size?: string;
    age?: string;
    location?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ pets: Pet[]; total: number }> {
    let filteredPets = [...this.pets];

    if (filters) {
      if (filters.type) {
        filteredPets = filteredPets.filter(pet => pet.type === filters.type);
      }
      if (filters.size) {
        filteredPets = filteredPets.filter(pet => pet.size === filters.size);
      }
      if (filters.status) {
        filteredPets = filteredPets.filter(pet => pet.status === filters.status);
      }
    }

    const total = filteredPets.length;
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 20;
    
    return {
      pets: filteredPets.slice(offset, offset + limit),
      total
    };
  }

  async getPetById(id: string): Promise<Pet | null> {
    return this.pets.find(pet => pet.id === id) || null;
  }

  async createPet(petData: Omit<Pet, 'id' | 'createdAt' | 'updatedAt'>): Promise<Pet> {
    const pet: Pet = {
      ...petData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    this.pets.push(pet);
    return pet;
  }

  async updatePet(id: string, updates: Partial<Pet>): Promise<Pet | null> {
    const index = this.pets.findIndex(pet => pet.id === id);
    if (index === -1) return null;

    this.pets[index] = {
      ...this.pets[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    return this.pets[index];
  }

  async deletePet(id: string): Promise<boolean> {
    const index = this.pets.findIndex(pet => pet.id === id);
    if (index === -1) return false;
    
    this.pets.splice(index, 1);
    return true;
  }

  // User operations
  async getUserById(id: string): Promise<User | null> {
    return this.users.find(user => user.id === id) || null;
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return this.users.find(user => user.email === email) || null;
  }

  async createUser(userData: Omit<User, 'id' | 'createdAt'>): Promise<User> {
    const user: User = {
      ...userData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };
    this.users.push(user);
    return user;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const index = this.users.findIndex(user => user.id === id);
    if (index === -1) return null;

    this.users[index] = { ...this.users[index], ...updates };
    return this.users[index];
  }

  // Application operations
  async getApplications(filters?: {
    userId?: string;
    petId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ applications: Application[]; total: number }> {
    let filteredApplications = [...this.applications];

    if (filters) {
      if (filters.userId) {
        filteredApplications = filteredApplications.filter(app => app.userId === filters.userId);
      }
      if (filters.petId) {
        filteredApplications = filteredApplications.filter(app => app.petId === filters.petId);
      }
      if (filters.status) {
        filteredApplications = filteredApplications.filter(app => app.status === filters.status);
      }
    }

    const total = filteredApplications.length;
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 20;
    
    return {
      applications: filteredApplications.slice(offset, offset + limit),
      total
    };
  }

  async createApplication(applicationData: Omit<Application, 'id' | 'submittedDate' | 'lastUpdate'>): Promise<Application> {
    const application: Application = {
      ...applicationData,
      id: Date.now().toString(),
      submittedDate: new Date().toISOString(),
      lastUpdate: new Date().toISOString()
    };
    this.applications.push(application);
    return application;
  }

  async updateApplication(id: string, updates: Partial<Application>): Promise<Application | null> {
    const index = this.applications.findIndex(app => app.id === id);
    if (index === -1) return null;

    this.applications[index] = {
      ...this.applications[index],
      ...updates,
      lastUpdate: new Date().toISOString()
    };
    return this.applications[index];
  }

  // Donation operations
  async createDonation(donationData: Omit<Donation, 'id' | 'date'>): Promise<Donation> {
    const donation: Donation = {
      ...donationData,
      id: Date.now().toString(),
      date: new Date().toISOString()
    };
    this.donations.push(donation);
    return donation;
  }

  async getDonations(filters?: {
    userId?: string;
    type?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ donations: Donation[]; total: number }> {
    let filteredDonations = [...this.donations];

    if (filters) {
      if (filters.userId) {
        filteredDonations = filteredDonations.filter(donation => donation.userId === filters.userId);
      }
      if (filters.type) {
        filteredDonations = filteredDonations.filter(donation => donation.type === filters.type);
      }
      if (filters.status) {
        filteredDonations = filteredDonations.filter(donation => donation.status === filters.status);
      }
    }

    const total = filteredDonations.length;
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 20;
    
    return {
      donations: filteredDonations.slice(offset, offset + limit),
      total
    };
  }
}

// Export singleton instance
export const db = new MockDatabase();

// Utility functions
export const generateId = () => Date.now().toString();

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const hashPassword = async (password: string): Promise<string> => {
  // In real app, use bcrypt or similar
  return `hashed_${password}`;
};

export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  // In real app, use bcrypt.compare
  return hash === `hashed_${password}`;
};
