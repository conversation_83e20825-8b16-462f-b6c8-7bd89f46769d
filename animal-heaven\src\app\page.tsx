/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React from 'react';
import {
  <PERSON><PERSON>,
  Navigation,
  Footer,
  Hero,
  PetCard
} from '@/components';
import { SuccessStories } from '@/components/sections/SuccessStories';
import { Testimonials } from '@/components/sections/Testimonials';
import { QuickFilters } from '@/components/sections/QuickFilters';

// Types
interface SearchFilters {
  type?: string;
  size?: string;
  age?: string;
  location?: string;
}

// Sample pet data for demonstration
const samplePets = [
  {
    id: '1',
    name: 'Buddy',
    type: 'dog' as const,
    breed: 'Golden Retriever',
    age: { value: 3, unit: 'years' as const },
    gender: 'male' as const,
    size: 'large' as const,
    color: 'Golden',
    description: '<PERSON> is a friendly and energetic Golden Retriever who loves playing fetch and swimming. He\'s great with kids and other dogs.',
    personality: ['friendly', 'energetic', 'playful', 'gentle'],
    images: [
      { url: '/images/pets/buddy.jpg', alt: 'Buddy the Golden Retriever', isPrimary: true }
    ],
    status: 'available' as const,
    location: { shelter: 'Happy Paws Shelter', city: 'Mumbai', state: 'Maharashtra' },
    adoptionFee: 5000,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: false },
    views: 245,
    likes: 89,
    featured: true
  },
  {
    id: '2',
    name: 'Luna',
    type: 'cat' as const,
    breed: 'Persian',
    age: { value: 2, unit: 'years' as const },
    gender: 'female' as const,
    size: 'medium' as const,
    color: 'White',
    description: 'Luna is a calm and affectionate Persian cat who enjoys quiet moments and gentle pets. Perfect for a peaceful home.',
    personality: ['calm', 'gentle', 'independent'],
    images: [
      { url: '/images/pets/luna.jpg', alt: 'Luna the Persian Cat', isPrimary: true }
    ],
    status: 'available' as const,
    location: { shelter: 'Feline Friends', city: 'Delhi', state: 'Delhi' },
    adoptionFee: 3000,
    specialNeeds: false,
    goodWith: { children: true, dogs: false, cats: true },
    views: 156,
    likes: 67,
    featured: false
  }
];

export default function Home() {
  const handleSearch = (query: string, filters: any) => {
    console.log('Search:', query, filters);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
      {/* Navigation */}
      <Navigation
        isAuthenticated={false}
        savedPetsCount={3}
      />

      {/* Hero Section */}
      <Hero onSearch={handleSearch} />

      {/* Quick Filters Section */}
      <QuickFilters onFilterSelect={(filterId) => console.log('Filter selected:', filterId)} />

      {/* Featured Pets Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
              Featured Pets Looking for Homes
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Meet some of our special pets who are ready to bring joy and love to their new families.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {samplePets.map((pet) => (
              <PetCard
                key={pet.id}
                pet={pet}
                onLike={(id) => console.log('Liked pet:', id)}
                onSave={(id) => console.log('Saved pet:', id)}
                onViewDetails={(id) => console.log('View details:', id)}
                isLiked={false}
                isSaved={false}
              />
            ))}
          </div>

          <div className="text-center">
            <Button variant="primary" size="lg">
              View All Available Pets
            </Button>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <SuccessStories />

      {/* Testimonials Section */}
      <Testimonials />



      {/* Footer */}
      <Footer />
    </div>
  );
}
