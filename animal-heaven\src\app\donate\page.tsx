'use client';

import React, { useState } from 'react';
import { 
  Heart, 
  Gift, 
  CreditCard, 
  Smartphone, 
  DollarSign, 
  Users, 
  Shield, 
  Award,
  TrendingUp,
  Calendar,
  Star,
  CheckCircle
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge, 
  Input,
  Navigation,
  Footer
} from '@/components';

interface DonationOption {
  id: string;
  amount: number;
  title: string;
  description: string;
  impact: string;
  popular?: boolean;
}

interface SponsorshipTier {
  id: string;
  name: string;
  monthlyAmount: number;
  benefits: string[];
  color: 'primary' | 'secondary' | 'success' | 'warning';
  popular?: boolean;
}

const donationOptions: DonationOption[] = [
  {
    id: 'food',
    amount: 500,
    title: 'Feed a Pet for a Week',
    description: 'Provide nutritious meals for one pet for an entire week',
    impact: 'Feeds 1 pet for 7 days'
  },
  {
    id: 'medical',
    amount: 2000,
    title: 'Medical Care Package',
    description: 'Cover basic medical checkup, vaccinations, and treatments',
    impact: 'Helps 1 pet get healthy',
    popular: true
  },
  {
    id: 'shelter',
    amount: 1000,
    title: 'Shelter & Care',
    description: 'Provide safe shelter, bedding, and daily care for a month',
    impact: 'Houses 1 pet for 30 days'
  },
  {
    id: 'rescue',
    amount: 5000,
    title: 'Emergency Rescue',
    description: 'Fund emergency rescue operations and immediate care',
    impact: 'Saves 1 pet in crisis'
  }
];

const sponsorshipTiers: SponsorshipTier[] = [
  {
    id: 'friend',
    name: 'Pet Friend',
    monthlyAmount: 500,
    benefits: [
      'Monthly updates on shelter activities',
      'Digital thank you card',
      'Access to volunteer events'
    ],
    color: 'primary'
  },
  {
    id: 'guardian',
    name: 'Pet Guardian',
    monthlyAmount: 1500,
    benefits: [
      'All Pet Friend benefits',
      'Quarterly shelter visit',
      'Pet care guide resources',
      'Priority adoption consultation'
    ],
    color: 'secondary',
    popular: true
  },
  {
    id: 'champion',
    name: 'Pet Champion',
    monthlyAmount: 3000,
    benefits: [
      'All Pet Guardian benefits',
      'Monthly video calls with staff',
      'Sponsor a specific pet',
      'Annual appreciation dinner',
      'Tax benefits consultation'
    ],
    color: 'success'
  },
  {
    id: 'hero',
    name: 'Pet Hero',
    monthlyAmount: 5000,
    benefits: [
      'All Pet Champion benefits',
      'Quarterly impact reports',
      'VIP shelter tours',
      'Recognition on website',
      'Custom pet care packages'
    ],
    color: 'warning'
  }
];

const DonatePage: React.FC = () => {
  const [donationType, setDonationType] = useState<'one-time' | 'monthly'>('one-time');
  const [selectedAmount, setSelectedAmount] = useState<number | null>(null);
  const [customAmount, setCustomAmount] = useState('');
  const [selectedTier, setSelectedTier] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'upi' | 'bank'>('card');
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  const handleAmountSelect = (amount: number) => {
    setSelectedAmount(amount);
    setCustomAmount('');
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    setSelectedAmount(null);
  };

  const handleDonate = () => {
    const amount = selectedAmount || parseInt(customAmount);
    if (amount && amount > 0) {
      setShowPaymentForm(true);
    }
  };

  const handleSponsor = (tierId: string) => {
    setSelectedTier(tierId);
    setShowPaymentForm(true);
  };

  const getFinalAmount = () => {
    return selectedAmount || parseInt(customAmount) || 0;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            Make a Difference Today
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            Your donation directly impacts the lives of animals in need. Every contribution, 
            no matter the size, helps us provide food, medical care, shelter, and love to pets waiting for their forever homes.
          </p>
          
          {/* Impact Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">₹2.5L+</div>
              <div className="text-sm text-neutral-600">Raised This Year</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">500+</div>
              <div className="text-sm text-neutral-600">Pets Helped</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">1,200+</div>
              <div className="text-sm text-neutral-600">Generous Donors</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">95%</div>
              <div className="text-sm text-neutral-600">Goes to Animals</div>
            </div>
          </div>
        </div>

        {/* Donation Type Toggle */}
        <div className="flex justify-center mb-12">
          <div className="bg-white rounded-lg p-1 shadow-md">
            <Button
              variant={donationType === 'one-time' ? 'primary' : 'ghost'}
              onClick={() => setDonationType('one-time')}
              className="rounded-md"
            >
              One-Time Donation
            </Button>
            <Button
              variant={donationType === 'monthly' ? 'primary' : 'ghost'}
              onClick={() => setDonationType('monthly')}
              className="rounded-md"
            >
              Monthly Sponsorship
            </Button>
          </div>
        </div>

        {donationType === 'one-time' ? (
          /* One-Time Donations */
          <div className="space-y-12">
            {/* Quick Donation Options */}
            <section>
              <div className="text-center mb-8">
                <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                  Choose Your Impact
                </h2>
                <p className="text-lg text-neutral-600">
                  Select a donation amount or enter a custom amount
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {donationOptions.map((option) => (
                  <Card 
                    key={option.id}
                    variant={selectedAmount === option.amount ? 'warm' : 'elevated'}
                    className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                      option.popular ? 'ring-2 ring-orange-300 ring-offset-2' : ''
                    }`}
                    onClick={() => handleAmountSelect(option.amount)}
                  >
                    <CardContent className="p-6 text-center">
                      {option.popular && (
                        <Badge variant="primary" size="sm" className="mb-3">
                          Most Popular
                        </Badge>
                      )}
                      <div className="text-2xl font-bold text-orange-600 mb-2">
                        ₹{option.amount.toLocaleString()}
                      </div>
                      <h3 className="font-semibold text-lg mb-2">{option.title}</h3>
                      <p className="text-neutral-600 text-sm mb-3">{option.description}</p>
                      <div className="flex items-center justify-center gap-2 text-green-600 text-sm">
                        <CheckCircle className="w-4 h-4" />
                        <span>{option.impact}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Custom Amount */}
              <Card variant="outlined" className="max-w-md mx-auto">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-lg mb-4 text-center">Custom Amount</h3>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-semibold text-neutral-700">₹</span>
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      value={customAmount}
                      onChange={(e) => handleCustomAmountChange(e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </CardContent>
              </Card>
            </section>

            {/* Payment Section */}
            {(selectedAmount || customAmount) && (
              <section>
                <Card variant="warm" className="max-w-2xl mx-auto">
                  <CardHeader>
                    <CardTitle className="text-center">
                      Complete Your Donation of ₹{getFinalAmount().toLocaleString()}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* Payment Methods */}
                    <div className="mb-6">
                      <h4 className="font-semibold mb-3">Choose Payment Method</h4>
                      <div className="grid grid-cols-3 gap-3">
                        <Button
                          variant={paymentMethod === 'card' ? 'primary' : 'outline'}
                          onClick={() => setPaymentMethod('card')}
                          className="flex flex-col items-center p-4 h-auto"
                        >
                          <CreditCard className="w-6 h-6 mb-2" />
                          <span className="text-sm">Card</span>
                        </Button>
                        <Button
                          variant={paymentMethod === 'upi' ? 'primary' : 'outline'}
                          onClick={() => setPaymentMethod('upi')}
                          className="flex flex-col items-center p-4 h-auto"
                        >
                          <Smartphone className="w-6 h-6 mb-2" />
                          <span className="text-sm">UPI</span>
                        </Button>
                        <Button
                          variant={paymentMethod === 'bank' ? 'primary' : 'outline'}
                          onClick={() => setPaymentMethod('bank')}
                          className="flex flex-col items-center p-4 h-auto"
                        >
                          <DollarSign className="w-6 h-6 mb-2" />
                          <span className="text-sm">Bank</span>
                        </Button>
                      </div>
                    </div>

                    {/* Security Badge */}
                    <div className="flex items-center justify-center gap-2 text-green-600 mb-6">
                      <Shield className="w-5 h-5" />
                      <span className="text-sm">Secure & Encrypted Payment</span>
                    </div>

                    <Button variant="primary" size="lg" className="w-full" onClick={handleDonate}>
                      Donate ₹{getFinalAmount().toLocaleString()} Now
                    </Button>
                  </CardContent>
                </Card>
              </section>
            )}
          </div>
        ) : (
          /* Monthly Sponsorship */
          <div className="space-y-12">
            <section>
              <div className="text-center mb-8">
                <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                  Become a Monthly Sponsor
                </h2>
                <p className="text-lg text-neutral-600">
                  Join our community of regular supporters and make a lasting impact
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {sponsorshipTiers.map((tier) => (
                  <Card 
                    key={tier.id}
                    variant={tier.popular ? 'warm' : 'elevated'}
                    className={`relative transition-all duration-300 hover:scale-105 ${
                      tier.popular ? 'ring-2 ring-orange-300 ring-offset-2' : ''
                    }`}
                  >
                    {tier.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge variant="primary" size="sm">
                          Most Popular
                        </Badge>
                      </div>
                    )}
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        <h3 className="font-semibold text-xl mb-2">{tier.name}</h3>
                        <div className="text-3xl font-bold text-orange-600 mb-1">
                          ₹{tier.monthlyAmount.toLocaleString()}
                        </div>
                        <div className="text-sm text-neutral-500">per month</div>
                      </div>

                      <ul className="space-y-3 mb-6">
                        {tier.benefits.map((benefit, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm">
                            <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>

                      <Button 
                        variant={tier.popular ? 'primary' : 'outline'}
                        className="w-full"
                        onClick={() => handleSponsor(tier.id)}
                      >
                        Choose {tier.name}
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>
          </div>
        )}

        {/* Impact Stories */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
              Your Impact in Action
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              See how your donations have made a real difference in the lives of animals
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card variant="elevated">
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Heart className="w-8 h-8 text-orange-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">Emergency Surgery</h3>
                  <p className="text-neutral-600 text-sm mb-4">
                    Thanks to donor support, we saved Max who needed emergency surgery after an accident.
                    He's now healthy and found his forever home.
                  </p>
                  <Badge variant="success" size="sm">₹15,000 raised</Badge>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">Shelter Expansion</h3>
                  <p className="text-neutral-600 text-sm mb-4">
                    Community donations helped us expand our shelter capacity by 50%,
                    allowing us to rescue and care for more animals in need.
                  </p>
                  <Badge variant="primary" size="sm">₹2.5L raised</Badge>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">Vaccination Drive</h3>
                  <p className="text-neutral-600 text-sm mb-4">
                    Monthly sponsors funded a city-wide vaccination drive,
                    protecting over 200 street animals from preventable diseases.
                  </p>
                  <Badge variant="secondary" size="sm">200+ pets helped</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Transparency Section */}
        <section className="mb-16">
          <Card variant="warm" className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
                  Where Your Money Goes
                </h2>
                <p className="text-lg text-neutral-600">
                  We believe in complete transparency about how donations are used
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="font-semibold text-lg mb-4">Fund Allocation</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-neutral-700">Animal Care & Medical</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-neutral-200 rounded-full overflow-hidden">
                          <div className="w-4/5 h-full bg-orange-500"></div>
                        </div>
                        <span className="text-sm font-semibold">60%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-neutral-700">Shelter Operations</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-neutral-200 rounded-full overflow-hidden">
                          <div className="w-1/4 h-full bg-green-500"></div>
                        </div>
                        <span className="text-sm font-semibold">25%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-neutral-700">Rescue Operations</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-neutral-200 rounded-full overflow-hidden">
                          <div className="w-2/5 h-full bg-blue-500"></div>
                        </div>
                        <span className="text-sm font-semibold">10%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-neutral-700">Administration</span>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-neutral-200 rounded-full overflow-hidden">
                          <div className="w-1/5 h-full bg-neutral-400"></div>
                        </div>
                        <span className="text-sm font-semibold">5%</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-lg mb-4">Our Commitments</h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-neutral-700 text-sm">95% of donations go directly to animal care</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-neutral-700 text-sm">Monthly financial reports available to all donors</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-neutral-700 text-sm">Annual third-party financial audit</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span className="text-neutral-700 text-sm">Regular updates on funded projects</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Other Ways to Help */}
        <section className="mb-16">
          <div className="text-center mb-8">
            <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-4">
              Other Ways to Help
            </h2>
            <p className="text-lg text-neutral-600">
              Can't donate right now? There are many other ways to support our mission
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card variant="outlined">
              <CardContent className="p-6 text-center">
                <Users className="w-12 h-12 text-orange-600 mx-auto mb-4" />
                <h3 className="font-semibold text-lg mb-2">Volunteer</h3>
                <p className="text-neutral-600 text-sm mb-4">
                  Donate your time and skills to help care for animals and support our operations.
                </p>
                <Button variant="outline" size="sm">
                  Learn More
                </Button>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardContent className="p-6 text-center">
                <Gift className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="font-semibold text-lg mb-2">Donate Supplies</h3>
                <p className="text-neutral-600 text-sm mb-4">
                  Contribute food, toys, blankets, and other supplies that animals need daily.
                </p>
                <Button variant="outline" size="sm">
                  View Wishlist
                </Button>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="font-semibold text-lg mb-2">Spread the Word</h3>
                <p className="text-neutral-600 text-sm mb-4">
                  Share our mission on social media and help us reach more potential adopters.
                </p>
                <Button variant="outline" size="sm">
                  Share Now
                </Button>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Tax Benefits */}
        <section className="mb-16">
          <Card variant="elevated" className="max-w-3xl mx-auto">
            <CardContent className="p-8 text-center">
              <Shield className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h2 className="font-primary text-2xl font-bold text-neutral-900 mb-4">
                Tax Benefits Available
              </h2>
              <p className="text-neutral-600 mb-6">
                Animal Heaven is a registered 501(c)(3) non-profit organization.
                Your donations are tax-deductible under Section 80G of the Income Tax Act.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                <div>
                  <h4 className="font-semibold mb-2">What You Get:</h4>
                  <ul className="space-y-1 text-sm text-neutral-600">
                    <li>• Official donation receipt</li>
                    <li>• 50% tax deduction on donations</li>
                    <li>• Annual tax summary report</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Registration Details:</h4>
                  <ul className="space-y-1 text-sm text-neutral-600">
                    <li>• Registration No: 80G/2024/001</li>
                    <li>• PAN: **********</li>
                    <li>• Valid until: March 2027</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default DonatePage;
