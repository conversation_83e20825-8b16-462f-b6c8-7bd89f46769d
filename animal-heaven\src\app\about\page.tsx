'use client';

import React from 'react';
import Image from 'next/image';
import { 
  Heart, 
  Users, 
  Award, 
  Target, 
  Shield, 
  Globe,
  Calendar,
  MapPin,
  Mail,
  Linkedin,
  Twitter,
  Star,
  TrendingUp
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge,
  Navigation,
  Footer
} from '@/components';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  image: string;
  experience: string;
  specialties: string[];
  social: {
    email?: string;
    linkedin?: string;
    twitter?: string;
  };
}

interface Milestone {
  year: string;
  title: string;
  description: string;
  impact: string;
}

const teamMembers: TeamMember[] = [
  {
    id: '1',
    name: 'Dr. <PERSON>',
    role: 'Founder & Chief Veterinarian',
    bio: 'Dr. <PERSON> founded Animal Heaven with a vision to create a world where every pet finds a loving home. With over 15 years of veterinary experience, she leads our medical care and adoption programs.',
    image: '/images/team/sarah.jpg',
    experience: '15+ years',
    specialties: ['Veterinary Medicine', 'Animal Welfare', 'Shelter Management'],
    social: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>',
      twitter: 'https://twitter.com/drsarah<PERSON><PERSON><PERSON>'
    }
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Operations Director',
    bio: 'Michael oversees daily operations and volunteer coordination. His background in non-profit management helps ensure our resources are used effectively to help as many animals as possible.',
    image: '/images/team/michael.jpg',
    experience: '10+ years',
    specialties: ['Operations Management', 'Volunteer Coordination', 'Non-profit Administration'],
    social: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/michaelchen'
    }
  },
  {
    id: '3',
    name: 'Lisa Patel',
    role: 'Adoption Coordinator',
    bio: 'Lisa is passionate about matching pets with perfect families. She guides adopters through the process and ensures every placement is a success story waiting to happen.',
    image: '/images/team/lisa.jpg',
    experience: '8+ years',
    specialties: ['Adoption Counseling', 'Family Matching', 'Pet Behavior Assessment'],
    social: {
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/lisapatel'
    }
  },
  {
    id: '4',
    name: 'David Rodriguez',
    role: 'Rescue Coordinator',
    bio: 'David leads our rescue operations and emergency response team. His dedication to saving animals in crisis situations has helped us rescue over 500 pets in the last two years.',
    image: '/images/team/david.jpg',
    experience: '12+ years',
    specialties: ['Emergency Rescue', 'Crisis Response', 'Animal Transportation'],
    social: {
      email: '<EMAIL>',
      twitter: 'https://twitter.com/davidrescue'
    }
  }
];

const milestones: Milestone[] = [
  {
    year: '2018',
    title: 'Animal Heaven Founded',
    description: 'Started as a small rescue operation with a big dream',
    impact: '50 pets rescued in first year'
  },
  {
    year: '2019',
    title: 'First Shelter Opened',
    description: 'Opened our first permanent shelter facility',
    impact: '200 pets housed and adopted'
  },
  {
    year: '2020',
    title: 'Volunteer Program Launch',
    description: 'Established comprehensive volunteer training program',
    impact: '100+ active volunteers recruited'
  },
  {
    year: '2021',
    title: 'Medical Center Expansion',
    description: 'Added full-service veterinary clinic',
    impact: '1,000+ medical procedures performed'
  },
  {
    year: '2022',
    title: 'Community Outreach',
    description: 'Launched education and awareness programs',
    impact: '50+ schools and communities reached'
  },
  {
    year: '2023',
    title: 'Digital Platform',
    description: 'Launched online adoption platform',
    impact: '500+ online adoptions facilitated'
  },
  {
    year: '2024',
    title: 'Regional Expansion',
    description: 'Expanded operations to 5 cities',
    impact: '2,500+ pets helped annually'
  }
];

const AboutPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-primary text-5xl font-bold text-neutral-900 mb-6">
            About Animal Heaven
          </h1>
          <p className="text-xl text-neutral-600 max-w-3xl mx-auto mb-8">
            We're more than just a shelter – we're a community of passionate animal lovers 
            dedicated to creating a world where every pet finds their perfect family and every family finds their perfect pet.
          </p>
          
          {/* Key Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">2,500+</div>
              <div className="text-sm text-neutral-600">Pets Adopted</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">150+</div>
              <div className="text-sm text-neutral-600">Active Volunteers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">6</div>
              <div className="text-sm text-neutral-600">Years of Service</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">5</div>
              <div className="text-sm text-neutral-600">Cities Served</div>
            </div>
          </div>
        </div>

        {/* Mission, Vision, Values */}
        <section className="mb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card variant="warm">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Target className="w-8 h-8 text-orange-600" />
                </div>
                <h3 className="font-primary text-2xl font-bold text-neutral-900 mb-4">Our Mission</h3>
                <p className="text-neutral-600 leading-relaxed">
                  To rescue, rehabilitate, and rehome animals in need while educating our community 
                  about responsible pet ownership and animal welfare.
                </p>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Globe className="w-8 h-8 text-green-600" />
                </div>
                <h3 className="font-primary text-2xl font-bold text-neutral-900 mb-4">Our Vision</h3>
                <p className="text-neutral-600 leading-relaxed">
                  A world where every animal is valued, protected, and loved – where no pet goes without 
                  a caring home and no family goes without the joy of pet companionship.
                </p>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Heart className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="font-primary text-2xl font-bold text-neutral-900 mb-4">Our Values</h3>
                <p className="text-neutral-600 leading-relaxed">
                  Compassion, integrity, and dedication guide everything we do. We believe in 
                  transparency, community collaboration, and treating every animal with dignity and respect.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Our Story */}
        <section className="mb-16">
          <Card variant="warm" className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
                  Our Story
                </h2>
                <p className="text-lg text-neutral-600">
                  How a small dream became a life-changing reality for thousands of animals
                </p>
              </div>

              <div className="prose prose-lg max-w-none text-neutral-700">
                <p className="mb-6">
                  Animal Heaven began in 2018 when Dr. Sarah Johnson, a veterinarian with a passion for animal welfare, 
                  noticed the overwhelming number of homeless pets in her community. What started as fostering a few 
                  animals in her own home quickly grew into something much bigger.
                </p>
                
                <p className="mb-6">
                  With the support of dedicated volunteers and generous donors, we opened our first shelter facility 
                  in 2019. Since then, we've grown from a small rescue operation to a comprehensive animal welfare 
                  organization serving multiple cities across the region.
                </p>
                
                <p className="mb-6">
                  Today, Animal Heaven operates state-of-the-art facilities that include medical clinics, training centers, 
                  and comfortable living spaces for animals awaiting their forever homes. But our greatest achievement 
                  isn't our facilities – it's the thousands of successful adoptions and the countless lives we've touched.
                </p>
                
                <p>
                  Every day, we're reminded that our work is about more than just finding homes for pets. We're creating 
                  families, healing hearts, and building a more compassionate community one adoption at a time.
                </p>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Timeline */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
              Our Journey
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Key milestones in our mission to help animals find loving homes
            </p>
          </div>

          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-orange-200"></div>

            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={milestone.year} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card variant="elevated">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-2 mb-3">
                          <Badge variant="primary" size="sm">{milestone.year}</Badge>
                          <Calendar className="w-4 h-4 text-orange-600" />
                        </div>
                        <h3 className="font-semibold text-lg mb-2">{milestone.title}</h3>
                        <p className="text-neutral-600 text-sm mb-3">{milestone.description}</p>
                        <div className="flex items-center gap-2 text-green-600 text-sm">
                          <TrendingUp className="w-4 h-4" />
                          <span>{milestone.impact}</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Timeline Dot */}
                  <div className="relative z-10 w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg"></div>

                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
              Meet Our Team
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              The passionate people behind Animal Heaven who make our mission possible
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member) => (
              <Card key={member.id} variant="elevated" className="group hover:scale-105 transition-all duration-300">
                <CardContent className="p-6 text-center">
                  {/* Profile Image */}
                  <div className="relative w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden bg-gradient-to-br from-orange-200 to-green-200">
                    <Image
                      src={member.image}
                      alt={member.name}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    {/* Fallback initials */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-neutral-600 font-semibold text-lg">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                  </div>

                  <h3 className="font-semibold text-lg mb-1">{member.name}</h3>
                  <p className="text-orange-600 font-medium text-sm mb-3">{member.role}</p>

                  <div className="mb-4">
                    <Badge variant="outline" size="sm" className="mb-2">
                      {member.experience} experience
                    </Badge>
                  </div>

                  <p className="text-neutral-600 text-sm mb-4 leading-relaxed">
                    {member.bio}
                  </p>

                  {/* Specialties */}
                  <div className="mb-4">
                    <h4 className="font-semibold text-xs text-neutral-700 mb-2">Specialties:</h4>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {member.specialties.map((specialty) => (
                        <Badge key={specialty} variant="outline" size="sm" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Social Links */}
                  <div className="flex justify-center gap-2">
                    {member.social.email && (
                      <Button variant="ghost" size="sm" className="p-2">
                        <Mail className="w-4 h-4" />
                      </Button>
                    )}
                    {member.social.linkedin && (
                      <Button variant="ghost" size="sm" className="p-2">
                        <Linkedin className="w-4 h-4" />
                      </Button>
                    )}
                    {member.social.twitter && (
                      <Button variant="ghost" size="sm" className="p-2">
                        <Twitter className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default AboutPage;
