'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'warm';
  inputSize?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  error?: string;
  label?: string;
  helper?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    type = 'text',
    variant = 'default',
    inputSize = 'md',
    leftIcon,
    rightIcon,
    error,
    label,
    helper,
    id,
    ...props 
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

    const baseStyles = `
      flex w-full rounded-lg border bg-white px-3 py-2 text-sm 
      transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium 
      placeholder:text-neutral-500 focus-visible:outline-none focus-visible:ring-2 
      focus-visible:ring-orange-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed 
      disabled:opacity-50
    `;

    const variants = {
      default: `
        border-neutral-300 hover:border-neutral-400 focus:border-orange-500
      `,
      warm: `
        border-orange-200 bg-orange-50/50 hover:border-orange-300 focus:border-orange-500
      `
    };

    const sizes = {
      sm: 'h-8 px-2 text-xs',
      md: 'h-10 px-3 text-sm',
      lg: 'h-12 px-4 text-base'
    };

    const hasError = !!error;
    const errorStyles = hasError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : '';

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-neutral-700 mb-1"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-500">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            id={inputId}
            className={cn(
              baseStyles,
              variants[variant],
              sizes[inputSize],
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              errorStyles,
              className
            )}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-500">
              {rightIcon}
            </div>
          )}
        </div>

        {error && (
          <p className="mt-1 text-xs text-red-600">{error}</p>
        )}
        
        {helper && !error && (
          <p className="mt-1 text-xs text-neutral-500">{helper}</p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input };
