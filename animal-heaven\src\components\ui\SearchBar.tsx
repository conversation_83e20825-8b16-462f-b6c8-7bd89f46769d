'use client';

import React, { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

export interface SearchFilters {
  type?: string;
  breed?: string;
  size?: string;
  age?: string;
  location?: string;
  gender?: string;
}

interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string, filters: SearchFilters) => void;
  onFiltersChange?: (filters: SearchFilters) => void;
  showFilters?: boolean;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search for your perfect companion...",
  onSearch,
  onFiltersChange,
  showFilters = true,
  className
}) => {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  const [showFilterPanel, setShowFilterPanel] = useState(false);

  const handleSearch = () => {
    onSearch?.(query, filters);
  };

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearFilter = (key: keyof SearchFilters) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearAllFilters = () => {
    setFilters({});
    onFiltersChange?.({});
  };

  const activeFiltersCount = Object.keys(filters).length;

  return (
    <div className={cn('w-full max-w-4xl mx-auto', className)}>
      {/* Main Search Bar */}
      <div className="relative">
        <div className="flex gap-2">
          <div className="flex-1">
            <Input
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder={placeholder}
              leftIcon={<Search className="w-4 h-4" />}
              inputSize="lg"
              variant="warm"
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          
          {showFilters && (
            <Button
              variant={showFilterPanel ? 'primary' : 'outline'}
              size="lg"
              onClick={() => setShowFilterPanel(!showFilterPanel)}
              leftIcon={<Filter className="w-4 h-4" />}
            >
              Filters
              {activeFiltersCount > 0 && (
                <Badge variant="secondary" size="sm" className="ml-1">
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          )}
          
          <Button
            variant="primary"
            size="lg"
            onClick={handleSearch}
          >
            Search
          </Button>
        </div>

        {/* Active Filters Display */}
        {activeFiltersCount > 0 && (
          <div className="mt-3 flex flex-wrap gap-2 items-center">
            <span className="text-sm text-neutral-600">Active filters:</span>
            {Object.entries(filters).map(([key, value]) => (
              <Badge
                key={key}
                variant="primary"
                className="cursor-pointer hover:bg-orange-200"
                onClick={() => clearFilter(key as keyof SearchFilters)}
              >
                {key}: {value}
                <X className="w-3 h-3 ml-1" />
              </Badge>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>

      {/* Filter Panel */}
      {showFilterPanel && (
        <div className="mt-4 p-4 bg-white rounded-lg border border-orange-200 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* Pet Type */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Pet Type
              </label>
              <select
                value={filters.type || ''}
                onChange={(e) => handleFilterChange('type', e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Any Type</option>
                <option value="dog">Dog</option>
                <option value="cat">Cat</option>
                <option value="bird">Bird</option>
                <option value="rabbit">Rabbit</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Size */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Size
              </label>
              <select
                value={filters.size || ''}
                onChange={(e) => handleFilterChange('size', e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Any Size</option>
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
                <option value="extra-large">Extra Large</option>
              </select>
            </div>

            {/* Age */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Age
              </label>
              <select
                value={filters.age || ''}
                onChange={(e) => handleFilterChange('age', e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Any Age</option>
                <option value="0-1">Puppy/Kitten (0-1 year)</option>
                <option value="1-3">Young (1-3 years)</option>
                <option value="3-7">Adult (3-7 years)</option>
                <option value="7-15">Senior (7+ years)</option>
              </select>
            </div>

            {/* Gender */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Gender
              </label>
              <select
                value={filters.gender || ''}
                onChange={(e) => handleFilterChange('gender', e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              >
                <option value="">Any Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
              </select>
            </div>

            {/* Location */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Location
              </label>
              <Input
                value={filters.location || ''}
                onChange={(e) => handleFilterChange('location', e.target.value)}
                placeholder="City or State"
                variant="default"
              />
            </div>

            {/* Breed */}
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-2">
                Breed
              </label>
              <Input
                value={filters.breed || ''}
                onChange={(e) => handleFilterChange('breed', e.target.value)}
                placeholder="Enter breed"
                variant="default"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export { SearchBar };
export type { SearchFilters };
