const express = require('express');
const Pet = require('../models/Pet');
const { auth, adminAuth } = require('../middleware/auth');

const router = express.Router();

// Get all pets with filtering and pagination
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      type,
      breed,
      size,
      gender,
      age,
      status = 'available',
      location,
      featured,
      search
    } = req.query;

    // Build filter object
    const filter = {};
    
    if (type) filter.type = type;
    if (breed) filter.breed = new RegExp(breed, 'i');
    if (size) filter.size = size;
    if (gender) filter.gender = gender;
    if (status) filter.status = status;
    if (featured) filter.featured = featured === 'true';
    
    if (age) {
      const ageRange = age.split('-');
      if (ageRange.length === 2) {
        filter['age.value'] = {
          $gte: parseInt(ageRange[0]),
          $lte: parseInt(ageRange[1])
        };
      }
    }

    if (location) {
      filter.$or = [
        { 'location.city': new RegExp(location, 'i') },
        { 'location.state': new RegExp(location, 'i') }
      ];
    }

    if (search) {
      filter.$or = [
        { name: new RegExp(search, 'i') },
        { breed: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') }
      ];
    }

    const skip = (page - 1) * limit;
    
    const pets = await Pet.find(filter)
      .sort({ featured: -1, createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Pet.countDocuments(filter);

    res.json({
      pets,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total,
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Get pets error:', error);
    res.status(500).json({ message: 'Server error while fetching pets' });
  }
});

// Get single pet by ID
router.get('/:id', async (req, res) => {
  try {
    const pet = await Pet.findById(req.params.id);
    
    if (!pet) {
      return res.status(404).json({ message: 'Pet not found' });
    }

    // Increment view count
    pet.views += 1;
    await pet.save();

    res.json({ pet });
  } catch (error) {
    console.error('Get pet error:', error);
    res.status(500).json({ message: 'Server error while fetching pet' });
  }
});

// Get featured pets
router.get('/featured/list', async (req, res) => {
  try {
    const pets = await Pet.find({ 
      featured: true, 
      status: 'available' 
    })
    .sort({ createdAt: -1 })
    .limit(6);

    res.json({ pets });
  } catch (error) {
    console.error('Get featured pets error:', error);
    res.status(500).json({ message: 'Server error while fetching featured pets' });
  }
});

// Get similar pets
router.get('/:id/similar', async (req, res) => {
  try {
    const pet = await Pet.findById(req.params.id);
    
    if (!pet) {
      return res.status(404).json({ message: 'Pet not found' });
    }

    const similarPets = await Pet.find({
      _id: { $ne: pet._id },
      type: pet.type,
      status: 'available',
      $or: [
        { breed: pet.breed },
        { size: pet.size },
        { 'age.value': { $gte: pet.age.value - 1, $lte: pet.age.value + 1 } }
      ]
    })
    .limit(4)
    .sort({ createdAt: -1 });

    res.json({ pets: similarPets });
  } catch (error) {
    console.error('Get similar pets error:', error);
    res.status(500).json({ message: 'Server error while fetching similar pets' });
  }
});

// Like a pet
router.post('/:id/like', auth, async (req, res) => {
  try {
    const pet = await Pet.findById(req.params.id);
    
    if (!pet) {
      return res.status(404).json({ message: 'Pet not found' });
    }

    pet.likes += 1;
    await pet.save();

    res.json({ message: 'Pet liked successfully', likes: pet.likes });
  } catch (error) {
    console.error('Like pet error:', error);
    res.status(500).json({ message: 'Server error while liking pet' });
  }
});

// Save/unsave a pet (for user's saved list)
router.post('/:id/save', auth, async (req, res) => {
  try {
    const User = require('../models/User');
    const user = await User.findById(req.userId);
    const petId = req.params.id;

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const isAlreadySaved = user.savedPets.includes(petId);

    if (isAlreadySaved) {
      user.savedPets = user.savedPets.filter(id => id.toString() !== petId);
      await user.save();
      res.json({ message: 'Pet removed from saved list', saved: false });
    } else {
      user.savedPets.push(petId);
      await user.save();
      res.json({ message: 'Pet saved successfully', saved: true });
    }
  } catch (error) {
    console.error('Save pet error:', error);
    res.status(500).json({ message: 'Server error while saving pet' });
  }
});

// Get pet statistics (admin only)
router.get('/stats/overview', adminAuth, async (req, res) => {
  try {
    const stats = await Pet.aggregate([
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const typeStats = await Pet.aggregate([
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({ statusStats: stats, typeStats });
  } catch (error) {
    console.error('Get pet stats error:', error);
    res.status(500).json({ message: 'Server error while fetching pet statistics' });
  }
});

module.exports = router;
