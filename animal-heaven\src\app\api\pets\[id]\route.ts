import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';

// GET /api/pets/[id] - Get a specific pet by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const pet = await db.getPetById(params.id);
    
    if (!pet) {
      return NextResponse.json(
        { success: false, error: 'Pet not found' },
        { status: 404 }
      );
    }

    // Increment view count
    await db.updatePet(params.id, { views: pet.views + 1 });
    
    return NextResponse.json({
      success: true,
      data: { ...pet, views: pet.views + 1 }
    });
  } catch (error) {
    console.error('Error fetching pet:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch pet' },
      { status: 500 }
    );
  }
}

// PUT /api/pets/[id] - Update a specific pet (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // In real app, validate admin authentication
    // const user = await validateAuthToken(request);
    // if (!user || (user.role !== 'admin' && user.role !== 'staff')) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    const existingPet = await db.getPetById(params.id);
    if (!existingPet) {
      return NextResponse.json(
        { success: false, error: 'Pet not found' },
        { status: 404 }
      );
    }

    // Filter out fields that shouldn't be updated directly
    const { id, createdAt, views, likes, ...updateData } = body;
    
    const updatedPet = await db.updatePet(params.id, updateData);
    
    return NextResponse.json({
      success: true,
      data: updatedPet,
      message: 'Pet updated successfully'
    });
  } catch (error) {
    console.error('Error updating pet:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update pet' },
      { status: 500 }
    );
  }
}

// DELETE /api/pets/[id] - Delete a specific pet (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // In real app, validate admin authentication
    // const user = await validateAuthToken(request);
    // if (!user || (user.role !== 'admin' && user.role !== 'staff')) {
    //   return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    // }

    const existingPet = await db.getPetById(params.id);
    if (!existingPet) {
      return NextResponse.json(
        { success: false, error: 'Pet not found' },
        { status: 404 }
      );
    }

    const deleted = await db.deletePet(params.id);
    
    if (deleted) {
      return NextResponse.json({
        success: true,
        message: 'Pet deleted successfully'
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to delete pet' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error deleting pet:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete pet' },
      { status: 500 }
    );
  }
}
