'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { 
  Heart, 
  Share2, 
  MapPin, 
  Calendar, 
  User, 
  Phone, 
  Mail, 
  ChevronLeft, 
  ChevronRight,
  Play,
  Pause,
  Star,
  Shield,
  Home,
  Users
} from 'lucide-react';
import { 
  Button, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge, 
  Navigation,
  Footer,
  PetCard
} from '@/components';
import { Pet } from '@/components/pets/PetCard';
import { formatAge } from '@/lib/utils';

// Sample pet data (in real app, this would come from API)
const samplePet: Pet = {
  id: '1',
  name: 'Buddy',
  type: 'dog',
  breed: 'Golden Retriever',
  age: { value: 3, unit: 'years' },
  gender: 'male',
  size: 'large',
  color: 'Golden',
  description: '<PERSON> is a friendly and energetic Golden Retriever who loves playing fetch and swimming. He\'s great with kids and other dogs, making him the perfect family companion. <PERSON> has been with us for 3 months and is fully house-trained, knows basic commands, and walks well on a leash.',
  personality: ['friendly', 'energetic', 'playful', 'gentle', 'social', 'intelligent'],
  images: [
    { url: '/images/pets/buddy-1.jpg', alt: 'Buddy playing in the yard', isPrimary: true },
    { url: '/images/pets/buddy-2.jpg', alt: 'Buddy with his favorite toy', isPrimary: false },
    { url: '/images/pets/buddy-3.jpg', alt: 'Buddy during training session', isPrimary: false },
    { url: '/images/pets/buddy-4.jpg', alt: 'Buddy relaxing indoors', isPrimary: false }
  ],
  status: 'available',
  location: { shelter: 'Happy Paws Shelter', city: 'Mumbai', state: 'Maharashtra' },
  adoptionFee: 5000,
  specialNeeds: false,
  goodWith: { children: true, dogs: true, cats: false },
  views: 245,
  likes: 89,
  featured: true
};

// Extended pet data for profile
const extendedPetData = {
  ...samplePet,
  story: 'Buddy came to us as a young puppy when his previous family could no longer care for him due to a move overseas. Despite this early setback, Buddy has remained optimistic and loving. He\'s spent the last few months learning manners and social skills with our volunteers and has become one of our most well-behaved residents.',
  healthInfo: {
    vaccinated: true,
    spayedNeutered: true,
    microchipped: true,
    lastVetVisit: '2024-01-15',
    medicalConditions: [],
    weight: '28 kg',
    nextVetDate: '2024-04-15'
  },
  behaviorNotes: [
    'House-trained and crate-trained',
    'Knows basic commands: sit, stay, come, down',
    'Walks well on leash with minimal pulling',
    'Enjoys car rides and travel',
    'Good with children of all ages',
    'Plays well with other dogs at the park'
  ],
  idealHome: [
    'Active family or individual who enjoys outdoor activities',
    'Home with a yard for play and exercise',
    'Experience with medium to large breed dogs preferred',
    'Children welcome - Buddy loves kids!',
    'Other friendly dogs in the home would be a plus'
  ],
  videos: [
    { url: '/videos/buddy-playing.mp4', title: 'Buddy Playing Fetch', description: 'Watch Buddy show off his fetch skills!' }
  ]
};

// Similar pets for recommendations
const similarPets: Pet[] = [
  {
    id: '2',
    name: 'Charlie',
    type: 'dog',
    breed: 'Labrador Mix',
    age: { value: 2, unit: 'years' },
    gender: 'male',
    size: 'large',
    color: 'Brown',
    description: 'Charlie is a playful young dog who loves adventures.',
    personality: ['playful', 'energetic', 'social'],
    images: [{ url: '/images/pets/charlie.jpg', alt: 'Charlie', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Happy Paws Shelter', city: 'Mumbai', state: 'Maharashtra' },
    adoptionFee: 4500,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: true },
    views: 189,
    likes: 92,
    featured: false
  },
  {
    id: '3',
    name: 'Max',
    type: 'dog',
    breed: 'Golden Retriever Mix',
    age: { value: 4, unit: 'years' },
    gender: 'male',
    size: 'large',
    color: 'Golden',
    description: 'Max is a gentle giant who loves cuddles and treats.',
    personality: ['gentle', 'calm', 'friendly'],
    images: [{ url: '/images/pets/max.jpg', alt: 'Max', isPrimary: true }],
    status: 'available',
    location: { shelter: 'Happy Paws Shelter', city: 'Mumbai', state: 'Maharashtra' },
    adoptionFee: 5500,
    specialNeeds: false,
    goodWith: { children: true, dogs: true, cats: false },
    views: 156,
    likes: 78,
    featured: false
  }
];

const PetProfilePage: React.FC = () => {
  const params = useParams();
  const petId = params.id as string;
  
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [showAdoptionForm, setShowAdoptionForm] = useState(false);

  // In real app, fetch pet data based on petId
  const pet = extendedPetData;

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % pet.images.length);
  };

  const previousImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + pet.images.length) % pet.images.length);
  };

  const handleAdopt = () => {
    setShowAdoptionForm(true);
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `Meet ${pet.name} - Available for Adoption`,
        text: `${pet.name} is a ${pet.age.value} year old ${pet.breed} looking for a loving home.`,
        url: window.location.href,
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-green-50">
      <Navigation isAuthenticated={false} savedPetsCount={3} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <div className="flex items-center space-x-2 text-sm text-neutral-600">
            <a href="/" className="hover:text-orange-600">Home</a>
            <span>/</span>
            <a href="/adopt" className="hover:text-orange-600">Adopt</a>
            <span>/</span>
            <span className="text-neutral-900">{pet.name}</span>
          </div>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <Card variant="elevated" padding="none" className="overflow-hidden">
              <div className="relative aspect-[4/3]">
                <Image
                  src={pet.images[currentImageIndex]?.url || '/placeholder-pet.jpg'}
                  alt={pet.images[currentImageIndex]?.alt || pet.name}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                
                {/* Fallback gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-orange-200 to-green-200 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-8xl mb-4">
                      {pet.type === 'dog' ? '🐕' : pet.type === 'cat' ? '🐱' : '🐾'}
                    </div>
                    <p className="text-neutral-600 font-medium text-xl">{pet.name}</p>
                  </div>
                </div>

                {/* Navigation Arrows */}
                {pet.images.length > 1 && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={previousImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white"
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={nextImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 backdrop-blur-sm hover:bg-white"
                    >
                      <ChevronRight className="w-5 h-5" />
                    </Button>
                  </>
                )}

                {/* Image Indicators */}
                {pet.images.length > 1 && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {pet.images.map((_, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`w-2 h-2 rounded-full transition-all ${
                          index === currentImageIndex ? 'bg-white scale-125' : 'bg-white/60'
                        }`}
                      />
                    ))}
                  </div>
                )}

                {/* Status Badge */}
                <div className="absolute top-4 left-4">
                  <Badge 
                    variant={pet.status === 'available' ? 'success' : 'warning'}
                    size="sm"
                  >
                    {pet.status === 'available' ? 'Available' : 'Pending Adoption'}
                  </Badge>
                </div>

                {/* Featured Badge */}
                {pet.featured && (
                  <div className="absolute top-4 right-4">
                    <Badge variant="primary" size="sm">
                      ⭐ Featured
                    </Badge>
                  </div>
                )}
              </div>

              {/* Thumbnail Strip */}
              {pet.images.length > 1 && (
                <div className="p-4 bg-neutral-50">
                  <div className="flex space-x-2 overflow-x-auto">
                    {pet.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                          index === currentImageIndex ? 'border-orange-500' : 'border-transparent'
                        }`}
                      >
                        <Image
                          src={image.url}
                          alt={image.alt}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Pet Details */}
            <Card variant="warm">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-3xl">{pet.name}</CardTitle>
                    <p className="text-lg text-neutral-600 mt-1">
                      {pet.breed} • {formatAge(pet.age.value, pet.age.unit)} • {pet.gender}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-orange-600">
                      {pet.adoptionFee === 0 ? 'Free' : `₹${pet.adoptionFee.toLocaleString()}`}
                    </div>
                    <div className="text-sm text-neutral-500">Adoption Fee</div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-white rounded-lg">
                      <div className="text-sm text-neutral-500">Size</div>
                      <div className="font-semibold capitalize">{pet.size}</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg">
                      <div className="text-sm text-neutral-500">Color</div>
                      <div className="font-semibold">{pet.color}</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg">
                      <div className="text-sm text-neutral-500">Weight</div>
                      <div className="font-semibold">{pet.healthInfo.weight}</div>
                    </div>
                    <div className="text-center p-3 bg-white rounded-lg">
                      <div className="text-sm text-neutral-500">Views</div>
                      <div className="font-semibold">{pet.views}</div>
                    </div>
                  </div>

                  {/* Description */}
                  <div>
                    <h3 className="font-semibold text-lg mb-3">About {pet.name}</h3>
                    <p className="text-neutral-700 leading-relaxed">{pet.description}</p>
                  </div>

                  {/* Personality Traits */}
                  <div>
                    <h3 className="font-semibold text-lg mb-3">Personality</h3>
                    <div className="flex flex-wrap gap-2">
                      {pet.personality.map((trait) => (
                        <Badge key={trait} variant="outline" className="capitalize">
                          {trait}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Good With */}
                  <div>
                    <h3 className="font-semibold text-lg mb-3">Good With</h3>
                    <div className="flex gap-4">
                      <div className={`flex items-center gap-2 ${pet.goodWith.children ? 'text-green-600' : 'text-neutral-400'}`}>
                        <Users className="w-4 h-4" />
                        <span className="text-sm">Children</span>
                      </div>
                      <div className={`flex items-center gap-2 ${pet.goodWith.dogs ? 'text-green-600' : 'text-neutral-400'}`}>
                        <span className="text-sm">🐕 Dogs</span>
                      </div>
                      <div className={`flex items-center gap-2 ${pet.goodWith.cats ? 'text-green-600' : 'text-neutral-400'}`}>
                        <span className="text-sm">🐱 Cats</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Pet Story */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>My Story</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-neutral-700 leading-relaxed">{pet.story}</p>
              </CardContent>
            </Card>

            {/* Health Information */}
            <Card variant="outlined">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="w-5 h-5 text-green-600" />
                  Health Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">Medical Status</h4>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span className={`w-2 h-2 rounded-full ${pet.healthInfo.vaccinated ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span className="text-sm">Vaccinated</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`w-2 h-2 rounded-full ${pet.healthInfo.spayedNeutered ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span className="text-sm">Spayed/Neutered</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`w-2 h-2 rounded-full ${pet.healthInfo.microchipped ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span className="text-sm">Microchipped</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Vet Information</h4>
                    <div className="space-y-1 text-sm text-neutral-600">
                      <div>Last Vet Visit: {new Date(pet.healthInfo.lastVetVisit).toLocaleDateString()}</div>
                      <div>Next Checkup: {new Date(pet.healthInfo.nextVetDate).toLocaleDateString()}</div>
                      <div>Medical Conditions: {pet.healthInfo.medicalConditions.length === 0 ? 'None' : pet.healthInfo.medicalConditions.join(', ')}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Behavior Notes */}
            <Card variant="warm">
              <CardHeader>
                <CardTitle>Behavior & Training</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {pet.behaviorNotes.map((note, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span className="text-neutral-700">{note}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Ideal Home */}
            <Card variant="outlined">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Home className="w-5 h-5 text-orange-600" />
                  Ideal Home
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {pet.idealHome.map((requirement, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></span>
                      <span className="text-neutral-700">{requirement}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Action Buttons */}
            <Card variant="elevated" className="sticky top-4">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Button
                    variant="primary"
                    size="lg"
                    className="w-full"
                    onClick={handleAdopt}
                    disabled={pet.status !== 'available'}
                  >
                    {pet.status === 'available' ? 'Apply to Adopt' : 'Adoption Pending'}
                  </Button>

                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={isLiked ? 'primary' : 'outline'}
                      size="sm"
                      onClick={handleLike}
                      className="flex-1"
                    >
                      <Heart className={`w-4 h-4 ${isLiked ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      variant={isSaved ? 'secondary' : 'outline'}
                      size="sm"
                      onClick={handleSave}
                      className="flex-1"
                    >
                      📋
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleShare}
                      className="flex-1"
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>

                  <div className="text-center pt-4 border-t">
                    <div className="flex items-center justify-center gap-1 text-yellow-500 mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 fill-current" />
                      ))}
                    </div>
                    <p className="text-sm text-neutral-600">
                      {pet.likes} people love {pet.name}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Shelter Information */}
            <Card variant="warm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  Shelter Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-semibold">{pet.location.shelter}</h4>
                    <p className="text-sm text-neutral-600">
                      {pet.location.city}, {pet.location.state}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Button variant="outline" size="sm" className="w-full">
                      <Phone className="w-4 h-4 mr-2" />
                      Call Shelter
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <Mail className="w-4 h-4 mr-2" />
                      Email Shelter
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      <MapPin className="w-4 h-4 mr-2" />
                      Get Directions
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card variant="outlined">
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Profile Views</span>
                    <span className="font-semibold">{pet.views}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Likes</span>
                    <span className="font-semibold">{pet.likes}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Time at Shelter</span>
                    <span className="font-semibold">3 months</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-600">Last Updated</span>
                    <span className="font-semibold">2 days ago</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Similar Pets */}
        <section className="mt-16">
          <h2 className="font-primary text-3xl font-bold text-neutral-900 mb-8 text-center">
            Similar Pets You Might Love
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {similarPets.map((similarPet) => (
              <PetCard
                key={similarPet.id}
                pet={similarPet}
                onLike={(id) => console.log('Liked pet:', id)}
                onSave={(id) => console.log('Saved pet:', id)}
                onViewDetails={(id) => console.log('View details:', id)}
                isLiked={false}
                isSaved={false}
              />
            ))}
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default PetProfilePage;
