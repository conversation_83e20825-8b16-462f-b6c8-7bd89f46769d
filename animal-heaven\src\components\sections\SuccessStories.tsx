'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, Heart, Calendar, MapPin } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { cn } from '@/lib/utils';

interface SuccessStory {
  id: string;
  petName: string;
  petType: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  adopterName: string;
  adopterLocation: string;
  adoptionDate: string;
  story: string;
  beforeImage: string;
  afterImage: string;
  featured: boolean;
}

interface SuccessStoriesProps {
  stories?: SuccessStory[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

const defaultStories: SuccessStory[] = [
  {
    id: '1',
    petName: 'Max',
    petType: 'dog',
    adopterName: 'The Johnson Family',
    adopterLocation: 'Mumbai, Maharashtra',
    adoptionDate: '2024-01-15',
    story: '<PERSON> was a shy rescue dog who had been at the shelter for 8 months. The <PERSON> family fell in love with his gentle nature and gave him the patient, loving home he needed. Now <PERSON> is a confident, happy dog who loves playing with the kids and going on family adventures.',
    beforeImage: '/images/success/max-before.jpg',
    afterImage: '/images/success/max-after.jpg',
    featured: true
  },
  {
    id: '2',
    petName: 'Luna',
    petType: 'cat',
    adopterName: 'Sarah & Mike',
    adopterLocation: 'Delhi, Delhi',
    adoptionDate: '2024-02-20',
    story: 'Luna was found as a tiny kitten on the streets. After months of care at the shelter, Sarah and Mike adopted her. Luna has transformed into a confident, playful cat who brings joy and laughter to their home every day.',
    beforeImage: '/images/success/luna-before.jpg',
    afterImage: '/images/success/luna-after.jpg',
    featured: true
  },
  {
    id: '3',
    petName: 'Buddy',
    petType: 'dog',
    adopterName: 'The Patel Family',
    adopterLocation: 'Bangalore, Karnataka',
    adoptionDate: '2024-03-10',
    story: 'Buddy was an older dog who many thought would never find a home. The Patel family saw past his age and gave him the loving retirement he deserved. Buddy now spends his days being spoiled and loved by his new family.',
    beforeImage: '/images/success/buddy-before.jpg',
    afterImage: '/images/success/buddy-after.jpg',
    featured: false
  }
];

const SuccessStories: React.FC<SuccessStoriesProps> = ({
  stories = defaultStories,
  autoPlay = true,
  autoPlayInterval = 6000
}) => {
  const [currentStory, setCurrentStory] = useState(0);
  const [showAfterImage, setShowAfterImage] = useState(false);

  React.useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      setCurrentStory((prev) => (prev + 1) % stories.length);
      setShowAfterImage(false);
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, stories.length]);

  const goToPrevious = () => {
    setCurrentStory((prev) => (prev - 1 + stories.length) % stories.length);
    setShowAfterImage(false);
  };

  const goToNext = () => {
    setCurrentStory((prev) => (prev + 1) % stories.length);
    setShowAfterImage(false);
  };

  const currentStoryData = stories[currentStory];

  return (
    <section className="py-16 bg-gradient-to-br from-green-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
            Happy Tails Success Stories
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Every adoption creates a beautiful story. Here are some of our favorite success stories 
            that show the incredible bond between pets and their new families.
          </p>
        </div>

        {/* Main Story Display */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-8">
          {/* Image Section */}
          <div className="relative">
            <Card variant="elevated" padding="none" className="overflow-hidden">
              <div className="relative aspect-[4/3]">
                {/* Before/After Images */}
                <div className="relative w-full h-full">
                  <Image
                    src={showAfterImage ? currentStoryData.afterImage : currentStoryData.beforeImage}
                    alt={`${currentStoryData.petName} ${showAfterImage ? 'after' : 'before'} adoption`}
                    fill
                    className="object-cover transition-opacity duration-500"
                    onError={(e) => {
                      // Fallback to gradient background if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                  
                  {/* Fallback gradient background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-200 to-green-200 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-6xl mb-2">
                        {currentStoryData.petType === 'dog' ? '🐕' : 
                         currentStoryData.petType === 'cat' ? '🐱' : '🐾'}
                      </div>
                      <p className="text-neutral-600 font-medium">{currentStoryData.petName}</p>
                    </div>
                  </div>
                </div>

                {/* Before/After Toggle */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="flex gap-2">
                    <Button
                      variant={!showAfterImage ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setShowAfterImage(false)}
                      className="flex-1"
                    >
                      Before
                    </Button>
                    <Button
                      variant={showAfterImage ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setShowAfterImage(true)}
                      className="flex-1"
                    >
                      After
                    </Button>
                  </div>
                </div>

                {/* Pet Type Badge */}
                <div className="absolute top-4 left-4">
                  <Badge variant="primary" className="capitalize">
                    {currentStoryData.petType}
                  </Badge>
                </div>
              </div>
            </Card>
          </div>

          {/* Story Content */}
          <div className="space-y-6">
            <div>
              <h3 className="font-primary text-3xl font-bold text-neutral-900 mb-2">
                {currentStoryData.petName}'s Journey
              </h3>
              <div className="flex flex-wrap gap-4 text-sm text-neutral-600 mb-4">
                <div className="flex items-center gap-1">
                  <Heart className="w-4 h-4 text-red-500" />
                  <span>Adopted by {currentStoryData.adopterName}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4 text-orange-500" />
                  <span>{currentStoryData.adopterLocation}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4 text-green-500" />
                  <span>{new Date(currentStoryData.adoptionDate).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            <p className="text-neutral-700 leading-relaxed text-lg">
              {currentStoryData.story}
            </p>

            <div className="flex gap-4">
              <Button variant="primary" size="lg">
                Share This Story
              </Button>
              <Button variant="outline" size="lg">
                View More Stories
              </Button>
            </div>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-center gap-6">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPrevious}
            leftIcon={<ChevronLeft className="w-4 h-4" />}
          >
            Previous
          </Button>

          {/* Story Indicators */}
          <div className="flex gap-2">
            {stories.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentStory(index);
                  setShowAfterImage(false);
                }}
                className={cn(
                  'w-3 h-3 rounded-full transition-all duration-300',
                  index === currentStory
                    ? 'bg-orange-500 scale-125'
                    : 'bg-neutral-300 hover:bg-neutral-400'
                )}
              />
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={goToNext}
            rightIcon={<ChevronRight className="w-4 h-4" />}
          >
            Next
          </Button>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Card variant="warm" className="max-w-2xl mx-auto">
            <CardContent className="text-center py-8">
              <h3 className="font-primary text-2xl font-semibold text-neutral-900 mb-4">
                Ready to Create Your Own Success Story?
              </h3>
              <p className="text-neutral-600 mb-6">
                Browse our available pets and find your perfect companion today.
              </p>
              <Button variant="primary" size="lg">
                Start Your Adoption Journey
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export { SuccessStories };
export type { SuccessStory, SuccessStoriesProps };
