const mongoose = require('mongoose');

const applicationSchema = new mongoose.Schema({
  applicant: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  pet: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Pet',
    required: true
  },
  type: {
    type: String,
    enum: ['adoption', 'foster', 'sponsor'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'under-review', 'approved', 'rejected', 'withdrawn'],
    default: 'pending'
  },
  personalInfo: {
    occupation: {
      type: String,
      required: true
    },
    employer: String,
    workSchedule: String,
    income: {
      range: {
        type: String,
        enum: ['under-25k', '25k-50k', '50k-75k', '75k-100k', 'over-100k']
      },
      stable: {
        type: Boolean,
        required: true
      }
    }
  },
  livingSituation: {
    type: {
      type: String,
      enum: ['house', 'apartment', 'condo', 'other'],
      required: true
    },
    ownership: {
      type: String,
      enum: ['own', 'rent', 'family'],
      required: true
    },
    landlordPermission: {
      type: Boolean,
      required: function() {
        return this.livingSituation.ownership === 'rent';
      }
    },
    yardSize: {
      type: String,
      enum: ['none', 'small', 'medium', 'large']
    },
    fenced: {
      type: Boolean,
      default: false
    },
    householdMembers: [{
      name: String,
      age: Number,
      relationship: String,
      allergies: Boolean
    }]
  },
  petExperience: {
    previousPets: [{
      type: String,
      breed: String,
      yearsOwned: Number,
      currentStatus: {
        type: String,
        enum: ['still-have', 'passed-away', 'rehomed', 'lost']
      },
      reason: String
    }],
    currentPets: [{
      type: String,
      breed: String,
      age: Number,
      spayedNeutered: Boolean,
      vaccinated: Boolean
    }],
    veterinarian: {
      name: String,
      clinic: String,
      phone: String,
      yearsWithVet: Number
    }
  },
  careCommitment: {
    dailyExercise: {
      type: String,
      enum: ['minimal', 'moderate', 'high'],
      required: true
    },
    timeAvailable: {
      type: String,
      enum: ['few-hours', 'half-day', 'most-day', 'all-day'],
      required: true
    },
    trainingCommitment: {
      type: Boolean,
      required: true
    },
    emergencyFunds: {
      type: Boolean,
      required: true
    },
    longTermCommitment: {
      type: Boolean,
      required: true
    }
  },
  references: [{
    name: {
      type: String,
      required: true
    },
    relationship: {
      type: String,
      required: true
    },
    phone: {
      type: String,
      required: true
    },
    email: String,
    contacted: {
      type: Boolean,
      default: false
    },
    feedback: String
  }],
  homeVisit: {
    requested: {
      type: Boolean,
      default: false
    },
    scheduled: Date,
    completed: {
      type: Boolean,
      default: false
    },
    notes: String,
    approved: Boolean
  },
  meetAndGreet: {
    scheduled: Date,
    completed: {
      type: Boolean,
      default: false
    },
    notes: String,
    petReaction: String,
    applicantReaction: String
  },
  additionalNotes: String,
  reviewedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  reviewDate: Date,
  reviewNotes: String,
  approvalDate: Date,
  rejectionReason: String,
  followUpRequired: {
    type: Boolean,
    default: false
  },
  followUpDate: Date,
  documents: [{
    type: {
      type: String,
      enum: ['id-proof', 'address-proof', 'income-proof', 'landlord-permission', 'vet-records', 'other']
    },
    url: String,
    verified: {
      type: Boolean,
      default: false
    }
  }]
}, {
  timestamps: true
});

// Indexes
applicationSchema.index({ applicant: 1, status: 1 });
applicationSchema.index({ pet: 1, status: 1 });
applicationSchema.index({ status: 1, createdAt: -1 });
applicationSchema.index({ type: 1, status: 1 });

module.exports = mongoose.model('Application', applicationSchema);
