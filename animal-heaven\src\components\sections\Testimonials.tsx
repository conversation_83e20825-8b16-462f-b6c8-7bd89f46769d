'use client';

import React from 'react';
import Image from 'next/image';
import { Star, Quote } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

interface Testimonial {
  id: string;
  name: string;
  location: string;
  petName: string;
  petType: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  rating: number;
  testimonial: string;
  avatar: string;
  adoptionDate: string;
  verified: boolean;
}

interface TestimonialsProps {
  testimonials?: Testimonial[];
}

const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    location: 'Mumbai, Maharashtra',
    petName: 'Rocky',
    petType: 'dog',
    rating: 5,
    testimonial: 'The adoption process was seamless and the staff was incredibly supportive. <PERSON> has brought so much joy to our family. The follow-up care and guidance we received was exceptional.',
    avatar: '/images/testimonials/priya.jpg',
    adoptionDate: '2024-01-15',
    verified: true
  },
  {
    id: '2',
    name: '<PERSON><PERSON>',
    location: 'Delhi, Delhi',
    petName: 'Whiskers',
    petType: 'cat',
    rating: 5,
    testimonial: 'Animal Heaven made our dream of pet parenthood come true. Whiskers was exactly as described, and the team helped us prepare our home perfectly. Highly recommended!',
    avatar: '/images/testimonials/rajesh.jpg',
    adoptionDate: '2024-02-20',
    verified: true
  },
  {
    id: '3',
    name: 'Anita Patel',
    location: 'Bangalore, Karnataka',
    petName: 'Buddy',
    petType: 'dog',
    rating: 5,
    testimonial: 'The care and love shown by the Animal Heaven team is remarkable. They matched us with Buddy perfectly, and the ongoing support has been invaluable for first-time pet owners.',
    avatar: '/images/testimonials/anita.jpg',
    adoptionDate: '2024-03-10',
    verified: true
  },
  {
    id: '4',
    name: 'Vikram Singh',
    location: 'Chennai, Tamil Nadu',
    petName: 'Luna',
    petType: 'cat',
    rating: 5,
    testimonial: 'Professional, caring, and thorough. The adoption process was transparent and they ensured Luna was the right fit for our lifestyle. She\'s now the queen of our house!',
    avatar: '/images/testimonials/vikram.jpg',
    adoptionDate: '2024-04-05',
    verified: true
  },
  {
    id: '5',
    name: 'Meera Reddy',
    location: 'Hyderabad, Telangana',
    petName: 'Charlie',
    petType: 'dog',
    rating: 5,
    testimonial: 'Animal Heaven doesn\'t just find homes for pets, they create families. Charlie has transformed our lives, and the support we received made the transition smooth and joyful.',
    avatar: '/images/testimonials/meera.jpg',
    adoptionDate: '2024-04-20',
    verified: true
  },
  {
    id: '6',
    name: 'Arjun Gupta',
    location: 'Pune, Maharashtra',
    petName: 'Milo',
    petType: 'dog',
    rating: 5,
    testimonial: 'The team\'s dedication to animal welfare is inspiring. They took time to understand our needs and matched us with Milo perfectly. The post-adoption support is outstanding.',
    avatar: '/images/testimonials/arjun.jpg',
    adoptionDate: '2024-05-15',
    verified: true
  }
];

const Testimonials: React.FC<TestimonialsProps> = ({
  testimonials = defaultTestimonials
}) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`w-4 h-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-neutral-300'
        }`}
      />
    ));
  };

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="font-primary text-4xl font-bold text-neutral-900 mb-4">
            What Our Families Say
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Don't just take our word for it. Here's what our adopting families have to say 
            about their experience with Animal Heaven and their new furry family members.
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} variant="elevated" className="h-full">
              <CardContent className="p-6 flex flex-col h-full">
                {/* Quote Icon */}
                <div className="mb-4">
                  <Quote className="w-8 h-8 text-orange-400" />
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {renderStars(testimonial.rating)}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-neutral-700 leading-relaxed mb-6 flex-grow">
                  "{testimonial.testimonial}"
                </blockquote>

                {/* Author Info */}
                <div className="flex items-center gap-3">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden bg-gradient-to-br from-orange-200 to-green-200 flex items-center justify-center">
                    <Image
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      width={48}
                      height={48}
                      className="object-cover"
                      onError={(e) => {
                        // Fallback to initials if image fails to load
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    {/* Fallback initials */}
                    <span className="text-neutral-600 font-semibold text-sm">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-neutral-900">
                        {testimonial.name}
                      </h4>
                      {testimonial.verified && (
                        <Badge variant="success" size="sm">
                          ✓ Verified
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-neutral-600">
                      {testimonial.location}
                    </p>
                    <p className="text-xs text-neutral-500">
                      Adopted {testimonial.petName} • {new Date(testimonial.adoptionDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Statistics Section */}
        <div className="bg-gradient-to-r from-orange-500 to-green-500 rounded-2xl p-8 text-white">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">2,500+</div>
              <div className="text-orange-100">Happy Adoptions</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">4.9/5</div>
              <div className="text-orange-100">Average Rating</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">98%</div>
              <div className="text-orange-100">Success Rate</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">150+</div>
              <div className="text-orange-100">Active Volunteers</div>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <h3 className="font-primary text-2xl font-semibold text-neutral-900 mb-4">
            Ready to Join Our Happy Families?
          </h3>
          <p className="text-neutral-600 mb-6 max-w-xl mx-auto">
            Start your adoption journey today and create your own success story with a loving pet.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              Browse Available Pets
            </button>
            <button className="border border-orange-500 text-orange-600 hover:bg-orange-50 px-6 py-3 rounded-lg font-medium transition-colors">
              Read More Reviews
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export { Testimonials };
export type { Testimonial, TestimonialsProps };
