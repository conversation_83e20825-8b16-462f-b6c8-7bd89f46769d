'use client';

import React, { useState } from 'react';
import { Sun, Moon, Monitor, ChevronDown } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { Button } from './Button';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  variant = 'button', 
  size = 'md',
  className 
}) => {
  const { theme, actualTheme, setTheme, toggleTheme } = useTheme();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const themes = [
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon },
    { value: 'system', label: 'System', icon: Monitor }
  ] as const;

  const currentThemeData = themes.find(t => t.value === theme) || themes[0];
  const CurrentIcon = currentThemeData.icon;

  if (variant === 'button') {
    return (
      <Button
        variant="ghost"
        size={size}
        onClick={toggleTheme}
        className={cn(
          'relative transition-all duration-200',
          className
        )}
        aria-label={`Switch to ${actualTheme === 'light' ? 'dark' : 'light'} mode`}
      >
        <Sun className={cn(
          'w-4 h-4 transition-all duration-300',
          actualTheme === 'dark' ? 'rotate-90 scale-0' : 'rotate-0 scale-100'
        )} />
        <Moon className={cn(
          'absolute w-4 h-4 transition-all duration-300',
          actualTheme === 'dark' ? 'rotate-0 scale-100' : '-rotate-90 scale-0'
        )} />
      </Button>
    );
  }

  return (
    <div className="relative">
      <Button
        variant="ghost"
        size={size}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className={cn(
          'flex items-center gap-2',
          className
        )}
        aria-label="Theme selector"
        aria-expanded={isDropdownOpen}
      >
        <CurrentIcon className="w-4 h-4" />
        <span className="hidden sm:inline">{currentThemeData.label}</span>
        <ChevronDown className={cn(
          'w-3 h-3 transition-transform duration-200',
          isDropdownOpen && 'rotate-180'
        )} />
      </Button>

      {isDropdownOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsDropdownOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute right-0 top-full mt-2 z-20 min-w-[140px] bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg py-1">
            {themes.map((themeOption) => {
              const Icon = themeOption.icon;
              const isSelected = theme === themeOption.value;
              
              return (
                <button
                  key={themeOption.value}
                  onClick={() => {
                    setTheme(themeOption.value);
                    setIsDropdownOpen(false);
                  }}
                  className={cn(
                    'w-full flex items-center gap-3 px-3 py-2 text-sm transition-colors',
                    'hover:bg-neutral-100 dark:hover:bg-neutral-700',
                    isSelected && 'bg-orange-50 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400'
                  )}
                >
                  <Icon className="w-4 h-4" />
                  <span>{themeOption.label}</span>
                  {isSelected && (
                    <div className="ml-auto w-2 h-2 bg-orange-500 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
};

export { ThemeToggle };
