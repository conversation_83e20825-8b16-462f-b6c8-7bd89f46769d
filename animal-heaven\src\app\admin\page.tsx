'use client';

import React, { useState } from 'react';
import { 
  Bar<PERSON>hart3, 
  <PERSON>, 
  Heart, 
  FileText, 
  Calendar, 
  Settings,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { 
  <PERSON><PERSON>, 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  Badge,
  Input,
  Navigation,
  Footer
} from '@/components';
import { useAuth } from '@/contexts/AuthContext';

interface AdminStats {
  totalPets: number;
  availablePets: number;
  adoptedThisMonth: number;
  pendingApplications: number;
  totalVolunteers: number;
  activeVolunteers: number;
  totalDonations: number;
  monthlyDonations: number;
}

interface PetManagement {
  id: string;
  name: string;
  type: 'dog' | 'cat' | 'bird' | 'rabbit' | 'other';
  breed: string;
  age: string;
  status: 'available' | 'adopted' | 'pending' | 'medical';
  dateAdded: string;
  applications: number;
  views: number;
}

interface ApplicationManagement {
  id: string;
  applicantName: string;
  petName: string;
  status: 'pending' | 'approved' | 'rejected' | 'interview';
  submittedDate: string;
  priority: 'high' | 'medium' | 'low';
}

const adminStats: AdminStats = {
  totalPets: 156,
  availablePets: 89,
  adoptedThisMonth: 23,
  pendingApplications: 47,
  totalVolunteers: 152,
  activeVolunteers: 89,
  totalDonations: 125000,
  monthlyDonations: 15000
};

const samplePets: PetManagement[] = [
  {
    id: '1',
    name: 'Buddy',
    type: 'dog',
    breed: 'Golden Retriever',
    age: '2 years',
    status: 'available',
    dateAdded: '2024-01-15',
    applications: 5,
    views: 234
  },
  {
    id: '2',
    name: 'Luna',
    type: 'cat',
    breed: 'Persian',
    age: '1 year',
    status: 'pending',
    dateAdded: '2024-01-18',
    applications: 3,
    views: 156
  },
  {
    id: '3',
    name: 'Charlie',
    type: 'dog',
    breed: 'Labrador Mix',
    age: '3 years',
    status: 'adopted',
    dateAdded: '2024-01-10',
    applications: 8,
    views: 345
  }
];

const sampleApplications: ApplicationManagement[] = [
  {
    id: '1',
    applicantName: 'John Smith',
    petName: 'Buddy',
    status: 'pending',
    submittedDate: '2024-01-20',
    priority: 'high'
  },
  {
    id: '2',
    applicantName: 'Sarah Johnson',
    petName: 'Luna',
    status: 'interview',
    submittedDate: '2024-01-19',
    priority: 'medium'
  },
  {
    id: '3',
    applicantName: 'Mike Chen',
    petName: 'Charlie',
    status: 'approved',
    submittedDate: '2024-01-18',
    priority: 'low'
  }
];

const AdminPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [searchQuery, setSearchQuery] = useState('');

  // Check if user has admin access
  if (!user || (user.role !== 'admin' && user.role !== 'staff')) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50">
        <Navigation isAuthenticated={true} savedPetsCount={0} />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-neutral-900 mb-4">Access Denied</h1>
          <p className="text-neutral-600 mb-6">You don't have permission to access the admin panel.</p>
          <Button variant="primary" onClick={() => window.location.href = '/dashboard'}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-600 bg-green-100';
      case 'adopted': return 'text-blue-600 bg-blue-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'medical': return 'text-red-600 bg-red-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'interview': return 'text-purple-600 bg-purple-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-neutral-600 bg-neutral-100';
    }
  };

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'pets', label: 'Pet Management', icon: Heart },
    { id: 'applications', label: 'Applications', icon: FileText },
    { id: 'volunteers', label: 'Volunteers', icon: Users },
    { id: 'content', label: 'Content', icon: Edit },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation isAuthenticated={true} savedPetsCount={0} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="font-primary text-3xl font-bold text-neutral-900">
                Admin Panel
              </h1>
              <p className="text-neutral-600 mt-1">
                Manage pets, applications, and shelter operations
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" leftIcon={<Download className="w-4 h-4" />}>
                Export Data
              </Button>
              <Button variant="primary" leftIcon={<Plus className="w-4 h-4" />}>
                Add New Pet
              </Button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-neutral-200">
            <nav className="-mb-px flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card variant="warm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-neutral-600">Total Pets</p>
                      <p className="text-2xl font-bold text-neutral-900">{adminStats.totalPets}</p>
                      <p className="text-xs text-green-600">
                        {adminStats.availablePets} available
                      </p>
                    </div>
                    <Heart className="w-8 h-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card variant="elevated">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-neutral-600">Adoptions This Month</p>
                      <p className="text-2xl font-bold text-neutral-900">{adminStats.adoptedThisMonth}</p>
                      <p className="text-xs text-blue-600">+15% from last month</p>
                    </div>
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card variant="outlined">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-neutral-600">Pending Applications</p>
                      <p className="text-2xl font-bold text-neutral-900">{adminStats.pendingApplications}</p>
                      <p className="text-xs text-yellow-600">Needs attention</p>
                    </div>
                    <Clock className="w-8 h-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>

              <Card variant="outlined">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-neutral-600">Active Volunteers</p>
                      <p className="text-2xl font-bold text-neutral-900">{adminStats.activeVolunteers}</p>
                      <p className="text-xs text-purple-600">
                        of {adminStats.totalVolunteers} total
                      </p>
                    </div>
                    <Users className="w-8 h-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card variant="elevated">
                <CardHeader>
                  <CardTitle>Recent Applications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {sampleApplications.slice(0, 3).map((application) => (
                      <div key={application.id} className="flex items-center justify-between p-3 border border-neutral-200 rounded-lg">
                        <div>
                          <h4 className="font-medium">{application.applicantName}</h4>
                          <p className="text-sm text-neutral-600">Applied for {application.petName}</p>
                        </div>
                        <Badge className={getStatusColor(application.status)} size="sm">
                          {application.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card variant="warm">
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <Button variant="primary" className="w-full justify-start" leftIcon={<Plus className="w-4 h-4" />}>
                      Add New Pet
                    </Button>
                    <Button variant="outline" className="w-full justify-start" leftIcon={<FileText className="w-4 h-4" />}>
                      Review Applications
                    </Button>
                    <Button variant="outline" className="w-full justify-start" leftIcon={<Calendar className="w-4 h-4" />}>
                      Schedule Appointments
                    </Button>
                    <Button variant="outline" className="w-full justify-start" leftIcon={<Upload className="w-4 h-4" />}>
                      Import Pet Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Pet Management Tab */}
        {activeTab === 'pets' && (
          <div className="space-y-6">
            <Card variant="outlined" className="text-center py-12">
              <CardContent>
                <Heart className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="font-semibold text-neutral-700 mb-2">Pet Management</h3>
                <p className="text-neutral-500 mb-4">
                  Manage pet inventory, add new pets, and track adoption status.
                </p>
                <Button variant="outline">Coming Soon</Button>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Applications Tab */}
        {activeTab === 'applications' && (
          <div className="space-y-6">
            <Card variant="outlined" className="text-center py-12">
              <CardContent>
                <FileText className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                <h3 className="font-semibold text-neutral-700 mb-2">Application Management</h3>
                <p className="text-neutral-500 mb-4">
                  Review and process adoption applications from potential pet parents.
                </p>
                <Button variant="outline">Coming Soon</Button>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Other tabs placeholder */}
        {(activeTab === 'volunteers' || activeTab === 'content' || activeTab === 'settings') && (
          <Card variant="outlined" className="text-center py-12">
            <CardContent>
              <Settings className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
              <h3 className="font-semibold text-neutral-700 mb-2">
                {tabs.find(tab => tab.id === activeTab)?.label} Panel
              </h3>
              <p className="text-neutral-500 mb-4">
                This section is under development and will be available soon.
              </p>
              <Button variant="outline">Coming Soon</Button>
            </CardContent>
          </Card>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default AdminPage;
